## 继承centos6-jdk1.8.151基础镜像,这个可根据需求调整.当前我们最好统一版本,方便维护!如果有特殊要求可变更
## 假设需要centos7,那么改为:
## FROM registry-vpc.cn-hangzhou.aliyuncs.com/yifangyun-library/centos7-jdk8:1.8.151
## registry-vpc.cn-hangzhou.aliyuncs.com为阿里云镜像仓库vpc下的地址
FROM registry.cn-hangzhou.aliyuncs.com/yifangyun-library/aspose-convertor:v2.0.2 as builder

FROM registry.cn-hangzhou.aliyuncs.com/yifangyun-library/centos7-transformer:v1.9

COPY --from=builder /usr/local/services/aspose-convertor/ /usr/local/services/aspose-convertor/

## Docker镜像构建时,会将代码打包到镜像中.镜像版本和代码的tag保持一致!
## 所以会将maven打包后的代码目录,拷贝到工程运目录下
COPY ./target/transformer/ /usr/local/services/transformer/

## 服务暴露端口，如果服务没有暴露则不用填写
#EXPOSE 443
EXPOSE 8080
