<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
  <modelVersion>4.0.0</modelVersion>

  <parent>
    <groupId>com.fangcloud.service</groupId>
    <artifactId>service-parent</artifactId>
    <version>1.2.0</version>
  </parent>

  <artifactId>services-transformer</artifactId>
  <packaging>jar</packaging>
  <name>services-transformer</name>
  <version>2.3.1</version>

  <properties>
    <service.baseDirectory>${project.build.directory}/transformer</service.baseDirectory>
    <itextpdf.version>7.1.13</itextpdf.version>
    <egeio-services-core.version>2.1.2-SNAPSHOT</egeio-services-core.version>
    <aspose-convertor.version>2.0.2</aspose-convertor.version>
    <bifrost.version>3.2.1</bifrost.version>
    <commons-exec.version>1.2</commons-exec.version>
    <commons-pool2.version>2.2</commons-pool2.version>
    <com-sun-pdfview.version>1.0.5-201003191900</com-sun-pdfview.version>
    <metadata-extractor.version>2.15.0</metadata-extractor.version>
    <bimface-java-sdk.version>3.3.4</bimface-java-sdk.version>
  </properties>

  <profiles>
    <profile>
      <id>dev</id>
      <properties>
        <environment>dev</environment>
      </properties>
      <activation>
        <activeByDefault>true</activeByDefault>
      </activation>
    </profile>
    <profile>
      <id>test</id>
      <properties>
        <environment>test</environment>
      </properties>
    </profile>
    <profile>
      <id>prod</id>
      <properties>
        <environment>prod</environment>
      </properties>
    </profile>
    <profile>
      <id>profession</id>
      <properties>
        <environment>profession</environment>
      </properties>
    </profile>

  </profiles>

  <build>
    <filters>
      <filter>build/${environment}.properties</filter>
    </filters>
    <plugins>
      <!-- copy resources -->
      <plugin>
        <artifactId>maven-resources-plugin</artifactId>
        <version>2.6</version>
        <executions>
          <execution>
            <id>copy-conf</id>
            <phase>package</phase>
            <goals>
              <goal>copy-resources</goal>
            </goals>
            <configuration>
              <outputDirectory>${service.baseDirectory}/conf</outputDirectory>
              <resources>
                <resource>
                  <directory>${basedir}/src/main/conf</directory>
                    <filtering>true</filtering>
                </resource>
              </resources>
            </configuration>
          </execution>
          <execution>
            <id>copy-scripts</id>
            <phase>package</phase>
            <goals>
              <goal>copy-resources</goal>
            </goals>
            <configuration>
              <outputDirectory>${service.baseDirectory}/scripts</outputDirectory>
              <resources>
                <resource>
                  <directory>${basedir}/src/main/scripts</directory>
                </resource>
              </resources>
            </configuration>
          </execution>
          <execution>
            <id>copy-lib</id>
            <phase>package</phase>
            <goals>
              <goal>copy-resources</goal>
            </goals>
            <configuration>
              <outputDirectory>${service.baseDirectory}/lib</outputDirectory>
              <resources>
                <resource>
                  <directory>${basedir}/src/main/lib</directory>
                </resource>
              </resources>
            </configuration>
          </execution>
          <execution>
            <id>copy-other</id>
            <phase>package</phase>
            <goals>
              <goal>copy-resources</goal>
            </goals>
            <configuration>
              <outputDirectory>${service.baseDirectory}/</outputDirectory>
              <resources>
                <resource>
                  <directory>${basedir}/src/main/other</directory>
                </resource>
              </resources>
            </configuration>
          </execution>
        </executions>
      </plugin>

      <!-- copy dependency -->
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-dependency-plugin</artifactId>
        <executions>
          <execution>
            <id>copy-dependencies</id>
            <phase>prepare-package</phase>
            <goals>
              <goal>copy-dependencies</goal>
            </goals>
            <configuration>
              <outputDirectory>${service.baseDirectory}/lib</outputDirectory>
              <overWriteReleases>false</overWriteReleases>
              <overWriteSnapshots>true</overWriteSnapshots>
              <overWriteIfNewer>true</overWriteIfNewer>
            </configuration>
          </execution>
        </executions>
      </plugin>

      <!-- package jar -->
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-jar-plugin</artifactId>
        <version>2.4</version>
        <configuration>
          <finalName>${project.artifactId}-${project.version}</finalName>
          <outputDirectory>${service.baseDirectory}</outputDirectory>
          <archive>
            <manifest>
              <addClasspath>true</addClasspath>
              <useUniqueVersions>false</useUniqueVersions>
              <classpathPrefix>lib/</classpathPrefix>
              <mainClass>com.egeio.services.transformer.TransformerService</mainClass>
            </manifest>
            <manifestEntries>
              <Class-Path>conf/ scripts/</Class-Path>
            </manifestEntries>
          </archive>
        </configuration>
      </plugin>

      <!-- package tar -->
      <plugin>
        <artifactId>maven-assembly-plugin</artifactId>
        <configuration>
          <finalName>${project.artifactId}-${project.version}</finalName>
          <!-- not append assembly id in release file name -->
          <appendAssemblyId>false</appendAssemblyId>
          <descriptors>
            <descriptor>assembly.xml</descriptor>
          </descriptors>
        </configuration>
        <executions>
          <execution>
            <id>make-assembly</id>
            <phase>package</phase>
            <goals>
              <goal>single</goal>
            </goals>
          </execution>
        </executions>
      </plugin>

      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-compiler-plugin</artifactId>
      </plugin>
    </plugins>
  </build>

  <dependencies>
      <dependency>
          <groupId>org.apache.zookeeper</groupId>
          <artifactId>zookeeper</artifactId>
          <version>3.4.13</version>
      </dependency>
      <dependency>
      <groupId>com.egeio.services</groupId>
      <artifactId>core-akka</artifactId>
      <version>${egeio-services-core.version}</version>
      <exclusions>
        <exclusion>
          <artifactId>zookeeper</artifactId>
          <groupId>org.apache.zookeeper</groupId>
        </exclusion>
      </exclusions>
    </dependency>
    <dependency>
      <groupId>com.egeio.services</groupId>
      <artifactId>core-tika</artifactId>
      <version>${egeio-services-core.version}</version>
    </dependency>
    <dependency>
      <groupId>com.fangcloud.service</groupId>
      <artifactId>bifrost-biz</artifactId>
      <version>${bifrost.version}</version>
      <exclusions>
        <exclusion>
          <groupId>com.egeio.services</groupId>
          <artifactId>core-auth</artifactId>
        </exclusion>
      </exclusions>
    </dependency>
    <dependency>
      <groupId>com.egeio.aspose</groupId>
      <artifactId>aspose-convertor</artifactId>
      <version>${aspose-convertor.version}</version>
      <exclusions>
        <exclusion>
          <groupId>com.egeio.services</groupId>
          <artifactId>core-utils</artifactId>
        </exclusion>
        <exclusion>
          <groupId>com.egeio.services</groupId>
          <artifactId>core-tika</artifactId>
        </exclusion>
      </exclusions>
    </dependency>
    <dependency>
      <groupId>org.apache.commons</groupId>
      <artifactId>commons-exec</artifactId>
      <version>${commons-exec.version}</version>
    </dependency>
    <dependency>
      <groupId>org.apache.commons</groupId>
      <artifactId>commons-pool2</artifactId>
      <version>${commons-pool2.version}</version>
    </dependency>
    <dependency>
      <groupId>com.kenai.nbpwr</groupId>
      <artifactId>com-sun-pdfview</artifactId>
      <version>${com-sun-pdfview.version}</version>
    </dependency>
    <dependency>
      <groupId>com.drewnoakes</groupId>
      <artifactId>metadata-extractor</artifactId>
      <version>${metadata-extractor.version}</version>
    </dependency>
    <dependency>
      <groupId>com.itextpdf</groupId>
      <artifactId>barcodes</artifactId>
      <version>${itextpdf.version}</version>
      <!-- barcodes depends on kernel -->
    </dependency>
    <dependency>
      <groupId>com.itextpdf</groupId>
      <artifactId>font-asian</artifactId>
      <version>${itextpdf.version}</version>
    </dependency>
    <dependency>
      <groupId>com.itextpdf</groupId>
      <artifactId>forms</artifactId>
      <version>${itextpdf.version}</version>
      <!-- forms depends on kernel and layout -->
    </dependency>
    <dependency>
      <groupId>com.itextpdf</groupId>
      <artifactId>hyph</artifactId>
      <version>${itextpdf.version}</version>
    </dependency>
    <dependency>
      <groupId>com.itextpdf</groupId>
      <artifactId>io</artifactId>
      <version>${itextpdf.version}</version>
    </dependency>
    <dependency>
      <groupId>com.itextpdf</groupId>
      <artifactId>kernel</artifactId>
      <version>${itextpdf.version}</version>
      <!-- kernel depends on io -->
    </dependency>
    <dependency>
      <groupId>com.itextpdf</groupId>
      <artifactId>layout</artifactId>
      <version>${itextpdf.version}</version>
      <!-- layout depends on kernel -->
    </dependency>
    <dependency>
      <groupId>com.itextpdf</groupId>
      <artifactId>pdfa</artifactId>
      <version>${itextpdf.version}</version>
      <!-- pdfa depends on kernel -->
    </dependency>
    <dependency>
      <groupId>com.itextpdf</groupId>
      <artifactId>sign</artifactId>
      <version>${itextpdf.version}</version>
      <!-- sign depends on kernel, layout and forms -->
    </dependency>
    <dependency>
      <groupId>com.bimface</groupId>
      <artifactId>bimface-java-sdk</artifactId>
      <version>${bimface-java-sdk.version}</version>
      <classifier>all</classifier>
      <exclusions>
        <exclusion>
          <groupId>com.bimface</groupId>
          <artifactId>piping-client</artifactId>
        </exclusion>
        <exclusion>
          <groupId>com.glodon.paas.foundation</groupId>
          <artifactId>restclient</artifactId>
        </exclusion>
      </exclusions>
    </dependency>
    <dependency>
      <groupId>com.auth0</groupId>
      <artifactId>java-jwt</artifactId>
      <version>3.4.0</version>
    </dependency>
    <dependency>
      <groupId>org.jsoup</groupId>
      <artifactId>jsoup</artifactId>
      <version>1.11.3</version>
    </dependency>
    <dependency>
      <groupId>com.googlecode.juniversalchardet</groupId>
      <artifactId>juniversalchardet</artifactId>
      <version>1.0.3</version>
    </dependency>
    <dependency>
      <groupId>org.springframework.data</groupId>
      <artifactId>spring-data-redis</artifactId>
      <version>1.6.4.RELEASE</version>
    </dependency>

    <dependency>
      <groupId>redis.clients</groupId>
      <artifactId>jedis</artifactId>
      <version>2.7.3</version>
      <type>jar</type>
    </dependency>


    <!-- https://mvnrepository.com/artifact/org.projectlombok/lombok -->
    <dependency>
      <groupId>org.projectlombok</groupId>
      <artifactId>lombok</artifactId>
      <version>1.18.20</version>
      <scope>provided</scope>
    </dependency>
  </dependencies>

  <repositories>
    <repository>
      <id>s7-repo</id>
      <name>s7-qbuild</name>
      <url>http://s7.qbuild.corp.qihoo.net:9081/nexus/repository/maven-public/</url>
    </repository>
  </repositories>

  <distributionManagement>
    <repository>
      <id>releases</id>
      <name>Nexus Release Repository</name>
      <url>http://s7.qbuild.corp.qihoo.net:9081/nexus/repository/maven-releases/</url>
    </repository>
    <snapshotRepository>
      <id>snapshots</id>
      <name>Nexus Snapshot Repository</name>
      <url>http://s7.qbuild.corp.qihoo.net:9081/nexus/repository/maven-snapshots/</url>
    </snapshotRepository>
  </distributionManagement>

</project>

