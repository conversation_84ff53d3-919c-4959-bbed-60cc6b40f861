<?xml version="1.0" encoding="UTF-8"?>
<!--
 - Copyright 1999-2011 Alibaba Group.
 -
 - Licensed under the Apache License, Version 2.0 (the "License");
 - you may not use this manager except in compliance with the License.
 - You may obtain a copy of the License at
 -
 -      http://www.apache.org/licenses/LICENSE-2.0
 -
 - Unless required by applicable law or agreed to in writing, software
 - distributed under the License is distributed on an "AS IS" BASIS,
 - WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 - See the License for the specific language governing permissions and
 - limitations under the License.
-->
<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xmlns:context="http://www.springframework.org/schema/context" xmlns:task="http://www.springframework.org/schema/task"
       xsi:schemaLocation="http://www.springframework.org/schema/beans
       http://www.springframework.org/schema/beans/spring-beans-2.5.xsd
       http://www.springframework.org/schema/context
       http://www.springframework.org/schema/context/spring-context.xsd http://www.springframework.org/schema/task http://www.springframework.org/schema/task/spring-task.xsd">

    <context:component-scan base-package="com.egeio.services.transformer, com.egeio.core.auth, com.egeio.services.bifrost"/>

    <!-- dubbo config -->
    <import resource="classpath:dubbo-consumer-config.xml"/>

    <bean id="redisConnectionFactory" class="org.springframework.data.redis.connection.jedis.JedisConnectionFactory">
        <property name="port" value="${redis.port}"/>
        <property name="hostName" value="${redis.host}"/>
        <property name="password" value="${redis.password}"/>
    </bean>

    <!-- redis template definition -->
    <bean id="stringRedisTemplate" class="org.springframework.data.redis.core.StringRedisTemplate">
        <property name="connectionFactory" ref="redisConnectionFactory"/>
    </bean>

    <!-- cache name 定为 cacheClient，不要更改 -->
    <!-- 依次配置bean: DoubleWriteRedisAsSlaveCacheClient/DoubleWriteRedisAsMasterCacheClient/RedisCacheClient -->
    <!-- 注意：最后下架memcached时候要改代码，将memcached初始化的代码删除，否则可能会有问题 -->
    <bean id="cacheClient" name="cacheClient" class="com.egeio.services.transformer.cache.RedisCacheClient">
        <constructor-arg ref="stringRedisTemplate"/>
    </bean>

    <bean id="applicationContextHolder" class="com.egeio.services.transformer.ApplicationContextHolder" lazy-init="false"/>


</beans>