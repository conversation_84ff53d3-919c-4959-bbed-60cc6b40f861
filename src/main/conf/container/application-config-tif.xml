<?xml version="1.0" encoding="UTF-8" ?>
<configuration>
  <name>TransformerService</name>
  <self_service_id>${transformer.service.id}</self_service_id>
  <restart_window>3600000</restart_window>
  <retries>1</retries>
  <restart_sleep>200</restart_sleep>
  <timeout>300000</timeout>

  <message_queue host="${mq.host}" port="${mq.port}" pool_size="5"
    timeout="${mq.timeout}" username="${mq.username}" password="${mq.password}" />

  <akka>
    <listeners>
      <listener name="pre-conversion-job-listener" interval="200" 
        jobQueueListener="true" maxPendingCount="4" 
        queue="file_preconversion_tif_queue" failQueue="file_preconversion_tif_fail_queue" 
        agent="conversion-job-dispatcher-agent" jobClass="com.egeio.services.transformer.models.ConversionJob"> 
      </listener>
      <listener name="conversion-job-listener" interval="200" 
        jobQueueListener="true" maxPendingCount="4" 
        queue="file_conversion_tif_queue" failQueue="file_conversion_tif_fail_queue" 
        agent="conversion-job-dispatcher-agent" jobClass="com.egeio.services.transformer.models.ConversionJob"> 
      </listener>
      ${transformer.akka.listener}
    </listeners>

    <agents>
      <agent name="conversion-job-dispatcher-agent"
        class="com.egeio.services.transformer.actors.ConversionJobDispatcherAgent" />
    </agents>
  </akka>
  <monitor>
    <prometheus application="transformer" port="9101" enabled="true"></prometheus>

    <!--  opentsdb  废弃  -->
    <opentsdb ip="${opentsdb.host}" port="4242" enabled="false"></opentsdb>
    <metric>com.egeio.services.transformer</metric>
    <interval>60</interval>
	<http_metric>com.egeio.services.transformer.http</http_metric>
  </monitor>

  <serializes>
    <serialize type="com.egeio.services.transformer.models.ConversionFlag"
      serailizer="com.egeio.services.transformer.models.ConversionFlagSerializer" />
    <serialize type="com.egeio.services.transformer.models.PreviewType"
      serailizer="com.egeio.services.transformer.models.PreviewTypeSerializer" />
    <serialize type="com.egeio.services.transformer.models.JobStatus"
      serailizer="com.egeio.services.transformer.models.JobStatusSerializer" />
    <serialize type="com.egeio.services.transformer.models.PreviewStatus"
      serailizer="com.egeio.services.transformer.models.PreviewStatusSerializer" />
    <serialize
      type="com.egeio.services.transformer.models.OfficeConversionPlatform"
      serailizer="com.egeio.services.transformer.models.OfficeConversionPlatformSerializer" />
    <serialize type="com.egeio.core.web.entity.UploadType"
      serailizer="com.egeio.core.web.entity.UploadTypeSerializer" />
  </serializes>

  <stream>
    <download buffer_size="10240" max_buffer_num="500" timeout="1800000"></download>
  </stream>

  <web_client>
    <max_connection>500</max_connection>
    <max_queue>60000</max_queue>
    <web_root>${v2.api.host}</web_root>
    <web_root_service_id>${v2.service.id}</web_root_service_id>
    <download_timeout>180000</download_timeout>
    <complete_callback>/internal_api/conversion/conversion_complete
    </complete_callback>
    <check_job_url>/internal_api/storages/get_message_info_by_file_storage_id
    </check_job_url>
    <check_preview_url>/internal_api/conversion/get_preview_status
    </check_preview_url>
    <upload_sub_url>/internal_upload</upload_sub_url>
    <download_sub_url>/interndownload</download_sub_url>
    <server_addr_sub_url>/internal_api/storages/get_server_addr
    </server_addr_sub_url>
    <update_status_url>/internal_api/conversion/update_status
    </update_status_url>


    <authentications open_token_expire_second="600"
                     enable_visit_guard="true">
    </authentications>

  </web_client>

  <access_control open_token_expire_second="600"
                  enable_visit_guard="true" enable_validation="true">
  </access_control>

  <jetty_server>
    <port>-1</port>
    <host>0.0.0.0</host>
    <max_queued>3100</max_queued>
    <min_threads>30</min_threads>
    <max_threads>3000</max_threads>
    <idle_time_out>60000</idle_time_out>
    <request_header_size>10240</request_header_size>
    <cross-domain-file>crossdomain.xml</cross-domain-file>
    <options enabled="true">
      <allow_origin allow_all="true">
        <default_origin>${v2.api.host}</default_origin>
        <origin_list_file>origins.conf</origin_list_file>
        <regular_origin>http(s)?://.*\.fangcloud\.com</regular_origin>
      </allow_origin>
      <allow_headers>Range, requesttoken, x-file-name, content-type
      </allow_headers>
      <expose_headers>Content-Range, Accept-Ranges, Content-Length,
        Content-Encoding
      </expose_headers>
      <allow_credentials>true</allow_credentials>
      <max_age>86400</max_age>
    </options>

    <https enabled="false" port="443" host="0.0.0.0"
           cert_location="/usr/local/services/upload/conf/fangcloud.jks"
           password="bigbang!" />
  </jetty_server>

  <publish_queues>
    <publish_queue access="public" type="conversion" queue="file_conversion_queue"/>
    <publish_queue access="public" type="preconversion" queue="file_preconversion_queue"/>
    <publish_queue access="private" type="preconversion" queue="private_file_preconversion_queue"/>
  </publish_queues>

  <memcache enable="true" username="${memcached.username}" password="${memcached.password}" binary="true" password_disable="false">
    <servers>
      <server host="${memcached.host}" port="${memcached.port}" />
    </servers>
    <preview_cache_key_version>v1</preview_cache_key_version>
    <min_threads>8</min_threads>
    <max_threads>2000</max_threads>
    <expires>1800000</expires>
  </memcache>

  <aspose-converter>
    <font_dir>/usr/share/fonts/my_fonts</font_dir>
    <try_aspose_extensions>yxls,xls, xlsx, ods, csv</try_aspose_extensions>
    <try_aspose_when_comments>false</try_aspose_when_comments>
    <mappings>
      <mapping extensions="doc, docx, odt, rtf, wps, docm, dot, dotm, dotx" method="WordToPDF" />
      <mapping extensions="yxls,xls, xlsx, ods, csv, et, xlsb, xlsm" method="ExcelToPDF" />
    </mappings>
  </aspose-converter>

  <empty_pdf>empty.pdf</empty_pdf>

  <tika_file_type_detector>
    <mapping
      type="application/vnd.openxmlformats-officedocument.wordprocessingml.document"
      extensions="docx,doc" />
    <mapping type="application/xml" extensions="doc,docx" />
    <mapping type="application/msword" extensions="doc,wps" />
    <mapping type="application/vnd.ms-works" extensions="wps" />
    <mapping type="application/rtf" extensions="rtf,doc,docx" />
    <mapping type="application/vnd.oasis.opendocument.text"
      extensions="odt" />
    <mapping
      type="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
      extensions="yxls,xlsx,xls" />
    <mapping type="application/vnd.ms-excel" extensions="yxls,xls,et" />
    <mapping type="text/html" extensions="xls,xlsx" />
    <mapping type="application/vnd.oasis.opendocument.spreadsheet"
      extensions="ods" />
    <mapping type="text/tab-separated-values" extensions="tsv" />
    <mapping type="text/csv" extensions="csv" />
    <mapping type="text/plain" extensions="csv,tsv,doc,docx" />
    <mapping type="text/tsv" extensions="tsv" />
    <mapping type="text/html" extensions="doc,docx" />
    <mapping type="message/rfc822" extensions="doc" />
    <mapping
      type="application/vnd.openxmlformats-officedocument.presentationml.presentation"
      extensions="pptx,ppt" />
    <mapping type="application/vnd.ms-powerpoint" extensions="ppt,dps" />
    <mapping type="application/vnd.oasis.opendocument.presentation"
      extensions="odp" />
    <mapping type="application/x-tika-ooxml-protected" extensions="xlsx" />
    <mapping type="application/vnd.ms-project" extensions="mpp" /> 
    <mapping type="application/vnd.visio" extensions="vdw,vsd" /> 
    <mapping type="application/vnd.ms-visio.drawing.macroenabled.12" 
      extensions="vsdm" /> 
    <mapping type="application/vnd.ms-visio.drawing" extensions="vsdx" />
  </tika_file_type_detector>

  <image>
    <max_pixel_size>3000</max_pixel_size>
    <orientation_list>jpg,jpeg,tiff,tif</orientation_list>
    <multi_list>ico,gif,tif,tiff</multi_list>
    <ignore_list>png,jpg,jpeg</ignore_list>
    <force_convert_list>jp2</force_convert_list>
    <cmyk_list>psd,jpg,jpeg,jpf,jp2</cmyk_list>
    <max_accept_size>15000</max_accept_size>
    <max_display_size>8000</max_display_size>
    <background>white</background>
    <cmyk_icc>/usr/local/services/material/USWebCoatedSWOP.icc
    </cmyk_icc>
    <rgb_icc>/usr/local/services/material/sRGB.icc</rgb_icc>
  </image>

  <pdf2image>
    <ppi>120</ppi>
    <size>1024</size>
    <max_pages>100</max_pages>
  </pdf2image>

  <windows-office-converter enabled="true" timeout="200000"> 
    <mapping_list> 
      <mapping extensions="doc, docx, odt, rtf, wps, docm, dot, dotm, dotx" 
        process_name="WINWORD" id="word" queue="word_file_conversion_queue" /> 
      <mapping extensions="ppt, pptx, odp, dps, ppsx, pot, potm, potx, pps, ppsm, pptm" process_name="POWERPNT" 
        id="ppt" queue="ppt_file_conversion_queue" /> 
      <mapping extensions="yxls, xls, xlsx, ods, csv, et, xlsb, xlsm" 
        process_name="EXCEL" id="excel" queue="excel_file_conversion_queue" /> 
      <mapping extensions="vsd,vsdx,vsdm,vdw" process_name="VISIO" 
        id="visio" queue="visio_file_conversion_queue" /> 
      <mapping extensions="mpp" process_name="WINPROJ" id="project" 
        queue="project_file_conversion_queue" /> 
    </mapping_list> 
  </windows-office-converter> 

  <cad>
    <background>black</background>
    <border>20</border>
  </cad>

  <watermark>
    <font_dir>/usr/share/fonts/my_fonts</font_dir>
    <pdf>
      <font>msyh.ttf</font>
      <fontsize>16</fontsize>
      <opacity>0.5</opacity>
      <rotate>30</rotate>
      <repeat x="4" delimiter_x="    " y="10"/>
      <multiplied_leading>1.0</multiplied_leading>
      <gray>0.85</gray>
    </pdf>
    <image>
      <size width="800" height="120"/>
      <font>msyh.ttf</font>
      <pointsize>16</pointsize>
      <border>30</border>
      <dissolve>50</dissolve>
      <rotate>350</rotate>
    </image>
  </watermark>

  <bimface>
    <app_key>${transformer.bimface.app_key}</app_key>
    <app_secret>${transformer.bimface.app_secret}</app_secret>
    <callback>/third_party/bimface/callback</callback>
    <host>${v2.external.host}</host>
  </bimface>

  <conversion>
    <tmp-conversion-dir>/var/tmp/conversion</tmp-conversion-dir>
    <tasks file="tasks.xml" />
    <mapping extensions="tif,tiff" type="image">
      <task id="image_thumbnail" />
      <task id="image_1024_stack" />
      <task id="image_2048_stack" />
      <task id="image_water_256" />
    </mapping>
  </conversion>
  <preview_url isInContainer="${transformer.preview.isInContainer}" />
</configuration>
