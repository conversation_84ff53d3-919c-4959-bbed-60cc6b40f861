<?xml version="1.0" encoding="UTF-8" ?>
<configuration>
  <name>TransformerService</name>
  <self_service_id>${transformer.service.id}</self_service_id>
  <restart_window>3600000</restart_window>
  <retries>1</retries>
  <restart_sleep>200</restart_sleep>
  <timeout>300000</timeout>

  <message_queue host="${mq.host}" port="${mq.port}" pool_size="5"
    timeout="${mq.timeout}" username="${mq.username}" password="${mq.password}" />

  <akka>
    <listeners>
      <listener name="pre-conversion-job-listener" interval="200"
        jobQueueListener="true" maxPendingCount="4"
        queue="file_preconversion_queue" failQueue="file_preconversion_fail_queue"
        agent="conversion-job-dispatcher-agent" jobClass="com.egeio.services.transformer.models.ConversionJob">
      </listener>
      <listener name="pre-conversion-job-listener-win"
        interval="200" jobQueueListener="true" maxPendingCount="4"
        queue="file_preconversion_queue_from_win" failQueue="file_preconversion_fail_queue"
        agent="conversion-job-dispatcher-agent" jobClass="com.egeio.services.transformer.models.ConversionJob">
      </listener>
      <listener name="private-file-pre-conversion-job-listener" interval="200" 
        jobQueueListener="true" maxPendingCount="8" 
        queue="private_file_preconversion_queue" failQueue="private_file_preconversion_fail_queue" 
        agent="conversion-job-dispatcher-agent" jobClass="com.egeio.services.transformer.models.ConversionJob"> 
      </listener>
      <listener name="vip-pre-conversion-job-listener" interval="200"
        jobQueueListener="true" maxPendingCount="8"
        queue="vip_file_preconversion_queue" failQueue="vip_file_preconversion_fail_queue"
        agent="conversion-job-dispatcher-agent" jobClass="com.egeio.services.transformer.models.ConversionJob">
      </listener>
      ${transformer.akka.listener}
    </listeners>

    <agents>
      <agent name="conversion-job-dispatcher-agent"
        class="com.egeio.services.transformer.actors.ConversionJobDispatcherAgent" />
    </agents>
  </akka>

  <monitor>
    <prometheus application="transformer" port="9101" enabled="true"></prometheus>

    <!--  opentsdb  废弃  -->
    <opentsdb ip="${opentsdb.host}" port="4242" enabled="false"></opentsdb>
    <metric>com.egeio.services.transformer</metric>
    <interval>60</interval>
	<http_metric>com.egeio.services.transformer.http</http_metric>
  </monitor>

  <serializes>
    <serialize type="com.egeio.services.transformer.models.ConversionFlag"
      serailizer="com.egeio.services.transformer.models.ConversionFlagSerializer" />
    <serialize type="com.egeio.services.transformer.models.PreviewType"
      serailizer="com.egeio.services.transformer.models.PreviewTypeSerializer" />
    <serialize type="com.egeio.services.transformer.models.JobStatus"
      serailizer="com.egeio.services.transformer.models.JobStatusSerializer" />
    <serialize type="com.egeio.services.transformer.models.PreviewStatus"
      serailizer="com.egeio.services.transformer.models.PreviewStatusSerializer" />
    <serialize
      type="com.egeio.services.transformer.models.OfficeConversionPlatform"
      serailizer="com.egeio.services.transformer.models.OfficeConversionPlatformSerializer" />
    <serialize type="com.egeio.core.web.entity.UploadType"
      serailizer="com.egeio.core.web.entity.UploadTypeSerializer" />
  </serializes>

  <stream>
    <download buffer_size="10240" max_buffer_num="500" timeout="1800000"></download>
  </stream>

  <web_client>
    <max_connection>500</max_connection>
    <max_queue>60000</max_queue>
    <web_root>${v2.api.host}</web_root>
    <web_root_service_id>${v2.service.id}</web_root_service_id>
    <download_timeout>180000</download_timeout>
    <complete_callback>/internal_api/conversion/conversion_complete
    </complete_callback>
    <check_job_url>/internal_api/storages/get_message_info_by_file_storage_id
    </check_job_url>
    <check_preview_url>/internal_api/conversion/get_preview_status
    </check_preview_url>
    <upload_sub_url>/internal_upload</upload_sub_url>
    <download_sub_url>/interndownload</download_sub_url>
    <server_addr_sub_url>/internal_api/storages/get_server_addr
    </server_addr_sub_url>
    <update_status_url>/internal_api/conversion/update_status
    </update_status_url>

    <authentications open_token_expire_second="600"
                     enable_visit_guard="true">
    </authentications>
  </web_client>

  <access_control open_token_expire_second="600"
                  enable_visit_guard="true" enable_validation="true">
  </access_control>

  <jetty_server>
    <port>-1</port>
    <host>0.0.0.0</host>
    <max_queued>3100</max_queued>
    <min_threads>30</min_threads>
    <max_threads>3000</max_threads>
    <idle_time_out>60000</idle_time_out>
    <request_header_size>10240</request_header_size>
    <cross-domain-file>crossdomain.xml</cross-domain-file>
    <options enabled="true">
      <allow_origin allow_all="true">
        <default_origin>${v2.api.host}</default_origin>
        <origin_list_file>origins.conf</origin_list_file>
        <regular_origin>http(s)?://.*\.fangcloud\.com</regular_origin>
      </allow_origin>
      <allow_headers>Range, requesttoken, x-file-name, content-type
      </allow_headers>
      <expose_headers>Content-Range, Accept-Ranges, Content-Length,
        Content-Encoding
      </expose_headers>
      <allow_credentials>true</allow_credentials>
      <max_age>86400</max_age>
    </options>

    <https enabled="false" port="443" host="0.0.0.0"
           cert_location="/usr/local/services/upload/conf/fangcloud.jks"
           password="bigbang!" />
  </jetty_server>

  <publish_queues>
    <publish_queue access="public" type="conversion" queue="file_conversion_queue"/>
    <publish_queue access="public" type="preconversion" queue="file_preconversion_queue"/>
    <publish_queue access="private" type="preconversion" queue="private_file_preconversion_queue"/>
  </publish_queues>

  <memcache enable="true" username="${memcached.username}" password="${memcached.password}"
            binary="true" password_disable="false">
    <servers>
      <server host="${memcached.host}" port="${memcached.port}" />
    </servers>
    <preview_cache_key_version>v1</preview_cache_key_version>
    <min_threads>8</min_threads>
    <max_threads>2000</max_threads>
    <expires>1800000</expires>
  </memcache>

  <tika_file_type_detector>
    <mapping
      type="application/vnd.openxmlformats-officedocument.wordprocessingml.document"
      extensions="docx,doc" />
    <mapping type="application/xml" extensions="doc,docx" />
    <mapping type="application/msword" extensions="doc,wps" />
    <mapping type="application/vnd.ms-works" extensions="wps" />
    <mapping type="application/rtf" extensions="rtf,doc,docx" />
    <mapping type="application/vnd.oasis.opendocument.text"
      extensions="odt" />
    <mapping
      type="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
      extensions="yxls,xlsx,xls" />
    <mapping type="application/vnd.ms-excel" extensions="yxls,xls,et" />
    <mapping type="text/html" extensions="xls,xlsx" />
    <mapping type="application/vnd.oasis.opendocument.spreadsheet"
      extensions="ods" />
    <mapping type="text/tab-separated-values" extensions="tsv" />
    <mapping type="text/csv" extensions="csv" />
    <mapping type="text/plain" extensions="csv,tsv,doc,docx" />
    <mapping type="text/tsv" extensions="tsv" />
    <mapping type="text/html" extensions="doc,docx" />
    <mapping type="message/rfc822" extensions="doc" />
    <mapping
      type="application/vnd.openxmlformats-officedocument.presentationml.presentation"
      extensions="pptx,ppt" />
    <mapping type="application/vnd.ms-powerpoint" extensions="ppt,dps" />
    <mapping type="application/vnd.oasis.opendocument.presentation"
      extensions="odp" />
    <mapping type="application/x-tika-ooxml-protected" extensions="xlsx" />
    <mapping type="application/vnd.ms-project" extensions="mpp" />
    <mapping type="application/vnd.visio" extensions="vdw,vsd" />
    <mapping type="application/vnd.ms-visio.drawing.macroenabled.12" extensions="vsdm" />
    <mapping type="application/vnd.ms-visio.drawing" extensions="vsdx" />
  </tika_file_type_detector>

  <image>
    <max_pixel_size>3000</max_pixel_size>
    <multi_list>ico,gif,tif,tiff</multi_list>
    <ignore_list>png,jpg,jpeg</ignore_list>
    <force_convert_list>jp2</force_convert_list>
    <cmyk_list>psd,jpg,jpeg,jpf,jp2</cmyk_list>
    <max_accept_size>15000</max_accept_size>
    <max_display_size>8000</max_display_size>
    <background>white</background>
    <cmyk_icc>/usr/local/services/material/USWebCoatedSWOP.icc
    </cmyk_icc>
    <rgb_icc>/usr/local/services/material/sRGB.icc</rgb_icc>
  </image>

  <windows-office-converter enabled="true" timeout="200000">
    <mapping_list>
      <mapping extensions="doc, docx, odt, rtf, wps, docm, dot, dotm, dotx"
        process_name="WINWORD" id="word" queue="word_file_conversion_queue" />
      <mapping extensions="ppt, pptx, odp, dps, ppsx, pot, potm, potx, pps, ppsm, pptm" process_name="POWERPNT"
        id="ppt" queue="ppt_file_conversion_queue" />
      <mapping extensions="yxls, xls, xlsx, ods, csv, et, xlsb, xlsm"
        process_name="EXCEL" id="excel" queue="excel_file_conversion_queue" />
      <mapping extensions="vsd,vsdx,vsdm,vdw" process_name="VISIO"
        id="visio" queue="visio_file_conversion_queue" />
      <mapping extensions="mpp" process_name="WINPROJ" id="project"
        queue="project_file_conversion_queue" />
    </mapping_list>
  </windows-office-converter>


  <conversion>
    <tmp-conversion-dir>/var/tmp/conversion</tmp-conversion-dir>
    <tasks file="tasks.xml" />

    <mapping extensions="psd" type="psd">
      <task id="image_thumbnail"
            argsClass="com.egeio.services.transformer.preconvert.PrePsd2Image" />
      <task id="image_1024_stack"
            argsClass="com.egeio.services.transformer.preconvert.PrePsd2Image" />
      <task id="image_2048_stack"
            argsClass="com.egeio.services.transformer.preconvert.PrePsd2Image" />
      <task id="image_water_256"
            argsClass="com.egeio.services.transformer.preconvert.PrePsd2Image" />
    </mapping>
    
    <mapping extensions="png,jpg,jpeg,jpf,jp2,gif,bmp,aix,ico"
             type="image">
      <task id="image_thumbnail" />
      <task id="image_1024_stack" />
      <task id="image_2048_stack" />
      <task id="image_water_256" />
    </mapping>
    
    <mapping extensions="svg" type="svg">
      <task id="image_thumbnail"
            workClass="com.egeio.services.transformer.actors.worker.SVGWorker" />
      <task id="image_water_256"
            workClass="com.egeio.services.transformer.actors.worker.SVGWorker" />
      <task id="abstract_vi_origin_stack" inherit="false"
            workClass="com.egeio.services.transformer.actors.worker.SVGWorker"
            argsClass="com.egeio.services.transformer.preconvert.PreImage2Type">
        <task id="vi_image_1024_stack" />
        <task id="vi_image_2048_stack" />
      </task>
    </mapping>
    
    <mapping extensions="ps,eps" type="Vimag">
      <task id="abstract_vi_origin_stack"
            workClass="com.egeio.services.transformer.actors.worker.CMDWorker"
            argsClass="com.egeio.services.transformer.preconvert.PreVimage2Image" />
    </mapping>
    
    <mapping extensions="vsd,vsdx,vsdm,vdw" type="visio">
      <task id="abstract_pdf_stack"
            transferQueue="visio_file_conversion_queue"
            workClass="com.egeio.services.transformer.actors.worker.WinOfficeWorker"
            argsClass="com.egeio.services.transformer.preconvert.PreWinOffice2Pdf" />
    </mapping>
    
    <mapping extensions="mpp" type="project">
      <task id="abstract_pdf_stack"
            transferQueue="project_file_conversion_queue"
            workClass="com.egeio.services.transformer.actors.worker.WinOfficeWorker"
            argsClass="com.egeio.services.transformer.preconvert.PreWinOffice2Pdf" />
    </mapping>
    
    <mapping extensions="tif,tiff"
             type="image">
      <task id="image_thumbnail"
            transferQueue="file_preconversion_tif_queue"/>
      <task id="image_1024_stack"
            transferQueue="file_preconversion_tif_queue"/>
      <task id="image_2048_stack"
            transferQueue="file_preconversion_tif_queue"/>
      <task id="image_water_256"
            transferQueue="file_preconversion_tif_queue"/>
    </mapping>
    
    <mapping extensions="ai" type="ai">
      <task id="abstract_vi_origin_stack"
            transferQueue="ai_conversion_queue"
            workClass="com.egeio.services.transformer.actors.worker.AIWorker"
            argsClass="com.egeio.services.transformer.preconvert.PreAI2Image" />
    </mapping>
  </conversion>

  <preview_url isInContainer="${transformer.preview.isInContainer}" />
</configuration>
