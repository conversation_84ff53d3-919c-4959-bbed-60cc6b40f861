        html,
        body {
            width: 100%;
            height: 100%;
            margin: 0
        }

        body {
            padding: 0 10px;
            box-sizing: border-box;
        }

        .tabstrip {
            position: fixed;
            top: 0px;
            border-bottom: 1px solid #ccc;
            width: 100%;
            overflow-x: auto;
            white-space: nowrap;
            background-color: #fff;
            z-index: 100000;
        }

        .tabstrip div {
            border-right: 1px solid #ccc;
            padding: 3px 5px;
            display: inline-block;
        }

        .tabstrip div {
            display: inline-block;
        }

        .tabstrip a {
            text-decoration: none;
            color: #6ca2ff
        }

        .tabstrip .active {
            color: #000
        }

        .wrapper {
            box-sizing: border-box;
            overflow: auto;
            width: 100%;
            height: 100%;
            padding-top: 30px;
            position: relative;
            -webkit-overflow-scrolling: touch;
        }
        table table td {
            border: 0;
        }

        td {
            border: 1px dotted lightgray;
        }
        .ds * {
            -webkit-user-select:none;
            -moz-user-select:none;
            -ms-user-select:none;
            user-select:none
        }
        * {
            -webkit-user-select:none;
            -moz-user-select:none;
            -ms-user-select:none;
            user-select:none
        }
    </style>
    <script>
        function on_tab_click(sheetId) {
            if (sheetId) {
                _show_sheet(sheetId);
                document.querySelector('.wrapper').scrollTop = 0;
                document.querySelector('.wrapper').scrollLeft = 0;
            }
        }

        function _show_sheet(sheetId) {
            var sheets = document.querySelectorAll(".wrapper .sheet");
            for (var i = 0; i < sheets.length; i++) {
                sheets[i].style.display = sheetId == sheets[i].id ? "block" : "none";
            }
            var tab_links = document.querySelectorAll(".tabstrip a");
            for (var i = 0; i < tab_links.length; i++) {
                tab_links[i].className = tab_links[i].className.replace(/\bactive\b/, "");
                if (tab_links[i].getAttribute('data-id') == sheetId) {
                    tab_links[i].className += " active";
                }
            }
        }
    </script>

