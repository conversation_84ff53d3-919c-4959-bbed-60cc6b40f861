<?xml version="1.0" encoding="UTF-8" ?>
<configuration>
  <name>TransformerService</name>
  <self_service_id>4</self_service_id>
  <restart_window>3600000</restart_window>
  <retries>1</retries>
  <restart_sleep>200</restart_sleep>
  <timeout>600000</timeout>
  <aspose_queue>file_conversion_queue</aspose_queue>

  <message_queue host="rabbitmq01.fangcloud.net" port="5672"
    timeout="5000" username="root" password="bigbang!" />

  <akka>
    <listeners>
      <listener name="word-conversion-job-listener" interval="200"
        jobQueueListener="true" maxPendingCount="2"
        queue="word_file_conversion_queue" failQueue="word_file_conversion_fail_queue"
        agent="conversion-job-dispatcher-agent" jobClass="com.egeio.services.transformer.models.ConversionJob"
        single_process="true">
      </listener>
      <listener name="excel-conversion-job-listener" interval="200"
        jobQueueListener="true" maxPendingCount="2"
        queue="excel_file_conversion_queue" failQueue="excel_file_conversion_fail_queue"
        agent="conversion-job-dispatcher-agent" jobClass="com.egeio.services.transformer.models.ConversionJob"
        single_process="true">
      </listener>
      <listener name="ppt-conversion-job-listener" interval="200"
        jobQueueListener="true" maxPendingCount="2"
        queue="ppt_file_conversion_queue" failQueue="ppt_file_conversion_fail_queue"
        agent="conversion-job-dispatcher-agent" jobClass="com.egeio.services.transformer.models.ConversionJob"
        single_process="true">
      </listener>
      <listener name="visio-conversion-job-listener" interval="200"
        jobQueueListener="true" maxPendingCount="2"
        queue="visio_file_conversion_queue" failQueue="visio_file_conversion_fail_queue"
        agent="conversion-job-dispatcher-agent" jobClass="com.egeio.services.transformer.models.ConversionJob"
        single_process="true">
      </listener>
      <listener name="project-conversion-job-listener"
        interval="200" jobQueueListener="true" maxPendingCount="2"
        queue="project_file_conversion_queue" failQueue="project_file_conversion_fail_queue"
        agent="conversion-job-dispatcher-agent" jobClass="com.egeio.services.transformer.models.ConversionJob"
        single_process="true">
      </listener>
    </listeners>

    <agents>
      <agent name="conversion-job-dispatcher-agent"
        class="com.egeio.services.transformer.actors.ConversionJobDispatcherAgent">
        <win-process-manager name="word_manager"
          maxAllowedWorkerCount="1" processName="WINWORD"
          processTimeout="300000" />
        <win-process-manager name="excel_manager"
          maxAllowedWorkerCount="1" processName="EXCEL" processTimeout="300000" />
        <win-process-manager name="ppt_manager"
          maxAllowedWorkerCount="1" processName="POWERPNT"
          processTimeout="300000" />
        <win-process-manager name="visio_manager"
          maxAllowedWorkerCount="1" processName="VISIO" processTimeout="300000" />
        <win-process-manager name="project_manager"
          maxAllowedWorkerCount="1" processName="WINPROJ"
          processTimeout="300000" />
      </agent>
    </agents>
  </akka>
  <monitor>
    <opentsdb ip="opentsdb.fangcloud.net" port="4242"></opentsdb>
    <metric>com.egeio.services.transformer</metric>
    <interval>60</interval>
    <http_metric>com.egeio.services.transformer.http</http_metric>
  </monitor>

  <serializes>
    <serialize type="com.egeio.services.transformer.models.ConversionFlag"
      serailizer="com.egeio.services.transformer.models.ConversionFlagSerializer" />
    <serialize type="com.egeio.services.transformer.models.PreviewType"
      serailizer="com.egeio.services.transformer.models.PreviewTypeSerializer" />
    <serialize type="com.egeio.services.transformer.models.JobStatus"
      serailizer="com.egeio.services.transformer.models.JobStatusSerializer" />
    <serialize type="com.egeio.services.transformer.models.PreviewStatus"
      serailizer="com.egeio.services.transformer.models.PreviewStatusSerializer" />
    <serialize
      type="com.egeio.services.transformer.models.OfficeConversionPlatform"
      serailizer="com.egeio.services.transformer.models.OfficeConversionPlatformSerializer" />
    <serialize type="com.egeio.core.web.entity.UploadType"
      serailizer="com.egeio.core.web.entity.UploadTypeSerializer" />
  </serializes>

  <stream>
    <download buffer_size="10240" max_buffer_num="500" timeout="1800000"></download>
  </stream>

  <empty_pdf>empty.pdf</empty_pdf>

  <tika_file_type_detector>
    <mapping type="application/vnd.ms-project" extensions="mpp" />
    <mapping type="application/vnd.visio" extensions="vdw,vsd" />
    <mapping type="application/vnd.ms-visio.drawing.macroenabled.12"
      extensions="vsdm" />
    <mapping type="application/vnd.ms-visio.drawing" extensions="vsdx" />
    <mapping
      type="application/vnd.openxmlformats-officedocument.wordprocessingml.document"
      extensions="docx,doc" />
    <mapping type="application/xml" extensions="doc,docx" />
    <mapping type="application/msword" extensions="doc,wps" />
    <mapping type="application/vnd.ms-works" extensions="wps" />
    <mapping type="application/rtf" extensions="rtf,doc,docx" />
    <mapping type="application/vnd.oasis.opendocument.text"
      extensions="odt" />
    <mapping
      type="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
      extensions="yxls,xlsx,xls" />
    <mapping type="application/vnd.ms-excel" extensions="yxls,xls,et" />
    <mapping type="application/vnd.ms-excel.sheet.binary.macroenabled.12" extensions="xls" />
    <mapping type="text/html" extensions="xls,xlsx" />
    <mapping type="application/vnd.oasis.opendocument.spreadsheet"
      extensions="ods" />
    <mapping type="text/tab-separated-values" extensions="tsv" />
    <mapping type="text/csv" extensions="csv" />
    <mapping type="text/plain" extensions="csv,tsv,doc,docx" />
    <mapping type="text/tsv" extensions="tsv" />
    <mapping type="text/html" extensions="doc,docx" />
    <mapping type="message/rfc822" extensions="doc" />
    <mapping
      type="application/vnd.openxmlformats-officedocument.presentationml.presentation"
      extensions="pptx,ppt" />
    <mapping type="application/vnd.ms-powerpoint" extensions="ppt,dps" />
    <mapping type="application/vnd.oasis.opendocument.presentation"
      extensions="odp" />
  </tika_file_type_detector>

  <tika_protect_file_type_detector>
    <mapping type="application/x-tika-ooxml-protected"
             extensions="xlsx,docx,pptx" />
  </tika_protect_file_type_detector>

  <windows-office-converter enabled="true"
    timeout="200000">
    <mapping_list>
      <mapping extensions="doc,docx,odt,rtf,wps,docm,dot,dotm,dotx"
        process_name="WINWORD" id="word" queue="word_file_conversion_queue" />
      <mapping extensions="ppt,pptx,odp,dps,ppsx,pot,potm,potx,pps,ppsm,pptm" process_name="POWERPNT"
        id="ppt" queue="ppt_file_conversion_queue" />
      <mapping extensions="yxls,xls,xlsx,ods,csv,et,xlsb,xlsm"
        process_name="EXCEL" id="excel" queue="excel_file_conversion_queue" />
      <mapping extensions="vsd,vsdx,vsdm,vdw" process_name="VISIO"
        id="visio" queue="visio_file_conversion_queue" />
      <mapping extensions="mpp" process_name="WINPROJ" id="project"
        queue="project_file_conversion_queue" />
    </mapping_list>
  </windows-office-converter>

  <web_client>
    <download_timeout>180000</download_timeout>
    <web_root>https://v2master.fangcloud.net</web_root>
    <web_root_service_id>1</web_root_service_id>
    <complete_callback>/internal_api/conversion/conversion_complete
    </complete_callback>
    <check_job_url>/internal_api/storages/get_message_info_by_file_storage_id
    </check_job_url>
    <check_preview_url>/internal_api/conversion/get_preview_status
    </check_preview_url>
    <upload_sub_url>/internal_upload</upload_sub_url>
    <download_sub_url>/interndownload</download_sub_url>
    <server_addr_sub_url>/internal_api/storages/get_server_addr
    </server_addr_sub_url>
    <update_status_url>/internal_api/conversion/update_status
    </update_status_url>
    <authentications open_token_expire_second="600"
      enable_visit_guard="true">
      <authentication>
        <server_service_name>Fangcloud web</server_service_name>
        <server_service_id>1</server_service_id>
        <secret>f2a4ae6efa0692a2ebb95f2927f27f2ba0a68198</secret>
        <version>1.0.0</version>
      </authentication>
      <authentication>
        <server_service_name>Fangcloud holmes</server_service_name>
        <server_service_id>3</server_service_id>
        <secret>d7aae863b7855d0a3057412cc38ae99d65b71ace</secret>
        <version>1.0.0</version>
      </authentication>
      <authentication>
        <server_service_name>Fangcloud upload</server_service_name>
        <server_service_id>2</server_service_id>
        <secret>165ff86da413af0cd87ba7b28513ac1adb427767</secret>
        <version>1.0.0</version>
      </authentication>
      <authentication>
        <server_service_name>Zju holmes</server_service_name>
        <server_service_id>18</server_service_id>
        <secret>e266e6cd5d78eeeee30335fa80a68af90e916219</secret>
        <version>1.0.0</version>
      </authentication>
      <authentication>
        <server_service_name>Zju upload</server_service_name>
        <server_service_id>19</server_service_id>
        <secret>137517a023603122ff91d29b4f80727a6eb2659e</secret>
        <version>1.0.0</version>
      </authentication>
      <authentication>
        <server_service_name>Jinko holmes</server_service_name>
        <server_service_id>23</server_service_id>
        <secret>6c66a1f453c8f19808b317a4f4b4c79cab64a1c8</secret>
        <version>1.0.0</version>
      </authentication>
      <authentication>
        <server_service_name>Jinko upload</server_service_name>
        <server_service_id>24</server_service_id>
        <secret>ef08f619c558b610bbac61a44799bf978560624e</secret>
        <version>1.0.0</version>
      </authentication>
    </authentications>
  </web_client>

  <access_control open_token_expire_second="600"
                  enable_visit_guard="true" enable_validation="true">
  </access_control>

  <jetty_server>
    <port>-1</port>
    <host>0.0.0.0</host>
    <max_queued>3100</max_queued>
    <min_threads>30</min_threads>
    <max_threads>3000</max_threads>
    <idle_time_out>60000</idle_time_out>
    <request_header_size>10240</request_header_size>
    <cross-domain-file>crossdomain.xml</cross-domain-file>
    <options enabled="true">
      <allow_origin allow_all="true">
        <default_origin>https://api.fangcloud.net</default_origin>
        <origin_list_file>origins.conf</origin_list_file>
        <regular_origin>http(s)?://.*\.fangcloud\.net</regular_origin>
      </allow_origin>
      <allow_headers>Range, requesttoken, x-file-name, content-type
      </allow_headers>
      <expose_headers>Content-Range, Accept-Ranges, Content-Length,
        Content-Encoding
      </expose_headers>
      <allow_credentials>true</allow_credentials>
      <max_age>86400</max_age>
    </options>

    <https enabled="false" port="443" host="0.0.0.0"
           cert_location="/usr/local/services/transformer/conf/fangcloud.jks"
           password="bigbang!" />
  </jetty_server>

  <publish_queues>
    <publish_queue access="public" type="conversion" queue="file_conversion_queue"/>
    <publish_queue access="public" type="preconversion" queue="file_preconversion_queue"/>
    <publish_queue access="private" type="preconversion" queue="private_file_preconversion_queue"/>
  </publish_queues>

  <memcache username="2a3a38b75ecc4699" password="Bigbang81302"
    binary="true" password_disable="false">
    <servers>
      <server host="2a3a38b75ecc4699.m.cnhzaliqshpub001.ocs.aliyuncs.com"
        port="11211" />
    </servers>
    <preview_cache_key_version>v1</preview_cache_key_version>
    <min_threads>8</min_threads>
    <max_threads>2000</max_threads>
    <expires>1800000</expires>
  </memcache>
  <pspdfkit>
    <upload_host>http://**************:5000</upload_host>
    <preview_host>http://**************:5000</preview_host>
    <secret_token>api-auth-token</secret_token>
    <preview_timeout>120000</preview_timeout>
    <protocol>2</protocol>
    <client>2018.6.1</client>
    <client_git>affba3e047</client_git>
    <origin>http://127.0.0.1</origin>
    <private_key>
********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************
    </private_key>
  </pspdfkit>

  <watermark>
    <font_dir>c:\transformer\my_fonts</font_dir>
    <pdf>
      <font>msyh.ttf</font>
      <fontsize>16</fontsize>
      <opacity>0.5</opacity>
      <rotate>30</rotate>
      <repeat x="4" delimiter_x="    " y="10" />
      <multiplied_leading>1.0</multiplied_leading>
      <gray>0.85</gray>
    </pdf>
    <image>
      <size width="800" height="120" />
      <font>msyh.ttf</font>
      <pointsize>16</pointsize>
      <border>30</border>
      <dissolve>50</dissolve>
      <rotate>350</rotate>
    </image>
  </watermark>

  <conversion>
    <tmp-conversion-dir>c:\conversion</tmp-conversion-dir>
    <tasks file="tasks.xml" />
    <mapping extensions="doc,docx,odt,rtf,wps,docm,dot,dotm,dotx" type="word">
      <task id="office_windows_no_watermark_pdf"
            workManager="word_manager" />
      <task id="office_watermark"
            workManager="word_manager" />
    </mapping>
    <mapping extensions="csv,ods" type="excel">
      <task id="office_windows_watermark_pdf"
            workManager="excel_manager" />
      <task id="office_windows_no_watermark_pdf"
            workManager="excel_manager" />
      <task id="excel_to_html"
            workManager="excel_manager" />
    </mapping>
    <mapping extensions="yxls,xls,xlsx,et,xlsb,xlsm" type="excel">
      <task id="office_windows_watermark_pdf"
            workManager="excel_manager" />
      <task id="office_windows_no_watermark_pdf"
            workManager="excel_manager" />
      <task id="excel_to_html"
            workManager="excel_manager" />
      <task id="office_watermark"
            workManager="excel_manager" />
    </mapping>
    <mapping extensions="ppt,pptx,odp,dps,ppsx,pot,potm,potx,pps,ppsm,pptm" type="ppt">
      <task id="office_windows_watermark_pdf"
            workManager="ppt_manager" />
      <task id="office_windows_no_watermark_pdf"
            workManager="ppt_manager" />
      <task id="office_watermark"
            workManager="ppt_manager" />
    </mapping>
    <mapping extensions="vsd,vsdx,vsdm,vdw" type="visio">
      <task id="office_windows_stack"
            workManager="visio_manager" />
    </mapping>
    <mapping extensions="mpp" type="project">
      <task id="office_windows_stack"
            workManager="project_manager" />
    </mapping>
  </conversion>
</configuration>