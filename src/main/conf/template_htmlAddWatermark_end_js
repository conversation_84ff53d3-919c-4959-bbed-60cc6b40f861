<script>
    var markTextEl = document.querySelector('#watermark-text');
    var watermarkText = markTextEl.innerText;
    var markWidth = markTextEl.clientWidth * 0.87 + 20 * 0.5;
    var markHeight = markTextEl.clientWidth * 0.5 + 20 * 0.87;

    var Watermark = function(containerCls) {
        var container = document.querySelector(containerCls);

        this.opt = {
            container: container,
            markWidth: markWidth || 160,
            markHeight: markHeight || 100,
            watermark: watermarkText,
            docWidth: container.clientWidth,
            docHeight: container.clientHeight,
            fontStyle: "22px Microsoft YaHei", //水印字体设置
            rotateAngle: -30 * Math.PI / 180, //水印字体倾斜角度设置
            fontColor: "rgba(220, 220, 220, 127)", //水印字体颜色设置
            firstLinePositionX: -(markHeight - 20) * 0.5, //canvas第一行文字起始X坐标
            firstLinePositionY: (markHeight - 20) * 0.87 + 20 //Y
        };
        this.draw(this.opt.docWidth, this.opt.docHeight);
        this.events();
    };

    Watermark.prototype = {

        draw: function(docWidth, docHeight) {
            var cw = this.opt.container.querySelector('.watermark');
            var imgBg = this.opt.container.querySelector('.repeat-watermark');

            // crw.setAttribute('width', docWidth);
            // crw.setAttribute('height', docHeight);

            var ctx = cw.getContext("2d");
            //清除小画布
            ctx.clearRect(0, 0, this.opt.markWidth, this.opt.markHeight);
            ctx.font = this.opt.fontStyle;
            //文字倾斜角度
            ctx.rotate(this.opt.rotateAngle);

            ctx.fillStyle = this.opt.fontColor;
            //第一行文字
            ctx.fillText(this.opt.watermark, this.opt.firstLinePositionX, this.opt.firstLinePositionY);
            //坐标系还原
            ctx.rotate(-this.opt.rotateAngle);

            // var ctxr = crw.getContext("2d");
            //清除整个画布
            // ctxr.clearRect(0, 0, crw.width, crw.height);
			var data = cw.toDataURL('image/png', .1);
			imgBg.style.background = "url(" + data + ")";
            //平铺--重复小块的canvas
            // var pat = ctxr.createPattern(cw, "repeat");
            // ctxr.fillStyle = pat;
            // ctxr.fillRect(0, 0, crw.width, crw.height);
        },
        events: function() {
            // var self = this;
            // window.addEventListener('resize', function() {
            //     var w = self.opt.container.clientWidth;
            //     var h = self.opt.container.clientHeight;
            //     self.draw(w, h);
            // });
        }

    };
    var sheets = document.querySelectorAll('.sheet');

    [].slice.call(sheets).forEach(function(el, index) {
        var sheetItem = sheets[index];
        var canvasEl1 = document.createElement('canvas');
        canvasEl1.setAttribute('class', 'watermark');
        canvasEl1.setAttribute('width', markWidth);
        canvasEl1.setAttribute('height', markHeight);
        canvasEl1.setAttribute('style', 'display: none');
        sheetItem.appendChild(canvasEl1);

        var imgBgEl = document.createElement('div');
        imgBgEl.setAttribute('class', 'repeat-watermark');
        sheetItem.appendChild(imgBgEl);

        if (sheetItem.style.display === 'block') {
            initWaterMark(sheetItem.id);
        }
    });
</script>