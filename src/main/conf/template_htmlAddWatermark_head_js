
<script>
    function on_tab_click(sheetId) {
        if (sheetId) {
            _show_sheet(sheetId);
            document.querySelector('.wrapper').scrollTop = 0;
            document.querySelector('.wrapper').scrollLeft = 0;
        }
    }

    function _show_sheet(sheetId) {
        var sheets = document.querySelectorAll(".wrapper .sheet");
        for (var i = 0; i < sheets.length; i++) {
            if (sheetId == sheets[i].id) {
                sheets[i].style.display = 'block'
                initWaterMark(sheetId)
            } else {
                sheets[i].style.display = 'none'
            }
        }
        var tab_links = document.querySelectorAll(".tabstrip a");
        for (var i = 0; i < tab_links.length; i++) {
            tab_links[i].className = tab_links[i].className.replace(/\bactive\b/, "");
            if (tab_links[i].getAttribute('data-id') == sheetId) {
                tab_links[i].className += " active";
            }
        }
    }

    function initWaterMark(sheetId) {
        if (window.Watermark) {
            new Watermark('#' + sheetId);
        }
    }

</script>