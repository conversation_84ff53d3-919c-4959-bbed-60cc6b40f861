<tasks>
  <task id="pspdfkit" convertKind="pspdfkit" bucket="preview"
        workClass="com.egeio.services.transformer.actors.worker.PSPDFKitUploadWorker"
        argsClass="com.egeio.services.transformer.preconvert.PrePSPDFKitUpload">
  </task>
  <task id="pspdfkit_enc" convertKind="pspdfkit_enc" bucket="preview"
        workClass="com.egeio.services.transformer.actors.worker.PSPDFKitUploadWorker"
        argsClass="com.egeio.services.transformer.preconvert.PrePSPDFKitEncryptUpload">
  </task>
  <task id="pspdfkit_watermark" convertKind="pspdfkit_watermark" bucket="preview"
        workClass="com.egeio.services.transformer.actors.worker.PSPDFKitUploadWorker"
        argsClass="com.egeio.services.transformer.preconvert.PrePSPDFKitUpload">
  </task>
  <task id="pspdfkit_watermark_enc" convertKind="pspdfkit_watermark_enc" bucket="preview"
        workClass="com.egeio.services.transformer.actors.worker.PSPDFKitUploadWorker"
        argsClass="com.egeio.services.transformer.preconvert.PrePSPDFKitEncryptUpload">
  </task>
  <task id="pdf_enc" convertKind="pdf_enc" bucket="preview"
        workClass="com.egeio.services.transformer.actors.worker.PdfEncryptWorker"
        argsClass="com.egeio.services.transformer.preconvert.PrePdfEncrypt">
    <task id="pspdfkit_enc" />
  </task>
  <task id="pdf_watermark_enc" convertKind="pdf_watermark_enc" bucket="watermark_preview"
        workClass="com.egeio.services.transformer.actors.worker.PdfWatermarkWorker"
        argsClass="com.egeio.services.transformer.preconvert.PrePdfWatermarkEncrypt">
      <task id="pspdfkit_watermark_enc" />
  </task>
  <task id="pdf_image_1024" convertKind="image_1024" bucket="preview"
        workClass="com.egeio.services.transformer.actors.worker.PdfToImageWorker"
        argsClass="com.egeio.services.transformer.preconvert.PrePdf2Image">
  </task>
  <task id="pdf_watermark" convertKind="pdf_watermark" bucket="watermark_preview"
        workClass="com.egeio.services.transformer.actors.worker.PdfWatermarkWorker"
        argsClass="com.egeio.services.transformer.preconvert.PrePdfWatermark">
    <task id="pspdfkit_watermark" />
  </task>
  <task id="pdf_image_1024_watermark" convertKind="image_1024_watermark" bucket="watermark_preview"
        workClass="com.egeio.services.transformer.actors.worker.PdfToImageWorker"
        argsClass="com.egeio.services.transformer.preconvert.PrePdfWatermark2Image">
  </task>
  <task id="image_1024_watermark" convertKind="image_1024_watermark" bucket="watermark_preview"
        workClass="com.egeio.services.transformer.actors.worker.ImageWatermarkWorker"
        argsClass="com.egeio.services.transformer.preconvert.PreImageWatermark">
  </task>
  <task id="image_2048_watermark" convertKind="image_2048_watermark" bucket="watermark_preview"
        workClass="com.egeio.services.transformer.actors.worker.ImageWatermarkWorker"
        argsClass="com.egeio.services.transformer.preconvert.PreImageWatermark">
  </task>
  <task id="image_thumbnail" convertKind="image_64,image_128" bucket="thumbnail"
        workClass="com.egeio.services.transformer.actors.worker.ImageWorker"
        argsClass="com.egeio.services.transformer.preconvert.PreImage2Type" >
  </task>
  <task id="image_water_256" convertKind="image_water_256" bucket="waterflow"
        workClass="com.egeio.services.transformer.actors.worker.ImageWorker"
        argsClass="com.egeio.services.transformer.preconvert.PreImage2Type">
  </task>
  <task id="vi_thumbnail" convertKind="image_64,image_128" bucket="thumbnail"
        workClass="com.egeio.services.transformer.actors.worker.VICropWorker"
        argsClass="com.egeio.services.transformer.preconvert.PreImage2Type">
  </task>
  <task id="vi_image_water_256" convertKind="image_water_256" bucket="waterflow"
        workClass="com.egeio.services.transformer.actors.worker.VICropWorker"
        argsClass="com.egeio.services.transformer.preconvert.PreImage2Type">
  </task>
  <task id="pdf_watermark_stack" convertKind="pdf_watermark" upload="true"
        bucket="watermark_preview"
        workClass="com.egeio.services.transformer.actors.worker.PdfWatermarkWorker"
        argsClass="com.egeio.services.transformer.preconvert.PrePdfWatermark">
    <task id="pdf_image_1024_watermark" />
    <task id="pdf_watermark" />
  </task>
  <task id="image_1024_stack" convertKind="image_1024" upload="true" bucket="preview"
        workClass="com.egeio.services.transformer.actors.worker.ImageWorker"
        argsClass="com.egeio.services.transformer.preconvert.PreImage2Type">
    <task id="image_1024_watermark"/>
  </task>
  <task id="image_2048_stack" convertKind="image_2048" upload="true" bucket="preview"
        workClass="com.egeio.services.transformer.actors.worker.ImageWorker"
        argsClass="com.egeio.services.transformer.preconvert.PreImage2Type">
    <task id="image_2048_watermark" />
  </task>
  <task id="vi_image_1024_stack" convertKind="image_1024" upload="true" bucket="preview"
        workClass="com.egeio.services.transformer.actors.worker.VICropWorker"
        argsClass="com.egeio.services.transformer.preconvert.PreImage2Type">
    <task id="image_1024_watermark" />
  </task>
  <task id="vi_image_2048_stack" convertKind="image_2048" upload="true" bucket="preview"
        workClass="com.egeio.services.transformer.actors.worker.VICropWorker"
        argsClass="com.egeio.services.transformer.preconvert.PreImage2Type">
    <task id="image_2048_watermark" />
  </task>
  <task id="office_linux_stack" convertKind="pdf" upload="true" bucket="preview"
        workClass="com.egeio.services.transformer.actors.worker.LinuxOfficeWorker"
        argsClass="com.egeio.services.transformer.preconvert.PreLinuxOffice2Pdf">
    <task id="pspdfkit" />
    <task id="pdf_enc" />
    <task id="pdf_watermark_enc" />
    <task id="pdf_watermark_stack" />
    <task id="pdf_image_1024" />
  </task>
  <task id="office_windows_stack" convertKind="pdf" upload="true" bucket="preview"
        workClass="com.egeio.services.transformer.actors.worker.WinOfficeWorker"
        argsClass="com.egeio.services.transformer.preconvert.PreWinOffice2Pdf">
    <task id="pdf_enc"
          transferQueue="file_conversion_queue_from_win" />
    <task id="pdf_watermark_enc"
          transferQueue="file_conversion_queue_from_win" />
    <task id="pdf_watermark_stack"
          transferQueue="file_conversion_queue_from_win" />
    <task id="pdf_image_1024"
          transferQueue="file_conversion_queue_from_win" />
    <task id="pspdfkit"
          transferQueue="file_conversion_queue_from_win" />
  </task>
  <task id="office_windows_watermark_pdf" convertKind="pdf_watermark" upload="true" bucket="watermark_preview"
        workClass="com.egeio.services.transformer.actors.worker.WinOfficeWorker"
        argsClass="com.egeio.services.transformer.preconvert.PreWinOfficeWatermarkPdf">
    <task id="pdf_watermark_enc"
          workClass="com.egeio.services.transformer.actors.worker.PdfEncryptWorker"
          argsClass="com.egeio.services.transformer.preconvert.PrePdfEncryptWatermark" />
    <task id="pdf_image_1024_watermark"
          transferQueue="file_conversion_queue_from_win" />
    <task id="pspdfkit_watermark"
          transferQueue="file_conversion_queue_from_win" />
  </task>
  <task id="office_windows_no_watermark_pdf" convertKind="pdf" upload="true" bucket="preview"
        workClass="com.egeio.services.transformer.actors.worker.WinOfficeWorker"
        argsClass="com.egeio.services.transformer.preconvert.PreWinOffice2Pdf">
    <task id="pdf_enc"
          transferQueue="file_conversion_queue_from_win" />
    <task id="pdf_image_1024"
          transferQueue="file_conversion_queue_from_win" />
    <task id="pspdfkit"
          transferQueue="file_conversion_queue_from_win" />
    <task id="pdf_watermark_enc" />
    <task id="pdf_watermark_stack" />
  </task>
  <task id="html_stack" convertKind="pdf" upload="true" bucket="preview"
        workClass="com.egeio.services.transformer.actors.worker.Html2PdfWorker"
        argsClass="com.egeio.services.transformer.preconvert.PreHtml2Pdf" >
    <task id="pspdfkit" />
    <task id="pdf_enc" />
    <task id="pdf_watermark_enc" />
    <task id="pdf_watermark_stack" />
    <task id="pdf_image_1024" />
  </task>
  <!-- pls supply workClass and argsClass attributes when using this task -->
  <task id="abstract_pdf_stack" convertKind="pdf" upload="true" bucket="preview">
    <task id="pspdfkit" />
    <task id="pdf_enc" />
    <task id="pdf_watermark_enc" />
    <task id="pdf_watermark_stack" />
    <task id="pdf_image_1024" />
  </task>
  <task id="abstract_pdf_stack_without_image" convertKind="pdf" upload="true" bucket="preview">
    <task id="pspdfkit" />
    <task id="pdf_enc" />
    <task id="pdf_watermark_enc" />
    <task id="pdf_watermark" />
  </task>
  <task id="markdown_stack" convertKind="html" bucket="preview"
        workClass="com.egeio.services.transformer.actors.worker.MarkdownWorker"
        argsClass="com.egeio.services.transformer.preconvert.PreMarkdown2Html">
    <task id="html_stack" />
  </task>
  <task id="code_stack" convertKind="html" bucket="preview"
        workClass="com.egeio.services.transformer.actors.worker.CMDWorker"
        argsClass="com.egeio.services.transformer.preconvert.PreCode2Html">
    <task id="html_stack" />
  </task>
  <task id="abstract_vi_origin_stack" convertKind="image_vi_origin" upload="true" bucket="tmp">
    <task id="vi_thumbnail" />
    <task id="vi_image_1024_stack" />
    <task id="vi_image_2048_stack" />
    <task id="vi_image_water_256" />
  </task>
  <task id="bimface" convertKind="bimface"
        workClass="com.egeio.services.transformer.actors.worker.CADBimfaceWorker"
        argsClass="com.egeio.services.transformer.preconvert.PreCAD2Bimface">
  </task>
  <task id="cad_image_1024_stack" convertKind="image_1024_origin" upload="true" bucket="tmp"
        workClass="com.egeio.services.transformer.actors.worker.CADImageWorker"
        argsClass="com.egeio.services.transformer.preconvert.PreCAD2Image">
    <task id="image_1024_stack"
          workClass="com.egeio.services.transformer.actors.worker.TrimImageWorker" />
  </task>
  <task id="cad_image_2048_stack" convertKind="image_2048_origin" upload="true" bucket="tmp"
        workClass="com.egeio.services.transformer.actors.worker.CADImageWorker"
        argsClass="com.egeio.services.transformer.preconvert.PreCAD2Image">
    <task id="image_2048_stack"
          workClass="com.egeio.services.transformer.actors.worker.TrimImageWorker" />
  </task>
  <task id="ocf" convertKind="ocf"
        workClass="com.egeio.services.transformer.actors.worker.Dwg2OcfWorker"
        argsClass="com.egeio.services.transformer.preconvert.PreDwg2Ocf">
  </task>
  <task id="excel_to_html_watermark" convertKind="split_html_watermark" bucket="tmp"
        workClass="com.egeio.services.transformer.actors.worker.WinOfficeWorker"
        argsClass="com.egeio.services.transformer.preconvert.PreWinOfficeWatermarkHtml">
    <task id="excel_merge_html_watermark" />
  </task>
  <task id="excel_merge_html_watermark" convertKind="html_watermark" bucket="watermark_preview"
        argsClass="com.egeio.services.transformer.preconvert.PreMergeHtmlWatermark"
        workClass="com.egeio.services.transformer.actors.worker.MergeHtmlWorker">
  </task>
  <task id="excel_to_html" convertKind="split_html" bucket="tmp"
        workClass="com.egeio.services.transformer.actors.worker.WinOfficeWorker"
        argsClass="com.egeio.services.transformer.preconvert.PreWinOffice2Html">
    <task id="excel_merge_html" />
    <task id="merge_html_add_watermark" />
  </task>
  <task id="excel_merge_html" convertKind="html" bucket="preview"
        argsClass="com.egeio.services.transformer.preconvert.PreMergeHtml"
        workClass="com.egeio.services.transformer.actors.worker.MergeHtmlWorker">
  </task>
  <task id="merge_html_add_watermark" convertKind="html_watermark" bucket="watermark_preview"
        argsClass="com.egeio.services.transformer.preconvert.PreMerageHtmlAddWatermark"
        workClass="com.egeio.services.transformer.actors.worker.MergeHtmlWorker"/>
  <task id="office_watermark" convertKind="office_watermark" bucket="watermark_preview"
        workClass="com.egeio.services.transformer.actors.worker.WinOfficeWorker"
        argsClass="com.egeio.services.transformer.preconvert.PreWinOfficeWatermark">
  </task>
</tasks>