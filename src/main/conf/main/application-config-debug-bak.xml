<?xml version="1.0" encoding="UTF-8" ?>
<configuration>
  <name>TransformerService</name>
  <self_service_id>4</self_service_id>
  <restart_window>3600000</restart_window>
  <retries>1</retries>
  <restart_sleep>200</restart_sleep>
  <timeout>300000</timeout>

  <message_queue host="rabbitmq01.fangcloud.net" port="5672" pool_size="20"
    timeout="5000" username="root" password="bigbang!" />

  <akka>
    <listeners>
      <listener name="conversion-job-listener" interval="200"
        jobQueueListener="true" maxPendingCount="8" queue="file_conversion_queue"
        failQueue="file_conversion_fail_queue" agent="conversion-job-dispatcher-agent"
        jobClass="com.egeio.services.transformer.models.ConversionJob">
      </listener>
      <listener name="conversion-job-listener-win" interval="200"
        jobQueueListener="true" maxPendingCount="8"
        queue="file_conversion_queue_from_win" failQueue="file_conversion_fail_queue"
        agent="conversion-job-dispatcher-agent" jobClass="com.egeio.services.transformer.models.ConversionJob">
      </listener>
      <listener name="pre-conversion-job-listener" interval="200"
        jobQueueListener="true" maxPendingCount="8"
        queue="file_preconversion_queue" failQueue="file_preconversion_fail_queue"
        agent="conversion-job-dispatcher-agent" jobClass="com.egeio.services.transformer.models.ConversionJob">
      </listener>
      <listener name="pre-conversion-job-listener-win"
        interval="200" jobQueueListener="true" maxPendingCount="8"
        queue="file_preconversion_queue_from_win" failQueue="file_preconversion_fail_queue"
        agent="conversion-job-dispatcher-agent" jobClass="com.egeio.services.transformer.models.ConversionJob">
      </listener>
    </listeners>

    <agents>
      <agent name="conversion-job-dispatcher-agent"
        class="com.egeio.services.transformer.actors.ConversionJobDispatcherAgent" />
    </agents>
  </akka>
  <monitor>
    <opentsdb ip="opentsdb.fangcloud.net" port="4242" enabled="true"/>
    <metric>com.egeio.services.transformer</metric>
    <interval>60</interval>
    <http_metric>com.egeio.services.transformer.http</http_metric>
  </monitor>

  <serializes>
    <serialize type="com.egeio.services.transformer.models.ConversionFlag"
      serailizer="com.egeio.services.transformer.models.ConversionFlagSerializer" />
    <serialize type="com.egeio.services.transformer.models.PreviewType"
      serailizer="com.egeio.services.transformer.models.PreviewTypeSerializer" />
    <serialize type="com.egeio.services.transformer.models.JobStatus"
      serailizer="com.egeio.services.transformer.models.JobStatusSerializer" />
    <serialize type="com.egeio.services.transformer.models.PreviewStatus"
      serailizer="com.egeio.services.transformer.models.PreviewStatusSerializer" />
    <serialize
      type="com.egeio.services.transformer.models.OfficeConversionPlatform"
      serailizer="com.egeio.services.transformer.models.OfficeConversionPlatformSerializer" />
    <serialize type="com.egeio.core.web.entity.UploadType"
      serailizer="com.egeio.core.web.entity.UploadTypeSerializer" />
  </serializes>

  <stream>
    <download buffer_size="10240" max_buffer_num="500" timeout="1800000"></download>
  </stream>

  <cmd_worker_standard_output_enable>false</cmd_worker_standard_output_enable>

  <aspose-converter>
    <font_dir>/usr/share/fonts/my_fonts</font_dir>
    <try_aspose_extensions>yxls, xls, xlsx, ods, csv
    </try_aspose_extensions>
    <try_aspose_when_comments>true</try_aspose_when_comments>
    <mappings>
      <mapping extensions="doc, docx, odt, rtf, wps" method="WordToPDF" />
      <mapping extensions="ppt, pptx, odp, dps" method="PowerPointToPDF" />
      <mapping extensions="yxls, xls, xlsx, ods, csv, et"
        method="ExcelToPDF" />
    </mappings>
  </aspose-converter>

  <empty_pdf>empty.pdf</empty_pdf>

  <tika_file_type_detector>
    <mapping type="application/vnd.ms-project" extensions="mpp" />
    <mapping type="application/vnd.visio" extensions="vdw,vsd" />
    <mapping type="application/vnd.ms-visio.drawing.macroenabled.12"
      extensions="vsdm" />
    <mapping type="application/vnd.ms-visio.drawing" extensions="vsdx" />
    <mapping
      type="application/vnd.openxmlformats-officedocument.wordprocessingml.document"
      extensions="docx,doc" />
    <mapping type="application/xml" extensions="doc,docx" />
    <mapping type="application/msword" extensions="doc,wps" />
    <mapping type="application/vnd.ms-works" extensions="wps" />
    <mapping type="application/rtf" extensions="rtf,doc,docx" />
    <mapping type="application/vnd.oasis.opendocument.text"
      extensions="odt" />
    <mapping
      type="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
      extensions="yxls,xlsx,xls" />
    <mapping type="application/vnd.ms-excel" extensions="yxls,xls,et" />
    <mapping type="text/html" extensions="xls,xlsx" />
    <mapping type="application/x-tika-ooxml-protected"
      extensions="xlsx" />
    <mapping type="application/vnd.oasis.opendocument.spreadsheet"
      extensions="ods" />
    <mapping type="text/tab-separated-values" extensions="tsv" />
    <mapping type="text/csv" extensions="csv" />
    <mapping type="text/plain" extensions="csv,tsv,doc,docx" />
    <mapping type="text/tsv" extensions="tsv" />
    <mapping type="text/html" extensions="doc,docx" />
    <mapping type="message/rfc822" extensions="doc" />
    <mapping
      type="application/vnd.openxmlformats-officedocument.presentationml.presentation"
      extensions="pptx,ppt" />
    <mapping type="application/vnd.ms-powerpoint" extensions="ppt,dps" />
    <mapping type="application/vnd.oasis.opendocument.presentation"
      extensions="odp" />
  </tika_file_type_detector>

  <image>
    <max_pixel_size>3000</max_pixel_size>
    <orientation_list>jpg,jpeg,tiff,tif</orientation_list>
    <multi_list>ico,gif,tif,tiff</multi_list>
    <ignore_list>png,jpg,jpeg</ignore_list>
    <force_convert_list>jp2</force_convert_list>
    <cmyk_list>psd,jpg,jpeg,jpf,jp2</cmyk_list>
    <max_accept_size>15000</max_accept_size>
    <max_display_size>8000</max_display_size>
    <background>white</background>
    <cmyk_icc>/usr/local/services/material/USWebCoatedSWOP.icc
    </cmyk_icc>
    <rgb_icc>/usr/local/services/material/sRGB.icc</rgb_icc>
  </image>

  <pdf2image>
    <ppi>120</ppi>
    <size>1024</size>
    <max_pages>100</max_pages>
  </pdf2image>

  <cad>
    <background>black</background>
    <border>20</border>
  </cad>

  <watermark>
    <font_dir>/usr/share/fonts/my_fonts</font_dir>
    <pdf>
      <font>msyh.ttf</font>
      <fontsize>16</fontsize>
      <opacity>0.5</opacity>
      <rotate>30</rotate>
      <repeat x="4" delimiter_x="    " y="10" />
      <multiplied_leading>3.0</multiplied_leading>
      <gray>0.85</gray>
    </pdf>
    <image>
      <size width="800" height="120" />
      <font>msyh.ttf</font>
      <pointsize>16</pointsize>
      <border>30</border>
      <dissolve>50</dissolve>
      <rotate>350</rotate>
    </image>
  </watermark>

  <bimface>
    <app_key>sfuN2nTAzkgQjnWOzZfyCGviVLCTekmz</app_key>
    <app_secret>MtLEqz2vfTAKPDBn5SzrCWXgD7PFGt2F</app_secret>
    <callback>/third_party/bimface/callback</callback>
    <host>https://api.fangcloud.net</host>
  </bimface>

  <windows-office-converter enabled="true"
    timeout="200000">
    <mapping_list>
      <mapping extensions="doc, docx, odt, rtf, wps"
        process_name="WINWORD" id="word" queue="word_file_conversion_queue" />
      <mapping extensions="ppt, pptx, odp, dps" process_name="POWERPNT"
        id="ppt" queue="ppt_file_conversion_queue" />
      <mapping extensions="yxls, xls, xlsx, ods, csv, et"
        process_name="EXCEL" id="excel" queue="excel_file_conversion_queue" />
      <mapping extensions="vsd,vsdx,vsdm,vdw" process_name="VISIO"
        id="visio" queue="visio_file_conversion_queue" />
      <mapping extensions="mpp" process_name="WINPROJ" id="project"
        queue="project_file_conversion_queue" />
    </mapping_list>
  </windows-office-converter>

  <web_client>
    <download_timeout>180000</download_timeout>
    <web_root>https://api.fangcloud.net</web_root>
    <web_root_service_id>1</web_root_service_id>
    <complete_callback>/internal_api/conversion/conversion_complete
    </complete_callback>
    <check_job_url>/internal_api/storages/get_message_info_by_file_storage_id
    </check_job_url>
    <check_preview_url>/internal_api/conversion/get_preview_status
    </check_preview_url>
    <upload_sub_url>/internal_upload</upload_sub_url>
    <download_sub_url>/interndownload</download_sub_url>
    <server_addr_sub_url>/internal_api/storages/get_server_addr
    </server_addr_sub_url>
    <update_status_url>/internal_api/conversion/update_status
    </update_status_url>
    <authentications open_token_expire_second="600"
      enable_visit_guard="true">
      <authentication>
        <server_service_name>Fangcloud web</server_service_name>
        <server_service_id>1</server_service_id>
        <secret>f2a4ae6efa0692a2ebb95f2927f27f2ba0a68198</secret>
        <version>1.0.0</version>
      </authentication>
      <authentication>
        <server_service_name>Fangcloud holmes</server_service_name>
        <server_service_id>3</server_service_id>
        <secret>d7aae863b7855d0a3057412cc38ae99d65b71ace</secret>
        <version>1.0.0</version>
      </authentication>
      <authentication>
        <server_service_name>Fangcloud upload</server_service_name>
        <server_service_id>2</server_service_id>
        <secret>165ff86da413af0cd87ba7b28513ac1adb427767</secret>
        <version>1.0.0</version>
      </authentication>
      <authentication>
        <server_service_name>Zju holmes</server_service_name>
        <server_service_id>18</server_service_id>
        <secret>e266e6cd5d78eeeee30335fa80a68af90e916219</secret>
        <version>1.0.0</version>
      </authentication>
      <authentication>
        <server_service_name>Zju upload</server_service_name>
        <server_service_id>19</server_service_id>
        <secret>137517a023603122ff91d29b4f80727a6eb2659e</secret>
        <version>1.0.0</version>
      </authentication>
      <authentication>
        <server_service_name>Jinko holmes</server_service_name>
        <server_service_id>23</server_service_id>
        <secret>6c66a1f453c8f19808b317a4f4b4c79cab64a1c8</secret>
        <version>1.0.0</version>
      </authentication>
      <authentication>
        <server_service_name>Jinko upload</server_service_name>
        <server_service_id>24</server_service_id>
        <secret>ef08f619c558b610bbac61a44799bf978560624e</secret>
        <version>1.0.0</version>
      </authentication>
    </authentications>
  </web_client>

  <access_control open_token_expire_second="600"
                  enable_visit_guard="true" enable_validation="true">
  </access_control>

  <jetty_server>
    <port>80</port>
    <host>0.0.0.0</host>
    <max_queued>3100</max_queued>
    <min_threads>30</min_threads>
    <max_threads>3000</max_threads>
    <idle_time_out>60000</idle_time_out>
    <request_header_size>10240</request_header_size>
    <cross-domain-file>crossdomain.xml</cross-domain-file>
    <options enabled="true">
      <allow_origin allow_all="true">
        <default_origin>https://api.fangcloud.net</default_origin>
        <origin_list_file>origins.conf</origin_list_file>
        <regular_origin>http(s)?://.*\.fangcloud\.net</regular_origin>
      </allow_origin>
      <allow_headers>Range, requesttoken, x-file-name, content-type
      </allow_headers>
      <expose_headers>Content-Range, Accept-Ranges, Content-Length,
        Content-Encoding
      </expose_headers>
      <allow_credentials>true</allow_credentials>
      <max_age>86400</max_age>
    </options>

    <https enabled="false" port="443" host="0.0.0.0"
           cert_location="/usr/local/services/transformer/conf/fangcloud.jks"
           password="bigbang!" />
  </jetty_server>

  <publish_queues>
    <publish_queue access="public" type="conversion" queue="file_conversion_queue"/>
    <publish_queue access="public" type="preconversion" queue="file_preconversion_queue"/>
    <publish_queue access="private" type="preconversion" queue="private_file_preconversion_queue"/>
  </publish_queues>

  <memcache enable="false" username="2a3a38b75ecc4699" password="Bigbang81302"
    binary="true" password_disable="false">
    <servers>
      <server host="2a3a38b75ecc4699.m.cnhzaliqshpub001.ocs.aliyuncs.com"
        port="11211" />
    </servers>
    <preview_cache_key_version>v1</preview_cache_key_version>
    <min_threads>8</min_threads>
    <max_threads>2000</max_threads>
    <expires>1800000</expires>
  </memcache>

  <conversion>
    <tmp-conversion-dir>/var/tmp/conversion</tmp-conversion-dir>
    <mapping extensions="doc,docx,odt,rtf,wps" type="word">
      <task convertKind="pdf" upload="true" bucket="preview"
        workClass="com.egeio.services.transformer.actors.worker.LinuxOfficeWorker"
        argsClass="com.egeio.services.transformer.preconvert.PreLinuxOffice2Pdf">
        <task convertKind="pdf_watermark" upload="true"
          bucket="watermark_preview"
          workClass="com.egeio.services.transformer.actors.worker.PdfWatermarkWorker"
          argsClass="com.egeio.services.transformer.preconvert.PrePdfWatermark">
          <task convertKind="image_1024_watermark" bucket="watermark_preview"
            workClass="com.egeio.services.transformer.actors.worker.PdfToImageWorker"
            argsClass="com.egeio.services.transformer.preconvert.PrePdfWatermark2Image">
          </task>
        </task>
        <task convertKind="image_1024" bucket="preview"
          workClass="com.egeio.services.transformer.actors.worker.PdfToImageWorker"
          argsClass="com.egeio.services.transformer.preconvert.PrePdf2Image">
        </task>
      </task>
    </mapping>
    <mapping extensions="yxls,xls,xlsx,ods,csv,et" type="excel">
      <task convertKind="pdf" upload="true" bucket="preview"
        workClass="com.egeio.services.transformer.actors.worker.LinuxOfficeWorker"
        argsClass="com.egeio.services.transformer.preconvert.PreLinuxOffice2Pdf">
        <task convertKind="pdf_watermark" upload="true"
          bucket="watermark_preview"
          workClass="com.egeio.services.transformer.actors.worker.PdfWatermarkWorker"
          argsClass="com.egeio.services.transformer.preconvert.PrePdfWatermark">
          <task convertKind="image_1024_watermark" bucket="watermark_preview"
            workClass="com.egeio.services.transformer.actors.worker.PdfToImageWorker"
            argsClass="com.egeio.services.transformer.preconvert.PrePdfWatermark2Image">
          </task>
        </task>
        <task convertKind="image_1024" bucket="preview"
          workClass="com.egeio.services.transformer.actors.worker.PdfToImageWorker"
          argsClass="com.egeio.services.transformer.preconvert.PrePdf2Image">
        </task>
      </task>
    </mapping>
    <mapping extensions="ppt,pptx,odp,dps" type="ppt">
      <task convertKind="pdf" upload="true" bucket="preview"
        workClass="com.egeio.services.transformer.actors.worker.LinuxOfficeWorker"
        argsClass="com.egeio.services.transformer.preconvert.PreLinuxOffice2Pdf">
        <task convertKind="pdf_watermark" upload="true"
          bucket="watermark_preview"
          workClass="com.egeio.services.transformer.actors.worker.PdfWatermarkWorker"
          argsClass="com.egeio.services.transformer.preconvert.PrePdfWatermark">
          <task convertKind="image_1024_watermark" bucket="watermark_preview"
            workClass="com.egeio.services.transformer.actors.worker.PdfToImageWorker"
            argsClass="com.egeio.services.transformer.preconvert.PrePdfWatermark2Image">
          </task>
        </task>
        <task convertKind="image_1024" bucket="preview"
          workClass="com.egeio.services.transformer.actors.worker.PdfToImageWorker"
          argsClass="com.egeio.services.transformer.preconvert.PrePdf2Image">
        </task>
      </task>
    </mapping>
    <mapping extensions="vsd,vsdx,vsdm,vdw" type="visio">
      <task convertKind="pdf" upload="true" bucket="preview"
        transferQueue="visio_file_conversion_queue"
        workClass="com.egeio.services.transformer.actors.worker.WinOfficeWorker"
        argsClass="com.egeio.services.transformer.preconvert.PreWinOffice2Pdf">
        <task convertKind="pdf_watermark" upload="true"
          bucket="watermark_preview"
          workClass="com.egeio.services.transformer.actors.worker.PdfWatermarkWorker"
          argsClass="com.egeio.services.transformer.preconvert.PrePdfWatermark">
          <task convertKind="image_1024_watermark" bucket="watermark_preview"
            workClass="com.egeio.services.transformer.actors.worker.PdfToImageWorker"
            argsClass="com.egeio.services.transformer.preconvert.PrePdfWatermark2Image">
          </task>
        </task>
        <task convertKind="image_1024" bucket="preview"
          workClass="com.egeio.services.transformer.actors.worker.PdfToImageWorker"
          argsClass="com.egeio.services.transformer.preconvert.PrePdf2Image">
        </task>
      </task>
    </mapping>
    <mapping extensions="mpp" type="project">
      <task convertKind="pdf" upload="true" bucket="preview"
        transferQueue="project_file_conversion_queue"
        workClass="com.egeio.services.transformer.actors.worker.WinOfficeWorker"
        argsClass="com.egeio.services.transformer.preconvert.PreWinOffice2Pdf">
        <task convertKind="pdf_watermark" upload="true"
          bucket="watermark_preview"
          workClass="com.egeio.services.transformer.actors.worker.PdfWatermarkWorker"
          argsClass="com.egeio.services.transformer.preconvert.PrePdfWatermark">
          <task convertKind="image_1024_watermark" bucket="watermark_preview"
            workClass="com.egeio.services.transformer.actors.worker.PdfToImageWorker"
            argsClass="com.egeio.services.transformer.preconvert.PrePdfWatermark2Image">
          </task>
        </task>
        <task convertKind="image_1024" bucket="preview"
          workClass="com.egeio.services.transformer.actors.worker.PdfToImageWorker"
          argsClass="com.egeio.services.transformer.preconvert.PrePdf2Image">
        </task>
      </task>
    </mapping>
    <mapping extensions="psd" type="psd">
      <task convertKind="image_64,image_128" bucket="thumbnail"
        workClass="com.egeio.services.transformer.actors.worker.ImageWorker"
        argsClass="com.egeio.services.transformer.preconvert.PrePsd2Image" />
      <task convertKind="image_1024" upload="true" bucket="preview"
        workClass="com.egeio.services.transformer.actors.worker.ImageWorker"
        argsClass="com.egeio.services.transformer.preconvert.PrePsd2Image">
        <task convertKind="image_1024_watermark" bucket="watermark_preview"
          workClass="com.egeio.services.transformer.actors.worker.ImageWatermarkWorker"
          argsClass="com.egeio.services.transformer.preconvert.PreImageWatermark">
        </task>
      </task>
      <task convertKind="image_2048" upload="true" bucket="preview"
        workClass="com.egeio.services.transformer.actors.worker.ImageWorker"
        argsClass="com.egeio.services.transformer.preconvert.PrePsd2Image">
        <task convertKind="image_2048_watermark" bucket="watermark_preview"
          workClass="com.egeio.services.transformer.actors.worker.ImageWatermarkWorker"
          argsClass="com.egeio.services.transformer.preconvert.PreImageWatermark">
        </task>
      </task>
      <task convertKind="image_water_256" bucket="waterflow"
        workClass="com.egeio.services.transformer.actors.worker.ImageWorker"
        argsClass="com.egeio.services.transformer.preconvert.PrePsd2Image" />
    </mapping>
    <mapping extensions="pdf" type="pdf">
      <task convertKind="pdf_watermark" upload="true" bucket="watermark_preview"
        workClass="com.egeio.services.transformer.actors.worker.PdfWatermarkWorker"
        argsClass="com.egeio.services.transformer.preconvert.PrePdfWatermark">
        <task convertKind="image_1024_watermark" bucket="watermark_preview"
          workClass="com.egeio.services.transformer.actors.worker.PdfToImageWorker"
          argsClass="com.egeio.services.transformer.preconvert.PrePdfWatermark2Image">
        </task>
      </task>
      <task convertKind="image_1024" bucket="preview"
        workClass="com.egeio.services.transformer.actors.worker.PdfToImageWorker"
        argsClass="com.egeio.services.transformer.preconvert.PrePdf2Image">
      </task>
    </mapping>
    <mapping extensions="png,jpg,jpeg,jpf,jp2,gif,tif,tiff,bmp,aix,ico"
      type="image">
      <task convertKind="image_64,image_128" bucket="thumbnail"
        workClass="com.egeio.services.transformer.actors.worker.ImageWorker"
        argsClass="com.egeio.services.transformer.preconvert.PreImage2Type" />
      <task convertKind="image_1024" upload="true" bucket="preview"
        workClass="com.egeio.services.transformer.actors.worker.ImageWorker"
        argsClass="com.egeio.services.transformer.preconvert.PreImage2Type">
        <task convertKind="image_1024_watermark" bucket="watermark_preview"
          workClass="com.egeio.services.transformer.actors.worker.ImageWatermarkWorker"
          argsClass="com.egeio.services.transformer.preconvert.PreImageWatermark">
        </task>
      </task>
      <task convertKind="image_2048" upload="true" bucket="preview"
        workClass="com.egeio.services.transformer.actors.worker.ImageWorker"
        argsClass="com.egeio.services.transformer.preconvert.PreImage2Type">
        <task convertKind="image_2048_watermark" bucket="watermark_preview"
          workClass="com.egeio.services.transformer.actors.worker.ImageWatermarkWorker"
          argsClass="com.egeio.services.transformer.preconvert.PreImageWatermark">
        </task>
      </task>
      <task convertKind="image_water_256" bucket="waterflow"
        workClass="com.egeio.services.transformer.actors.worker.ImageWorker"
        argsClass="com.egeio.services.transformer.preconvert.PreImage2Type" />
    </mapping>
    <mapping extensions="svg" type="svg">
      <task convertKind="image_64,image_128" bucket="thumbnail"
        workClass="com.egeio.services.transformer.actors.worker.SVGWorker"
        argsClass="com.egeio.services.transformer.preconvert.PreImage2Type">
      </task>
      <task convertKind="image_water_256" bucket="waterflow"
        workClass="com.egeio.services.transformer.actors.worker.SVGWorker"
        argsClass="com.egeio.services.transformer.preconvert.PreImage2Type">
      </task>
      <task convertKind="image_vi_origin" upload="true" bucket="tmp"
        workClass="com.egeio.services.transformer.actors.worker.SVGWorker"
        argsClass="com.egeio.services.transformer.preconvert.PreImage2Type">
        <task convertKind="image_1024" upload="true" bucket="preview"
          workClass="com.egeio.services.transformer.actors.worker.VICropWorker"
          argsClass="com.egeio.services.transformer.preconvert.PreImage2Type">
          <task convertKind="image_1024_watermark" bucket="watermark_preview"
            workClass="com.egeio.services.transformer.actors.worker.ImageWatermarkWorker"
            argsClass="com.egeio.services.transformer.preconvert.PreImageWatermark">
          </task>
        </task>
        <task convertKind="image_2048" upload="true" bucket="preview"
          workClass="com.egeio.services.transformer.actors.worker.VICropWorker"
          argsClass="com.egeio.services.transformer.preconvert.PreImage2Type">
          <task convertKind="image_2048_watermark" bucket="watermark_preview"
            workClass="com.egeio.services.transformer.actors.worker.ImageWatermarkWorker"
            argsClass="com.egeio.services.transformer.preconvert.PreImageWatermark">
          </task>
        </task>
      </task>
    </mapping>
    <mapping extensions="markdown,md,mdown" type="markdown">
      <task convertKind="html" bucket="preview"
        workClass="com.egeio.services.transformer.actors.worker.MarkdownWorker"
        argsClass="com.egeio.services.transformer.preconvert.PreMarkdown2Html">
        <task convertKind="pdf" upload="true" bucket="preview"
          workClass="com.egeio.services.transformer.actors.worker.Html2PdfWorker"
          argsClass="com.egeio.services.transformer.preconvert.PreHtml2Pdf">
          <task convertKind="pdf_watermark" upload="true"
            bucket="watermark_preview"
            workClass="com.egeio.services.transformer.actors.worker.PdfWatermarkWorker"
            argsClass="com.egeio.services.transformer.preconvert.PrePdfWatermark">
            <task convertKind="image_1024_watermark" bucket="watermark_preview"
              workClass="com.egeio.services.transformer.actors.worker.PdfToImageWorker"
              argsClass="com.egeio.services.transformer.preconvert.PrePdfWatermark2Image">
            </task>
          </task>
          <task convertKind="image_1024" bucket="preview"
            workClass="com.egeio.services.transformer.actors.worker.PdfToImageWorker"
            argsClass="com.egeio.services.transformer.preconvert.PrePdf2Image">
          </task>
        </task>
      </task>
    </mapping>
    <mapping extensions="ps,eps" type="Vimag">
      <task convertKind="image_vi_origin" upload="true" bucket="tmp"
        workClass="com.egeio.services.transformer.actors.worker.CMDWorker"
        argsClass="com.egeio.services.transformer.preconvert.PreVimage2Image">
        <task convertKind="image_64,image_128" bucket="thumbnail"
          workClass="com.egeio.services.transformer.actors.worker.VICropWorker"
          argsClass="com.egeio.services.transformer.preconvert.PreImage2Type">
        </task>
        <task convertKind="image_1024" upload="true" bucket="preview"
          workClass="com.egeio.services.transformer.actors.worker.VICropWorker"
          argsClass="com.egeio.services.transformer.preconvert.PreImage2Type">
          <task convertKind="image_1024_watermark" bucket="watermark_preview"
            workClass="com.egeio.services.transformer.actors.worker.ImageWatermarkWorker"
            argsClass="com.egeio.services.transformer.preconvert.PreImageWatermark">
          </task>
        </task>
        <task convertKind="image_2048" upload="true" bucket="preview"
          workClass="com.egeio.services.transformer.actors.worker.VICropWorker"
          argsClass="com.egeio.services.transformer.preconvert.PreImage2Type">
          <task convertKind="image_2048_watermark" bucket="watermark_preview"
            workClass="com.egeio.services.transformer.actors.worker.ImageWatermarkWorker"
            argsClass="com.egeio.services.transformer.preconvert.PreImageWatermark">
          </task>
        </task>
        <task convertKind="image_water_256" bucket="waterflow"
          workClass="com.egeio.services.transformer.actors.worker.VICropWorker"
          argsClass="com.egeio.services.transformer.preconvert.PreImage2Type">
        </task>
      </task>
    </mapping>
    <mapping extensions="html,xhtml,htm" type="HTML">
      <task convertKind="pdf" upload="true" bucket="preview"
        workClass="com.egeio.services.transformer.actors.worker.Html2PdfWorker"
        argsClass="com.egeio.services.transformer.preconvert.PreHtml2Pdf">
        <task convertKind="pdf_watermark" upload="true"
          bucket="watermark_preview"
          workClass="com.egeio.services.transformer.actors.worker.PdfWatermarkWorker"
          argsClass="com.egeio.services.transformer.preconvert.PrePdfWatermark">
          <task convertKind="image_1024_watermark" bucket="watermark_preview"
            workClass="com.egeio.services.transformer.actors.worker.PdfToImageWorker"
            argsClass="com.egeio.services.transformer.preconvert.PrePdfWatermark2Image">
          </task>
        </task>
        <task convertKind="image_1024" bucket="preview"
          workClass="com.egeio.services.transformer.actors.worker.PdfToImageWorker"
          argsClass="com.egeio.services.transformer.preconvert.PrePdf2Image">
        </task>
      </task>
    </mapping>
    <mapping
      extensions="tsv,as,as3,asm,bat,c,cc,cmake,cpp,cs,csh,css,cxx,diff,erb,groovy,h,haml,hh,java,js,less,m,make,ml,mm,php,pl,plist,properties,py,rb,sass,scala,scm,script,sh,sml,sql,txt,vi,vim,xml,xsd,xsl,xslt,yaml"
      type="code">
      <task convertKind="html" bucket="preview"
        workClass="com.egeio.services.transformer.actors.worker.CMDWorker"
        argsClass="com.egeio.services.transformer.preconvert.PreCode2Html">
        <task convertKind="pdf" upload="true" bucket="preview"
          workClass="com.egeio.services.transformer.actors.worker.Html2PdfWorker"
          argsClass="com.egeio.services.transformer.preconvert.PreHtml2Pdf">
          <task convertKind="pdf_watermark" upload="true"
            bucket="watermark_preview"
            workClass="com.egeio.services.transformer.actors.worker.PdfWatermarkWorker"
            argsClass="com.egeio.services.transformer.preconvert.PrePdfWatermark">
            <task convertKind="image_1024_watermark" bucket="watermark_preview"
              workClass="com.egeio.services.transformer.actors.worker.PdfToImageWorker"
              argsClass="com.egeio.services.transformer.preconvert.PrePdfWatermark2Image">
            </task>
          </task>
          <task convertKind="image_1024" bucket="preview"
            workClass="com.egeio.services.transformer.actors.worker.PdfToImageWorker"
            argsClass="com.egeio.services.transformer.preconvert.PrePdf2Image">
          </task>
        </task>
      </task>
    </mapping>
    <mapping extensions="rvt,rfa,nwd,nwc,ifc,skp,3ds,stl,sat,dgn,igs,stp" type="cadx">
      <task convertKind="bimface"
        workClass="com.egeio.services.transformer.actors.worker.CADBimfaceWorker"
        argsClass="com.egeio.services.transformer.preconvert.PreCAD2Bimface">
      </task>
    </mapping>
    <mapping extensions="dwg,dwf,dxf" type="cad">
      <task convertKind="bimface"
        workClass="com.egeio.services.transformer.actors.worker.CADBimfaceWorker"
        argsClass="com.egeio.services.transformer.preconvert.PreCAD2Bimface">
      </task>
      <task convertKind="pdf" transferQueue="cad_conversion_queue"
        upload="true" bucket="preview"
        workClass="com.egeio.services.transformer.actors.worker.CADPdfWorker"
        argsClass="com.egeio.services.transformer.preconvert.PreCAD2Pdf">
        <task convertKind="pdf_watermark" bucket="watermark_preview"
          workClass="com.egeio.services.transformer.actors.worker.PdfWatermarkWorker"
          argsClass="com.egeio.services.transformer.preconvert.PrePdfWatermark">
        </task>
      </task>
      <task convertKind="image_1024_origin" upload="true"
        transferQueue="cad_conversion_queue" bucket="tmp"
        workClass="com.egeio.services.transformer.actors.worker.CADImageWorker"
        argsClass="com.egeio.services.transformer.preconvert.PreCAD2Image">
        <task convertKind="image_1024" upload="true" bucket="preview"
          workClass="com.egeio.services.transformer.actors.worker.TrimImageWorker"
          argsClass="com.egeio.services.transformer.preconvert.PreImage2Type">
          <task convertKind="image_1024_watermark" bucket="watermark_preview"
            workClass="com.egeio.services.transformer.actors.worker.ImageWatermarkWorker"
            argsClass="com.egeio.services.transformer.preconvert.PreImageWatermark">
          </task>
        </task>
      </task>
      <task convertKind="image_2048_origin" upload="true"
        transferQueue="cad_conversion_queue" bucket="tmp"
        workClass="com.egeio.services.transformer.actors.worker.CADImageWorker"
        argsClass="com.egeio.services.transformer.preconvert.PreCAD2Image">
        <task convertKind="image_2048" upload="true" bucket="preview"
          workClass="com.egeio.services.transformer.actors.worker.TrimImageWorker"
          argsClass="com.egeio.services.transformer.preconvert.PreImage2Type">
          <task convertKind="image_2048_watermark" bucket="watermark_preview"
            workClass="com.egeio.services.transformer.actors.worker.ImageWatermarkWorker"
            argsClass="com.egeio.services.transformer.preconvert.PreImageWatermark">
          </task>
        </task>
      </task>
    </mapping>
    <mapping extensions="ai" type="ai">
      <task convertKind="image_vi_origin" upload="true" bucket="tmp"
        transferQueue="ai_conversion_queue"
        workClass="com.egeio.services.transformer.actors.worker.AIWorker"
        argsClass="com.egeio.services.transformer.preconvert.PreAI2Image">
        <task convertKind="image_64,image_128" bucket="thumbnail"
          workClass="com.egeio.services.transformer.actors.worker.VICropWorker"
          argsClass="com.egeio.services.transformer.preconvert.PreImage2Type">
        </task>
        <task convertKind="image_1024" upload="true" bucket="preview"
          workClass="com.egeio.services.transformer.actors.worker.VICropWorker"
          argsClass="com.egeio.services.transformer.preconvert.PreImage2Type">
          <task convertKind="image_1024_watermark" bucket="watermark_preview"
            workClass="com.egeio.services.transformer.actors.worker.ImageWatermarkWorker"
            argsClass="com.egeio.services.transformer.preconvert.PreImageWatermark">
          </task>
        </task>
        <task convertKind="image_2048" upload="true" bucket="preview"
          workClass="com.egeio.services.transformer.actors.worker.VICropWorker"
          argsClass="com.egeio.services.transformer.preconvert.PreImage2Type">
          <task convertKind="image_2048_watermark" bucket="watermark_preview"
            workClass="com.egeio.services.transformer.actors.worker.ImageWatermarkWorker"
            argsClass="com.egeio.services.transformer.preconvert.PreImageWatermark">
          </task>
        </task>
        <task convertKind="image_water_256" bucket="waterflow"
          workClass="com.egeio.services.transformer.actors.worker.VICropWorker"
          argsClass="com.egeio.services.transformer.preconvert.PreImage2Type">
        </task>
      </task>
    </mapping>
  </conversion>
</configuration>