#!/bin/bash
# chkconfig: - 76 15
# Short-Description: transformer
# Description: transformer service
### END INIT INFO
: ${HOME:=/root}
export HOME
source /etc/profile
PATH=${PATH}:/usr/local/sbin:/usr/local/bin:/sbin:/bin:/usr/sbin:/usr/bin
HOME_DIR=/usr/local/services/transformer/
cd $HOME_DIR   ##进入相应目录
service_name="transformer"
service_process_name="services-transformer"

function checkPid()
{
    for ((i=0; i<$[$2+1]; i++)); do
        result=0
        PID=`ps -ef | grep $service_process_name | grep -v grep | awk '{print $2}'` ##找出对应进程
        if [ -z "$PID" ]; then
            if [ $1 = '1' ]; then ## expect exist
                result=1
            fi
        else
            if [ $1 = '2' ]; then ## expect not exist
                result=1
            fi
        fi

        if [ $result -eq 0 ]; then
            return 0
        else
            if [ $i -eq $2 ]; then
                echo "$3"
                exit 1
            else
                if [ $i -eq 0 ]; then
                    echo ""
                fi
                echo -n "----"
                sleep 1
                if [ $[$i+1] -eq $2 ]; then
                    echo ""
                fi
            fi
        fi
    done
}

case "$1" in
  start)
    checkPid 2 0 "$service_name server is already running"

    echo -n "starting $service_name server"
    /bin/bash $HOME_DIR/start.sh -s

    checkPid 1 1 "$service_name server start failed"

    echo ""
    echo "start $service_name completed"
    ;;
  stop)
    checkPid 1 0 "$service_name server is not running"

    if [ ! -z "$PID" ]; then
        echo -n "Stopping $service_name server"
        kill -15 $PID
        checkPid 2 5 "stop $service_name failed"
        echo ""
        echo "stop $service_name completed"
    fi
    ;;
  restart)
    $0 stop
    $0 start
    ;;
  status)
    PID=`ps -ef | grep $service_process_name | grep -v grep | awk '{print $2}'` ##找出对应进程
    if [ -z "$PID" ]; then
        echo "$service_name server is not running"
    else
        echo "$service_name server is running"
    fi
    ;;
  *)
    echo "Usage: service $service_name {start|stop|restart|status}"
    exit 1
esac

exit 0
