#!/bin/bash
START_MODE=0
DEBUG_MODE=1
RUN_MODE=2
SERVICE_MODE=3

function createFolder()
{
	if [ ! -e "$1" ] || [ ! -d "$1" ];then
		mkdir "$1"
	fi
}

function usage()
{
	echo "-d | --debug: run in debug mode"
	echo "-r | --run: run in background"
	echo "-s | --service: run as service"
}

mode=$START_MODE

while [ "$1" != "" ]; do
    case $1 in
        -d | --debug )          mode=$DEBUG_MODE
                                ;;
        -r | --run )            mode=$RUN_MODE
                                ;;
        -s | --service)			mode=$SERVICE_MODE
        						;;
        -h | --help )           usage
                                exit
                                ;;
        * )                     usage
                                exit 1
    esac
    shift
done

CONVERSION_DIR="/var/tmp/conversion"
UPLOAD_DIR="/var/tmp/upload"
LOG_DIR="/var/log/transformer"

createFolder $CONVERSION_DIR
createFolder $LOG_DIR
createFolder $UPLOAD_DIR

dir=$(dirname $0)
case $mode in
	$START_MODE )		java -XX:OnOutOfMemoryError="kill -9 %p" -Dfile.encoding=UTF-8 -XX:MaxMetaspaceSize=256m -jar ${dir}/services-transformer*.jar
						;;
	$DEBUG_MODE )		java -XX:OnOutOfMemoryError="kill -9 %p" -Dfile.encoding=UTF-8 -XX:MaxMetaspaceSize=256m -jar -agentlib:jdwp=transport=dt_socket,server=y,address=8000 ${dir}/services-transformer*.jar
						;;
	$RUN_MODE )			nohup java -server -XX:OnOutOfMemoryError="kill -9 %p" -Dfile.encoding=UTF-8 -XX:MaxMetaspaceSize=256m -jar ${dir}/services-transformer*.jar 1>/dev/null 2>&1 &
						;;
	$SERVICE_MODE )		java -server -XX:OnOutOfMemoryError="kill -9 %p" -Dfile.encoding=UTF-8 -XX:MaxMetaspaceSize=256m -jar ${dir}/services-transformer*.jar 1>/dev/null 2>&1 &
						;;
esac
