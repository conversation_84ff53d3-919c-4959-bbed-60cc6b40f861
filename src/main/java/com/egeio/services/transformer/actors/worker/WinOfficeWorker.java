package com.egeio.services.transformer.actors.worker;

import akka.actor.Cancellable;
import com.egeio.core.config.Config;
import com.egeio.core.jobsystem.actors.message.FailJobMessage;
import com.egeio.core.jobsystem.actors.message.MoveJobMessage;
import com.egeio.services.transformer.actors.messages.CompleteConversion;
import com.egeio.services.transformer.actors.messages.OfficePlatformMessage;
import com.egeio.services.transformer.actors.messages.StartConversion;
import com.egeio.services.transformer.actors.messages.TimeoutMessage;
import com.egeio.services.transformer.models.ConversionJob;
import com.egeio.services.transformer.models.ConversionStatus;
import com.egeio.services.transformer.models.OfficeConversionPlatform;
import com.egeio.services.transformer.models.OfficeType;
import com.egeio.services.transformer.preconvert.AbstractWinOfficePreConvert;
import com.egeio.services.transformer.preconvert.PreWinOffice2Pdf;
import scala.concurrent.duration.Duration;

import java.util.concurrent.TimeUnit;

public class WinOfficeWorker extends CMDWorker {
    private AbstractWinOfficePreConvert preConvert;
    private OfficeType officeType;

    private Cancellable timeoutMessage;
    private String asposeQueue;

    public WinOfficeWorker(ConversionJob job, AbstractWinOfficePreConvert preConvert) {
        super(job, preConvert);
        this.preConvert = preConvert;
    }

    @Override
    public void realPreStart() {
        logger.info(job.getUUID(), "START: Office [worker: {}]", getPath());

        asposeQueue = Config.getString("/configuration/aspose_queue",
                "file_conversion_queue");

        timeoutMessage = getContext().system().scheduler().scheduleOnce(
                Duration.create(timeout, TimeUnit.MILLISECONDS), self(),
                new TimeoutMessage(job), getContext().dispatcher(), self());

        getSelf().tell(new StartConversion(), getSelf());
    }

    @Override
    public void postStop() {
        if (timeoutMessage != null && !timeoutMessage.isCancelled()) {
            timeoutMessage.cancel();
        }
        logger.info(job.getUUID(), "STOP: Office [worker: {}]", getPath());
    }

    private void notSupport() {
        getSelf().tell(new CompleteConversion(preConvert,
                ConversionStatus.NOT_SUPPORT, job), getSelf());
    }

    private void convert() throws Exception {
        officeType = OfficeType.getTypeViaExt(job.getExtension());
        if (officeType == OfficeType.UNKNOWN) {
            notSupport();
            return;
        }

        getAgent().tell(
                new OfficePlatformMessage(OfficeConversionPlatform.WIN, job),
                getSelf());
        logger.info(job.getUUID(),
                "windows start to convert from [source: {}] to [dest: {}]",
                preConvert.getSourceFilePath(), preConvert.getTargetFilePath());
        preConvert.setType(officeType);

        try {
            // windows office worker's timeout is managed by timeout message,
            // not by cmd executor.
            doCmdJob(timeout * 2);

            logger.info(job.getUUID(), "windows convert success.");

            getSelf().tell(new CompleteConversion(preConvert,
                    ConversionStatus.CONVERTED, job), getSelf());
        }
        catch (Exception e) {
            // ppt do not have another platform
            if (job.isTryAnotherWhenFail() && officeType != OfficeType.PPT) {
                job.setTryAnotherWhenFail(false);

                // try aspose if failed
                if (job.isForceConvert()) {
                    job.setLastPlatform(OfficeConversionPlatform.WIN);
                }
                else {
                    job.setLastPlatform(OfficeConversionPlatform.ASPOSE);
                }
                getSelf().tell(new MoveJobMessage<ConversionJob>(job, asposeQueue, 0),
                        getSelf());
            }
            else {
                throw e;
            }
        }
    }

    @Override
    public boolean onMessageReceive(Object message) throws Exception {
        if (message instanceof StartConversion) {
            convert();
        }
        else if (message instanceof TimeoutMessage) {
            logger.error(job.getUUID(), "converter timeout");
            getSelf().tell(new CompleteConversion(preConvert,
                    ConversionStatus.FAILED, job), getSelf());
        }
        else if (message instanceof CompleteConversion) {
            this.getContext().parent().tell(message, getSelf());

            this.getContext().stop(this.getSelf());
        }
        else if (message instanceof FailJobMessage) {
            logger.error(job.getUUID(), "failed job");
            getAgent().tell(message, getSelf());
            this.getContext().stop(getSelf());
        }
        else {
            return false;
        }
        return true;
    }
}
