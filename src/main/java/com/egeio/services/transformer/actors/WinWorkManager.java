package com.egeio.services.transformer.actors;

import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentLinkedQueue;
import java.util.concurrent.TimeUnit;

import com.egeio.core.jobsystem.actors.ABaseUntypedActor;
import com.egeio.core.jobsystem.actors.WinAppKiller;
import com.egeio.core.jobsystem.actors.message.FailJobMessage;
import com.egeio.core.jobsystem.actors.message.MoveJobMessage;
import com.egeio.core.jobsystem.actors.message.OpentsdbHeartbeatMessage;
import com.egeio.core.jobsystem.actors.message.WinKillCompleted;
import com.egeio.core.jobsystem.actors.message.WinKillFailed;
import com.egeio.core.log.MyUUID;
import com.egeio.core.monitor.MonitorClient;
import com.egeio.core.monitor.MonitorValue;
import com.egeio.core.monitor.MonitorClient.Record;
import com.egeio.services.transformer.actors.messages.CancelWorkMessage4Manager;
import com.egeio.services.transformer.actors.messages.CompleteConversion;
import com.egeio.services.transformer.actors.messages.NewWorkMessage4Manager;
import com.egeio.services.transformer.actors.messages.OfficePlatformMessage;
import com.egeio.services.transformer.actors.messages.StartPollQueue;
import com.egeio.services.transformer.actors.messages.TimeoutMessage;
import com.egeio.services.transformer.models.ConversionJob;
import com.egeio.services.transformer.models.ConversionWork;

import akka.actor.ActorRef;
import akka.actor.Cancellable;
import akka.actor.Props;
import scala.concurrent.duration.Duration;

//helper class to record work state
class ConversionWorkState {
    private ConversionWork conversionWork;
    private Date startedTime;

    public ConversionWorkState(ConversionWork conversionWork) {
        super();
        this.conversionWork = conversionWork;
        startedTime = new Date();
    }

    public ConversionWork getConversionWork() {
        return conversionWork;
    }

    public Date getStartedTime() {
        return startedTime;
    }
}

/* @formatter:off */
/**
 * it supports concurrency control and process killing. 
 * 1): some window conversions, such as AI, can not run in parallel 
 *     and need concurrency control. 
 * 2): some window applications should be restarted when called time reaches a limitation.
 */
/* @formatter:on */
public class WinWorkManager extends ABaseUntypedActor {
    private MyUUID uuid;// to identify thread for logger

    /// for WinAppWorker
    private int maxAllowedConcurrentWorkerCount; // 1 means no parallel
    private ConcurrentLinkedQueue<ConversionWork> concurrentQueue; // for
                                                                   // concurrency
                                                                   // control

    /// for WinAppKiller
    private int maxAllowedCalledTimes;
    private int actualCalledTimes;
    private long workTimeout;
    private String winProcessName;

    private String managerName; // for log and opentsdb

    private Map<ConversionJob, ConversionWorkState> activeJob2WorkState = new ConcurrentHashMap<ConversionJob, ConversionWorkState>();
    private Boolean stop2KillProcess = false; // kill when no active works

    private int delay = 500;
    protected MonitorClient opentsdbClient;
    private Cancellable heartbeat; // can be cancelled when stopping
    private Cancellable opentsdbHeartbeat;

    public WinWorkManager(String managerName, int maxAllowedWorkerCount,
            int maxAllowedCalledTimes, long workTimeout,
            String winProcessName) {
        super();

        this.managerName = managerName;
        this.maxAllowedConcurrentWorkerCount = maxAllowedWorkerCount;
        this.maxAllowedCalledTimes = maxAllowedCalledTimes;
        this.workTimeout = workTimeout;
        this.winProcessName = winProcessName;

        this.concurrentQueue = new ConcurrentLinkedQueue<ConversionWork>();

        uuid = new MyUUID();
    }

    @Override
    protected void realPreStart() throws Exception {
        logger.info(uuid,
                "START: WinWorkManager [agent: {}] with name [managerName: {}]",
                getPath(), managerName);

        this.opentsdbClient = MonitorClient.getInstance(getMonitorMetric());
        opentsdbHeartbeat = getContext().system().scheduler()
                .schedule(Duration.Zero(),
                        Duration.create(this.getMonitorInteval(),
                                TimeUnit.SECONDS),
                        self(), new OpentsdbHeartbeatMessage(),
                        getContext().dispatcher(), null);

        getSelf().tell(new StartPollQueue(), getSelf());
    }

    @Override
    public void postStop() throws Exception {
        if (this.heartbeat != null && !this.heartbeat.isCancelled()) {
            this.heartbeat.cancel();
        }

        if (this.opentsdbHeartbeat != null
                && !this.opentsdbHeartbeat.isCancelled()) {
            this.opentsdbHeartbeat.cancel();
        }

        if (opentsdbClient != null) {
            opentsdbClient.close();
        }

        activeJob2WorkState.clear();
        concurrentQueue.clear();

        logger.info(uuid,
                "STOP: WinWorkManager [agent: {}] with name [managerName: {}]",
                getPath(), managerName);
    }

    private enum PollingStatus {
        Polling,
        Delay,
        DoNothing;
    }

    @SuppressWarnings("unchecked")
    @Override
    protected void realOnReceive(Object message) throws Exception {
        if (message instanceof StartPollQueue) {
            clearTimeoutWork();

            heartbeat = null;
            PollingStatus status = PollingStatus.Delay;
            if (winProcessName == null) {
                if ((activeJob2WorkState
                        .size() < maxAllowedConcurrentWorkerCount)) {
                    if (doConvert()) {
                        status = PollingStatus.Polling;
                    }
                }
            }
            else {
                if ((activeJob2WorkState
                        .size() < maxAllowedConcurrentWorkerCount)
                        && !stop2KillProcess) {
                    if (doConvert()) {
                        status = PollingStatus.Polling;
                    }
                }
                else if (stop2KillProcess && activeJob2WorkState.isEmpty()) {
                    // stop win application process and wait for result
                    stopWinProcess();
                    status = PollingStatus.DoNothing;
                }
            }

            if (status == PollingStatus.Polling) {
                getSelf().tell(new StartPollQueue(), getSelf());
            }
            else if (status == PollingStatus.Delay) {
                heartbeat = getContext().system().scheduler().scheduleOnce(
                        Duration.create(delay, TimeUnit.MILLISECONDS), self(),
                        new StartPollQueue(), getContext().dispatcher(),
                        getSelf());
            }
        }
        else if (message instanceof OpentsdbHeartbeatMessage) {
            sendOpentsdbRecord();
        }
        else if (message instanceof NewWorkMessage4Manager) {
            // get message from mainWorker and put it in queue
            NewWorkMessage4Manager msg = NewWorkMessage4Manager.class
                    .cast(message);
            concurrentQueue.offer(msg.getWork());
        }
        else if (message instanceof CancelWorkMessage4Manager) {
            CancelWorkMessage4Manager msg = CancelWorkMessage4Manager.class
                    .cast(message);
            ConversionWork work = msg.getWork();
            if (!concurrentQueue.remove(work)) {
                if (activeJob2WorkState.containsKey(work.getJob())) {
                    // stop the running worker
                    // Be careful that in most cases, cancel doesn't work for
                    // running Workers.
                    // They still response CompleteConversion back.
                    logger.info(uuid, "job cancelled by mainWorker");
                    getContext().stop(work.getAppWorker());
                    activeJob2WorkState.remove(work.getJob());
                    actualCalledTimes++;
                }
            }
        }
        else if (message instanceof OfficePlatformMessage) {
            OfficePlatformMessage msg = OfficePlatformMessage.class
                    .cast(message);
            ConversionJob job = msg.getJob();
            responseMessage(job, message);
        }
        else if (message instanceof CompleteConversion) {
            // 1.write log
            // 2.response to mainWorker
            // 3.remove work
            CompleteConversion completeConversionMsg = CompleteConversion.class
                    .cast(message);
            ConversionJob job = completeConversionMsg.getJob();

            // check whether the work is cancelled or not
            if (activeJob2WorkState.containsKey(job)) {
                logger.info(uuid, "job completed");
                responseMessage(job, message);
                activeJob2WorkState.remove(job);
            }
            actualCalledTimes++;
        }
        else if (message instanceof FailJobMessage) {
            // 1.write log
            // 2.report to mainWorker
            // 3.abandon work
            FailJobMessage<ConversionJob> failedJobMsg = FailJobMessage.class.cast(message);
            ConversionJob job = failedJobMsg.getJob();

            // check whether the work is cancelled or not
            if (activeJob2WorkState.containsKey(job)) {
                logger.info(uuid, "job failed and abandoned");
                responseMessage(job, message);
                activeJob2WorkState.remove(job);
            }
            actualCalledTimes++;
        }
        else if (message instanceof MoveJobMessage) {
            MoveJobMessage<ConversionJob> msg = MoveJobMessage.class.cast(message);
            ConversionJob job = msg.getJob();

            // check whether the work is cancelled or not
            if (activeJob2WorkState.containsKey(job)) {
                logger.info(uuid, "job moved to {}", msg.getDestQueue());
                responseMessage(job, message);
                activeJob2WorkState.remove(job);
            }
            actualCalledTimes++;
        }
        else if (message instanceof TimeoutMessage) {
            TimeoutMessage winConvertTimeout = TimeoutMessage.class
                    .cast(message);
            ConversionJob job = winConvertTimeout.getJob();
            stop2KillProcess = true;
            stopAppWorker(message, job);
        }
        else if (message instanceof WinKillCompleted) {
            // 1.reset stop4Killer flag to resume work
            logger.info(uuid, "stop [process: {}] completed", winProcessName);
            stop2KillProcess = false;
            actualCalledTimes = 0;

            getSelf().tell(new StartPollQueue(), getSelf());
        }
        else if (message instanceof WinKillFailed) {
            // Nothing we can do in this case but writing log
            logger.info(uuid, "stop [process: {}] failed in [managerName: {}]",
                    winProcessName, managerName);

            getSelf().tell(new StartPollQueue(), getSelf());
        }
        else {
            unhandled(message);
        }
    }

    // record queueSize, activeJobCount, actualCalledTimes
    private void sendOpentsdbRecord() {
        int queueSize = concurrentQueue.size();
        int activeJobCount = activeJob2WorkState.size();

        List<Record> records = new ArrayList<MonitorClient.Record>();

        records.add(createRecordWith2Tags(queueSize, "type",
                "pending_work_count", "managerName", managerName));
        records.add(createRecordWith2Tags(activeJobCount, "type",
                "doing_work_count", "managerName", managerName));
        records.add(createRecordWith2Tags(actualCalledTimes, "type",
                "actual_called_times", "managerName", managerName));

        opentsdbClient.send(records);
    }

    private static Record createRecordWith2Tags(float value, String key1,
            String val1, String key2, String val2) {
        HashMap<String, String> tags = new HashMap<String, String>();
        tags.put(key1, val1);
        tags.put(key2, val2);
        return MonitorValue.createRecord(value, tags, true);
    }

    private void stopAppWorker(Object message, ConversionJob job) {
        // 1. set stop flag
        // 2. report to mainWorker
        // 3. abandon job
        logger.info(uuid, "job failed to convert and abandoned");

        responseMessage(job, message);
        ConversionWorkState workState = activeJob2WorkState.get(job);
        getContext().stop(workState.getConversionWork().getAppWorker());
        activeJob2WorkState.remove(job);

        actualCalledTimes++;
    }

    private void responseMessage(ConversionJob job, Object message) {
        ConversionWorkState workState = activeJob2WorkState.get(job);
        if (workState != null) {
            workState.getConversionWork().getCaller().tell(message, getSelf());
        }
    }

    /**
     * 
     * @return boolean - false means doing nothing and true means doing actual
     *         work
     * @throws InterruptedException
     */
    private boolean doConvert() throws InterruptedException {
        ConversionWork work = concurrentQueue.poll();
        if (work == null) {
            // poll is asynchronous, it will return null if the queue is empty
            return false;
        }
        else {
            // call winAppWorker to do convert and response
            runAppWorker(work);
            return true;
        }
    }

    private void runAppWorker(ConversionWork work) {
        logger.info(uuid, "call [class: {}] to do the actual converting work",
                work.getWorkClass().toString());

        ActorRef winAppWorker = this.getContext().actorOf(Props.create(
                work.getWorkClass(), work.getJob(), work.getPreConvert()));
        work.setAppWorker(winAppWorker);

        // store new work for and for and killer
        activeJob2WorkState.put(work.getJob(), new ConversionWorkState(work));

        // -1 means infinite
        if (maxAllowedCalledTimes != -1 && actualCalledTimes
                + activeJob2WorkState.size() >= maxAllowedCalledTimes) {
            stop2KillProcess = true;
        }
    }

    private void stopWinProcess() {
        logger.info(uuid, "stop [process: {}]", winProcessName);
        this.getContext().actorOf(
                Props.create(WinAppKiller.class, winProcessName, getSelf()));
    }

    private void clearTimeoutWork() {
        Set<ConversionJob> jobs = activeJob2WorkState.keySet();
        for (ConversionJob job : jobs) {
            ConversionWorkState state = activeJob2WorkState.get(job);
            long nowInMilliseconds = new Date().getTime();
            long elapseMilliseconds = nowInMilliseconds
                    - state.getStartedTime().getTime();
            if (elapseMilliseconds >= workTimeout) {
                stop2KillProcess = true;
                stopAppWorker(new TimeoutMessage(job), job);
            }
        }
    }
}