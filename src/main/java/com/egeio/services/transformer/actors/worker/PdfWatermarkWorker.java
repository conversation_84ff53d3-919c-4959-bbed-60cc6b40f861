package com.egeio.services.transformer.actors.worker;

import java.io.IOException;

import com.egeio.services.transformer.actors.AConversionBaseWorker;
import com.egeio.services.transformer.actors.messages.CompleteConversion;
import com.egeio.services.transformer.actors.messages.StartConversion;
import com.egeio.services.transformer.models.ConversionJob;
import com.egeio.services.transformer.models.ConversionStatus;
import com.egeio.services.transformer.models.WatermarkInfo;
import com.egeio.services.transformer.preconvert.AbstractPreConvert;
import com.egeio.services.transformer.preconvert.PrePdfWatermark;
import com.egeio.services.transformer.preconvert.PrePdfWatermarkEncrypt;
import com.egeio.services.transformer.utils.ColorUtil;
import com.egeio.services.transformer.utils.PdfWatermarkUtil;
import com.egeio.services.transformer.utils.TransformerConstants;
import com.itextpdf.io.font.PdfEncodings;
import com.itextpdf.kernel.colors.Color;
import com.itextpdf.kernel.colors.DeviceGray;
import com.itextpdf.kernel.colors.DeviceRgb;
import com.itextpdf.kernel.font.PdfFont;
import com.itextpdf.kernel.font.PdfFontFactory;
import com.itextpdf.kernel.geom.Rectangle;
import com.itextpdf.kernel.pdf.EncryptionConstants;
import com.itextpdf.kernel.pdf.PdfDocument;
import com.itextpdf.kernel.pdf.PdfPage;
import com.itextpdf.kernel.pdf.PdfReader;
import com.itextpdf.kernel.pdf.PdfWriter;
import com.itextpdf.kernel.pdf.WriterProperties;
import com.itextpdf.kernel.pdf.canvas.PdfCanvas;
import com.itextpdf.kernel.pdf.extgstate.PdfExtGState;
import com.itextpdf.layout.Document;
import com.itextpdf.layout.element.Paragraph;
import com.itextpdf.layout.property.TextAlignment;
import com.itextpdf.layout.property.VerticalAlignment;
import org.apache.commons.lang.RandomStringUtils;
import org.apache.commons.lang.StringUtils;

public class PdfWatermarkWorker extends AConversionBaseWorker {

    public PdfWatermarkWorker(ConversionJob job,
            AbstractPreConvert preConvert) {
        super(job, preConvert);
    }

    @Override
    public boolean onMessageReceive(Object message) throws Exception {
        if (message instanceof StartConversion) {
            // add watermark
            PrePdfWatermark prePdfWatermark = PrePdfWatermark.class
                    .cast(preConvert);
            addWatermark(prePdfWatermark);
            this.getContext()
                    .parent().tell(
                            new CompleteConversion(prePdfWatermark,
                                    ConversionStatus.CONVERTED, job),
                            getSelf());
            this.getContext().stop(this.getSelf());
        }
        else {
            return false;
        }
        return true;
    }

    @Override
    protected void realPreStart() throws Exception {
        logger.info(job.getUUID(), "START: Pdf Watermark [worker: {}]",
                getPath());
        this.getSelf().tell(new StartConversion(), null);
    }

    @Override
    public void postStop() throws Exception {
        logger.info(job.getUUID(), "STOP: Pdf Watermark [worker: {}]",
                getPath());
    }

    private void addWatermark(PrePdfWatermark prePdfWatermark)
            throws IOException {
        WriterProperties prop = new WriterProperties();
        // add password
        if (prePdfWatermark instanceof PrePdfWatermarkEncrypt) {
            String userPass = ((PrePdfWatermarkEncrypt) prePdfWatermark)
                    .getPassword();
            String ownerPass = ((PrePdfWatermarkEncrypt) prePdfWatermark)
                    .getOwnerPassword();
            if (ownerPass == null || "".equals(ownerPass)) {
                ownerPass = RandomStringUtils
                        .randomAscii(TransformerConstants.DEFAULT_PASSWORD_LENGTH);
            }
            prop.setStandardEncryption(userPass.getBytes(),
                    ownerPass.getBytes(), 0,
                    EncryptionConstants.ENCRYPTION_AES_256);
        }

        try (PdfReader reader = new PdfReader(
                prePdfWatermark.getSourceFilePath());
                PdfDocument pdfDoc = new PdfDocument(
                        reader.setUnethicalReading(true), new PdfWriter(
                                prePdfWatermark.getTargetFilePath(), prop));
                Document document = new Document(pdfDoc)) {

            int n = pdfDoc.getNumberOfPages();
            for (int i = 1; i <= n; i++) {
                PdfPage page = pdfDoc.getPage(i);
                Rectangle pageSize = page.getPageSize();
                PdfCanvas canvas = new PdfCanvas(page);
                // draw watermark
                Paragraph p = constructWatermarkParagraph(prePdfWatermark, pageSize);
                canvas.saveState();

                WatermarkInfo watermarkInfo = prePdfWatermark.getWatermarkInfo();
                float opacity = PrePdfWatermark.OPACITY;
                int positionRatioWith = 2;
                int positionRatioHeight = 2;
                //自定义水印
                if(watermarkInfo.isCustom()){
                    opacity = watermarkInfo.getOpacity();
                    positionRatioWith = watermarkInfo.getPositionRatioWith();
                    positionRatioHeight = watermarkInfo.getPositionRatioHeight();
                }

                logger.info(job.getUUID(), "[pdf加水印] 水印透明度opacity:{} positionRatioWith:{} positionRatioHeight:{}", opacity,positionRatioWith,positionRatioHeight);
                PdfExtGState gs = new PdfExtGState().setFillOpacity(opacity);
                canvas.setExtGState(gs);

                document.showTextAligned(p,
                        pageSize.getWidth() / positionRatioWith,  //确定水印的x y起始坐标,根据坐标系坐标确定
                        pageSize.getHeight() / positionRatioHeight,
                        pdfDoc.getPageNumber(page),
                        TextAlignment.CENTER,  //文本水平对齐方式   LEFT指的是文字左侧对齐  RIGHT指的是文字右侧对齐
                        VerticalAlignment.MIDDLE,  //文本垂直对齐方式    MIDDLE指中间对齐
                        (float) Math.PI * (PrePdfWatermark.ROTATE / 180.0f)); //弧度，转换公式
                canvas.restoreState();
            }
        }
    }

    private Paragraph constructWatermarkParagraph(PrePdfWatermark prePdfWatermark, Rectangle pageSize) throws IOException {
        logger.info(uuid, "[获取水印数据]");
        PdfFont font = PdfFontFactory.createFont(PrePdfWatermark.FONT,
                PdfEncodings.IDENTITY_H, true);

        int fontSize = PrePdfWatermark.FONTSIZE;
        String fontColor = "";
        Color color = new DeviceGray(PrePdfWatermark.GRAY);

        //自定义水印
        WatermarkInfo watermarkInfo = prePdfWatermark.getWatermarkInfo();
        boolean isCustom = watermarkInfo.isCustom();
        if(isCustom){
            logger.info(uuid, "[自定义水印转换]");
            fontSize = watermarkInfo.getFontSize();

            fontColor = watermarkInfo.getFontColor();
            if (StringUtils.isNotEmpty(fontColor)) {
                int[] colorInts = ColorUtil.hex2RGB(fontColor);
                color = new DeviceRgb(colorInts[0], colorInts[1], colorInts[2]);
            }

            PdfWatermarkUtil pdfWatermarkUtil = new PdfWatermarkUtil();
            String customWatermarkContent = pdfWatermarkUtil.getWatermarkContent(uuid, fontSize, pageSize, font, watermarkInfo.getContentList(), watermarkInfo.getWatermarkLineOffset(), watermarkInfo.getWatermarkLineDistance(), watermarkInfo.getWatermarkLineRepeat());

            Paragraph p = new Paragraph(customWatermarkContent)
                    .setMultipliedLeading(PrePdfWatermark.MULTIPLIED_LEADING)
                    .setFont(font)
                    .setFontSize(fontSize)
                    .setFontColor(color);
//            logger.info(uuid, "[自定义水印格式] customWatermarkContent:{}", customWatermarkContent);
            return p;
        }


        String watermarkPara = prePdfWatermark.getWatermarkParagraph(false);
//        logger.info(uuid, "[普通水印格式] watermarkPara:{}", watermarkPara);
        logger.info(uuid, "[普通水印转换]");
        Paragraph p = new Paragraph(watermarkPara)
                .setMultipliedLeading(PrePdfWatermark.getMultipliedLeading(false))
                .setFont(font)
                .setFontSize(fontSize)
                .setFontColor(color);

        float width = font.getWidth(prePdfWatermark.getWatermarkLine(),fontSize);
        float height = PrePdfWatermark.getMultipliedLeading(false) * fontSize * prePdfWatermark.getRepeatY();

        int ratioY = (int) Math.ceil(pageSize.getHeight() / height * 2);
        int ratioX = (int) Math.ceil(pageSize.getWidth() / width * 3);

        if (ratioX > 1 || ratioY > 1) {
            watermarkPara = prePdfWatermark.getWatermarkParagraph(
                    ratioX * prePdfWatermark.getRepeatX(),
                    ratioY * prePdfWatermark.getRepeatY());
            p = new Paragraph(watermarkPara)
                    .setMultipliedLeading(PrePdfWatermark.getMultipliedLeading(false))
                    .setFont(font)
                    .setFontColor(color)
                    .setFontSize(fontSize);
        }

        if (logger.isDebugEnabled()) {
            width = font.getWidth(prePdfWatermark.getWatermarkLine(),
                    fontSize);
            height = PrePdfWatermark.getMultipliedLeading(false)
                    * fontSize * prePdfWatermark.getRepeatY()
                    * ratioY;
            logger.debug(uuid, "page size: [{}], watermark size: [{}, {}]",
                    pageSize, width, height);
        }

        return p;
    }
}
