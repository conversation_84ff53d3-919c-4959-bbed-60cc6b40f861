package com.egeio.services.transformer.actors.messages;

import com.egeio.core.jobsystem.actors.model.AbstractAkkaModel;
import com.egeio.core.utils.GsonUtils;
import com.egeio.core.webclient.message.WebResponseMessage;
import com.ning.http.client.Response;

public class PSPDFKitPreviewAuthenticatedMessage extends WebResponseMessage {

    private static final long serialVersionUID = 1550405016078001754L;

    private String token;

    @Override
    public void setResponse(Response response) throws Exception {
        PSPDFKitAuthResponse pspdfkitUploadResponse = GsonUtils.getGson().fromJson(response.getResponseBody(), PSPDFKitAuthResponse.class);
        token = pspdfkitUploadResponse.getToken();
    }

    public String getToken() {
        return token;
    }

    private class PSPDFKitAuthResponse extends AbstractAkkaModel {

        private static final long serialVersionUID = -9110687044907992371L;

        private String token;

        public String getToken() {
            return token;
        }

        public void setToken(String token) {
            this.token = token;
        }
    }
}
