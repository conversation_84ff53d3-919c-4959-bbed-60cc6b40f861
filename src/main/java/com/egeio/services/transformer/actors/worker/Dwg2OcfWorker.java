package com.egeio.services.transformer.actors.worker;

import com.egeio.services.transformer.actors.messages.StartConversion;
import com.egeio.services.transformer.models.ConversionJob;
import com.egeio.services.transformer.preconvert.PreDwg2Ocf;

public class Dwg2Ocf<PERSON>orker extends CMDWorker {
    public Dwg2OcfWorker(ConversionJob job, PreDwg2Ocf preConvert) {
        super(job, preConvert);
    }

    @Override
    public void realPreStart() {
        logger.info(job.getUUID(), "START: Dwg Ocf [worker: {}]", getPath());

        this.getSelf().tell(new StartConversion(), null);
    }

    @Override
    public void postStop() {
        logger.info(job.getUUID(), "STOP: Dwg Ocf [worker: {}]", getPath());
    }

}
