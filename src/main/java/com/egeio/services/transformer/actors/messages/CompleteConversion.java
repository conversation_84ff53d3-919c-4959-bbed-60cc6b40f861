package com.egeio.services.transformer.actors.messages;

import java.io.File;
import java.util.Map;

import com.egeio.core.jobsystem.actors.message.AbstractMessage;
import com.egeio.services.transformer.models.ConversionJob;
import com.egeio.services.transformer.models.ConversionStatus;
import com.egeio.services.transformer.models.ImageStatus;
import com.egeio.services.transformer.preconvert.AbstractPreConvert;

public class CompleteConversion extends AbstractMessage {
    private static final long serialVersionUID = -1994427943848316671L;
    private String convertKind;
    private File targetFile;
    private String targetType;
    private ConversionStatus status;

    private ImageStatus imageStatus;
    private ConversionJob job; // for AI, we need job to identify the MainWorker
    private Map<String, Object> additionalInfos;

    public CompleteConversion(String convertKind, File targetFile,
            String targetType, ConversionStatus status, ConversionJob job) {
        this.convertKind = convertKind;
        this.targetFile = targetFile;
        this.status = status;
        this.targetType = targetType;
        this.job = job;
    }

    public CompleteConversion(AbstractPreConvert preConvert,
            ConversionStatus status, ConversionJob job) {
        this(preConvert.getConvertKind(), preConvert.getTargetFile(),
                preConvert.getTargetType(), status, job);
    }

    public CompleteConversion(AbstractPreConvert preConvert,
            ConversionStatus status, ImageStatus imageStatus,
            ConversionJob job) {
        this(preConvert.getConvertKind(), preConvert.getTargetFile(),
                preConvert.getTargetType(), status, job);
        this.imageStatus = imageStatus;
    }

    public CompleteConversion(AbstractPreConvert preConvert,
            ConversionStatus status, ConversionJob job,
            Map<String, Object> additionalInfos) {
        this(preConvert, status, job);
        this.additionalInfos = additionalInfos;
    }

    public String getConvertKind() {
        return convertKind;
    }

    public void setConvertKind(String convertKind) {
        this.convertKind = convertKind;
    }

    public File getTargetFile() {
        return targetFile;
    }

    public void setTargetFile(File targetFile) {
        this.targetFile = targetFile;
    }

    public String getTargetType() {
        return targetType;
    }

    public void setTargetType(String targetType) {
        this.targetType = targetType;
    }

    public ConversionStatus getStatus() {
        return status;
    }

    public void setStatus(ConversionStatus status) {
        this.status = status;
    }

    public ImageStatus getImageStatus() {
        return imageStatus;
    }

    public void setImageStatus(ImageStatus imageStatus) {
        this.imageStatus = imageStatus;
    }

    public ConversionJob getJob() {
        return job;
    }

    public Map<String, Object> getAdditionalInfos() {
        return additionalInfos;
    }

    public void setAdditionalInfos(Map<String, Object> additionalInfos) {
        this.additionalInfos = additionalInfos;
    }

}
