package com.egeio.services.transformer.actors.worker;

import org.apache.commons.io.FileUtils;

import com.egeio.services.transformer.actors.AConversionBaseWorker;
import com.egeio.services.transformer.actors.messages.CompleteConversion;
import com.egeio.services.transformer.actors.messages.StartConversion;
import com.egeio.services.transformer.models.ConversionFlag;
import com.egeio.services.transformer.models.ConversionJob;
import com.egeio.services.transformer.models.ConversionStatus;
import com.egeio.services.transformer.models.ImageConversionKind;
import com.egeio.services.transformer.models.ImageMeta;
import com.egeio.services.transformer.models.ImageStatus;
import com.egeio.services.transformer.preconvert.AbstractImagePreConvert;
import com.egeio.services.transformer.preconvert.AbstractPreConvert;
import com.egeio.services.transformer.utils.ImageUtils;

public class VICropWorker extends AConversionBaseWorker {
    private ImageStatus imageStatus = new ImageStatus();
    private AbstractImagePreConvert preConvert;

    public VICropWorker(ConversionJob job, AbstractPreConvert preConvert) {
        super(job, preConvert);
        this.preConvert = (AbstractImagePreConvert) preConvert;
        imageStatus.setConversion1024(ConversionFlag.CONVERSION);
        imageStatus.setConversion2048(ConversionFlag.UNKNOWN);
    }

    @Override
    public boolean onMessageReceive(Object message) throws Exception {
        if (message instanceof StartConversion) {
            ImageMeta meta = new ImageMeta();

            logger.info(job.getUUID(),
                    "start to convert from [source: {}] to [dest: {}]",
                    preConvert.getSourceFilePath(),
                    preConvert.getTargetFilePath());

            ImageConversionKind convertKind = ImageConversionKind
                    .getImageConversionKinds(preConvert.getConvertKind());
            ConversionStatus status;
            switch (preConvert.getPreviewType()) {
            case THUMBNAIL:
                status = ImageUtils.cropImage(preConvert, job.getUUID(),
                        preConvert.getWidth(), preConvert.getHeight(), meta);
                break;
            case WATER_FLOW:
                status = ImageUtils.waterFlowImage(preConvert, uuid,
                        preConvert.getWidth(), meta);
                break;
            case PREVIEW:
                if (convertKind == ImageConversionKind.IMAGE1024) {
                    status = ImageUtils.compressImage(preConvert, job.getUUID(),
                            ImageUtils.MAX_SIZE, meta);
                    if (!status.isFail()) {
                        if (status == ConversionStatus.IGNORE) {
                            FileUtils.copyFile(preConvert.getSourceFile(),
                                    preConvert.getTargetFile());
                            imageStatus.setConversion2048(
                                    ConversionFlag.NO_CONVERSION);
                            status = ConversionStatus.CONVERTED;
                        }
                        else {
                            imageStatus.setConversion2048(
                                    ConversionFlag.CONVERSION);
                        }
                    }
                }
                else if (convertKind == ImageConversionKind.IMAGE2048) {
                    status = ImageUtils.compressImage(preConvert, job.getUUID(),
                            ImageUtils.MAX_DISPLAY_SIZE, meta);
                    if (status == ConversionStatus.IGNORE) {
                        FileUtils.copyFile(preConvert.getSourceFile(),
                                preConvert.getTargetFile());
                    }
                    status = ConversionStatus.CONVERTED;
                    imageStatus.setConversion2048(ConversionFlag.CONVERSION);
                }
                else {
                    status = ConversionStatus.NOT_SUPPORT;
                }
                break;

            default:
                status = ConversionStatus.NOT_SUPPORT;
                break;
            }

            imageStatus.setRatio(meta.getHeight(), meta.getWidth());

            getAgent().tell(new CompleteConversion(preConvert, status,
                    imageStatus, job), getSelf());
            getContext().stop(this.getSelf());
        }
        else {
            return false;
        }
        return true;
    }

    @Override
    public void realPreStart() {
        logger.info(job.getUUID(), "START: VI Crop [worker: {}]", getPath());
        this.getSelf().tell(new StartConversion(), null);
    }

    @Override
    public void postStop() {
        logger.info(job.getUUID(), "STOP: VI Crop [worker: {}]", getPath());
    }

}
