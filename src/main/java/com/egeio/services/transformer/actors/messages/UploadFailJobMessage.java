package com.egeio.services.transformer.actors.messages;

import com.egeio.core.jobsystem.actors.message.FailJobMessage;
import com.egeio.services.transformer.models.ConversionJob;

public class UploadFailJobMessage extends FailJobMessage<ConversionJob> {
    private String convertKind;

    public UploadFailJobMessage(ConversionJob job, Throwable cause, String kind) {
        super(job, cause);
        this.convertKind = kind;
    }

    public String getConvertKind() {
        return convertKind;
    }

    public void setConvertKind(String convertKind) {
        this.convertKind = convertKind;
    }

    private static final long serialVersionUID = 5837125394422657140L;

}
