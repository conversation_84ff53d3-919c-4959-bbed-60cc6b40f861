package com.egeio.services.transformer.actors.messages;

import com.egeio.core.jobsystem.actors.message.AbstractMessage;
import com.egeio.services.transformer.models.ConversionWork;

public class CancelWorkMessage4Manager extends AbstractMessage {
    private static final long serialVersionUID = 8217814130429999812L;

    private ConversionWork work;

    public CancelWorkMessage4Manager(ConversionWork work) {
        super();
        this.work = work;
    }

    public ConversionWork getWork() {
        return work;
    }

}
