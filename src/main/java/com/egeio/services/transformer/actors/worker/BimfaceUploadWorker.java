package com.egeio.services.transformer.actors.worker;

import java.io.BufferedInputStream;
import java.io.File;
import java.io.FileInputStream;
import java.io.InputStream;

import com.bimface.exception.BimfaceException;
import com.bimface.file.bean.FileBean;
import com.bimface.sdk.BimfaceClient;
import com.egeio.core.encryption.EncryptUtils;
import com.egeio.core.jobsystem.actors.AJobWorker;
import com.egeio.core.log.Logger;
import com.egeio.core.log.LoggerFactory;
import com.egeio.services.transformer.actors.messages.BimfaceSubmittedMessage;
import com.egeio.services.transformer.actors.messages.StartConversion;
import com.egeio.services.transformer.models.ConversionJob;
import com.egeio.services.transformer.utils.BimfaceClientFactory;

public class BimfaceUploadWorker extends AJobWorker<ConversionJob> {
    private static Logger logger = LoggerFactory
            .getLogger(CADBimfaceWorker.class);

    private File sourceFile;
    private BimfaceClient client;

    public BimfaceUploadWorker(ConversionJob job, File sourceFile) {
        super(job);
        this.sourceFile = sourceFile;
        this.client = BimfaceClientFactory.getClient();
    }

    @Override protected void realPreStart() throws Exception {
        logger.info(job.getUUID(), "START: Bimface Upload [worker: {}]",
                getPath());
        getContext().self().tell(new StartConversion(), null);
    }

    @Override public void postStop() {
        logger.info(job.getUUID(), "STOP: Bimface Upload [worker: {}]",
                getPath());
    }

    private String getTargetFileName(String uniqueName, String ext) {
        return uniqueName + "." + ext;
    }

    @Override public boolean onMessageReceive(Object message) throws Exception {
        if (message instanceof StartConversion) {
            try (InputStream inputStream = new BufferedInputStream(
                    new FileInputStream(sourceFile))) {
                String bimfaceFileName = getTargetFileName(EncryptUtils
                                .md5DigestToString(job.getUniqueName().getBytes()),
                        job.getExtension());
                logger.info(job.getUUID(),
                        "start to upload to bimface from [source: {}] to [dest: {}]",
                        sourceFile.getAbsolutePath(), bimfaceFileName);

                FileBean result = client
                        .upload(bimfaceFileName, sourceFile.length(),
                                inputStream);
                logger.info(uuid, "uploaded to bimface");

                this.getContext().parent()
                        .tell(new BimfaceSubmittedMessage(result.getFileId()),
                                self());

            }
            catch (BimfaceException e) {
                logger.error(uuid, "bimface error code: {}",
                        "" + e.getMessage());
                throw e;
            }
            this.getContext().stop(this.getSelf());
        }
        else {
            return false;
        }
        return true;
    }
}
