package com.egeio.services.transformer.actors.worker;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

import org.dom4j.Element;

import com.egeio.core.config.Config;
import com.egeio.services.transformer.actors.AConversionBaseWorker;
import com.egeio.services.transformer.actors.messages.CompleteConversion;
import com.egeio.services.transformer.actors.messages.StartConversion;
import com.egeio.services.transformer.models.ConversionFlag;
import com.egeio.services.transformer.models.ConversionJob;
import com.egeio.services.transformer.models.ImageConversionKind;
import com.egeio.services.transformer.models.ImageStatus;
import com.egeio.services.transformer.models.ConversionStatus;
import com.egeio.services.transformer.models.ImageMeta;
import com.egeio.services.transformer.preconvert.AbstractImagePreConvert;
import com.egeio.services.transformer.preconvert.AbstractPreConvert;
import com.egeio.services.transformer.utils.ImageUtils;

public class ImageWorker extends AConversionBaseWorker {
    private List<String> ignoreImageList = new ArrayList<String>();
    protected ImageStatus imageStatus = new ImageStatus();

    public ImageWorker(ConversionJob job, AbstractPreConvert preConvert) {
        super(job, preConvert);
    }

    protected void dealImageMeta(ConversionStatus result, ImageMeta meta) {
        long size = Math.max(meta.getHeight(), meta.getWidth());
        ImageConversionKind kind = ImageConversionKind
                .getImageConversionKinds(preConvert.getConvertKind());
        imageStatus.setRotation(meta.getRotation());

        if (kind == ImageConversionKind.IMAGE1024) {
            if (result == ConversionStatus.IGNORE) {
                imageStatus.setConversion1024(ConversionFlag.ORIGIN);
                imageStatus.setConversion2048(ConversionFlag.NO_CONVERSION);
            }
            else if (!result.isFail()) {
                imageStatus.setConversion1024(ConversionFlag.CONVERSION);

                if (meta.isReachMax()) {
                    imageStatus.setConversion2048(ConversionFlag.NO_CONVERSION);
                }
                else {
                    if (size <= 1024) {
                        imageStatus.setConversion2048(
                                ConversionFlag.NO_CONVERSION);
                    }
                    else if (meta.isForceConvert() || size > 2048) {
                        imageStatus
                                .setConversion2048(ConversionFlag.CONVERSION);
                    }
                    else {
                        imageStatus.setConversion2048(ConversionFlag.ORIGIN);
                    }
                }
            }
        }
        else if (kind == ImageConversionKind.IMAGE2048) {
            if (result == ConversionStatus.IGNORE) {
                if (size <= 1024) {
                    if (meta.isForceConvert()) {
                        imageStatus
                                .setConversion1024(ConversionFlag.CONVERSION);
                    }
                    else {
                        imageStatus.setConversion1024(ConversionFlag.ORIGIN);
                    }

                    imageStatus.setConversion2048(ConversionFlag.NO_CONVERSION);
                }
                else if (size <= 2048) {
                    imageStatus.setConversion1024(ConversionFlag.CONVERSION);

                    if (meta.isForceConvert()) {
                        imageStatus
                                .setConversion2048(ConversionFlag.CONVERSION);
                    }
                    else {
                        imageStatus.setConversion2048(ConversionFlag.ORIGIN);
                    }
                }
                else {
                    imageStatus.setConversion1024(ConversionFlag.CONVERSION);
                    imageStatus.setConversion2048(ConversionFlag.CONVERSION);
                }
            }
            else if (!result.isFail()) {
                if (size <= 1024) {
                    imageStatus.setConversion1024(ConversionFlag.CONVERSION);
                    imageStatus.setConversion2048(ConversionFlag.NO_CONVERSION);
                }
                else {
                    imageStatus.setConversion1024(ConversionFlag.CONVERSION);
                    imageStatus.setConversion2048(ConversionFlag.CONVERSION);
                }
            }
        }
        else {
            if (!result.isFail()) {
                if (meta.isForceConvert() || size > 1024) {
                    imageStatus.setConversion1024(ConversionFlag.CONVERSION);
                }
                else {
                    imageStatus.setConversion1024(ConversionFlag.ORIGIN);
                }
            }
        }
    }

    @Override
    public boolean onMessageReceive(Object message) throws Exception {
        if (message instanceof StartConversion) {
            AbstractImagePreConvert preConvert = (AbstractImagePreConvert) (this.preConvert);

            // execute the operation
            logger.info(job.getUUID(),
                    "start to convert from [source: {}] to [dest: {}]",
                    preConvert.getSourceFilePath(),
                    preConvert.getTargetFilePath());

            boolean forceConvert = true;
            for (String ext : ignoreImageList) {
                if (preConvert.getSourceType().equals(ext)) {
                    forceConvert = false;
                    break;
                }
            }

            ImageMeta meta = new ImageMeta();
            ConversionStatus result = ImageUtils.doImageWork(
                    preConvert.getSourceFilePath(),
                    preConvert.getTargetFilePath(), preConvert.getWidth(),
                    preConvert.getHeight(), ImageUtils.MAX_SIZE,
                    preConvert.getPreviewType(), forceConvert,
                    preConvert.isOnlyFirstLayer(), true, meta, job.getUUID());
            dealImageMeta(result, meta);
            imageStatus.setRatio(meta.getHeight(), meta.getWidth());

            this.getContext().parent().tell(new CompleteConversion(preConvert,
                    result, imageStatus, job), getSelf());
            this.getContext().stop(this.getSelf());

        }
        else {
            return false;
        }
        return true;
    }

    @Override
    public void realPreStart() {
        logger.info(job.getUUID(), "START: Image [worker: {}]", getPath());

        Element ele = Config.getConfig()
                .getElement("/configuration/image/ignore_list");
        if (ele != null) {
            String ignoreListStr = ele.getTextTrim();
            if (ignoreListStr != null) {
                String[] ignoreListArray = ignoreListStr.split(",");
                ignoreImageList = Arrays.asList(ignoreListArray);
            }
        }

        this.getSelf().tell(new StartConversion(), null);
    }

    @Override
    public void postStop() {
        logger.info(job.getUUID(), "STOP: Image [worker: {}]", getPath());
    }
}
