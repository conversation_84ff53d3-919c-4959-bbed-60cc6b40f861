package com.egeio.services.transformer.actors.messages;

import com.egeio.core.jobsystem.actors.message.AbstractMessage;

public class BimfaceSubmittedMessage extends AbstractMessage {
    private static final long serialVersionUID = 5437563289477961026L;
    private Long bimfaceFileId;

    public BimfaceSubmittedMessage(Long bimfaceFileId) {
        this.bimfaceFileId = bimfaceFileId;
    }

    public Long getBimfaceFileId() {
        return bimfaceFileId;
    }

    public void setBimfaceFileId(Long bimfaceFileId) {
        this.bimfaceFileId = bimfaceFileId;
    }

}
