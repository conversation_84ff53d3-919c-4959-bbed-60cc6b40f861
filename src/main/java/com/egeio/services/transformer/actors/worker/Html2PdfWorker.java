package com.egeio.services.transformer.actors.worker;

import java.io.File;

import org.apache.commons.exec.ExecuteException;

import com.egeio.services.transformer.models.ConversionJob;
import com.egeio.services.transformer.preconvert.AbstractPreConvert;

public class Html2PdfWorker extends CMDWorker {

    @Override
    protected void doCmdJob(long timeout) throws Exception {
        try {
            super.doCmdJob(timeout);
        }
        catch (ExecuteException e) {
            File tempOutput = preConvert.getTargetFile();
            if (tempOutput.exists() && tempOutput.isFile()
                    && tempOutput.length() > 0) {
                logger.warn(uuid,
                        "wkhtml2pdf failed but file generated, maybe warning",
                        e);
            }
            else {
                throw e;
            }
        }
    }

    public Html2PdfWorker(ConversionJob job, AbstractPreConvert preConvert) {
        super(job, preConvert);
    }
}
