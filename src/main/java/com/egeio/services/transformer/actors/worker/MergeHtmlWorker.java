package com.egeio.services.transformer.actors.worker;

import com.egeio.core.log.Logger;
import com.egeio.core.log.LoggerFactory;
import com.egeio.core.utils.GsonUtils;
import com.egeio.services.transformer.actors.AConversionBaseWorker;
import com.egeio.services.transformer.actors.messages.*;
import com.egeio.services.transformer.models.ConversionJob;
import com.egeio.services.transformer.models.ConversionStatus;
import com.egeio.services.transformer.preconvert.AbstractPreConvert;
import com.egeio.services.transformer.preconvert.PreMerageHtmlAddWatermark;
import com.google.gson.reflect.TypeToken;
import org.apache.commons.io.IOUtils;
import org.jsoup.Jsoup;
import org.jsoup.nodes.Document;
import org.jsoup.nodes.Element;
import org.mozilla.universalchardet.UniversalDetector;
import scala.Predef;
import sun.misc.BASE64Encoder;

import javax.script.Invocable;
import javax.script.ScriptEngine;
import javax.script.ScriptEngineManager;
import java.io.*;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

public class MergeHtmlWorker extends AConversionBaseWorker {
    private static Logger logger = LoggerFactory
            .getLogger(MergeHtmlWorker.class);

    static private String templateHeadBegin;
    static private String templateHeadEnd;
    static private String templateBodyScript;
    static private String watermarkCss;
    static private String watermarkHeadJs;
    static private String watermarkEndJs;
    private List<String> tabs;
    private int activeIndex;
    private File sourceFile;
    private File sourceFolder;
    private final Pattern imgPattern = Pattern.compile("(<img(?:[\\s\\S]*?)src=)(\\S*?)( (?:[\\s\\S]*?)>)");
    private final Pattern hiddenColPattern = Pattern.compile("(<col(?:[\\s\\S]*?))(display:none)((?:[\\s\\S]*?)>)");
    private final Pattern backgroundPattern = Pattern.compile("<body(?:[\\s\\S]*?)background=(\\S*?)(?: [\\s\\S]*?)?>");
    private BASE64Encoder base64Encoder = new BASE64Encoder();
    private String charset = "GBK";

    static {
        try {
            templateHeadBegin = new String(IOUtils.toByteArray(
                    MergeHtmlWorker.class.getClassLoader().getResourceAsStream("template_head.begin")));
            templateHeadEnd = new String(IOUtils.toByteArray(
                    MergeHtmlWorker.class.getClassLoader().getResourceAsStream("template_head.end")));
            templateBodyScript = new String(IOUtils.toByteArray(
                    MergeHtmlWorker.class.getClassLoader().getResourceAsStream("template_body.script")));
            watermarkCss = new String(IOUtils.toByteArray(
                    MergeHtmlWorker.class.getClassLoader().getResourceAsStream("template_htmlAddWatermark_css")));
            watermarkHeadJs = new String(IOUtils.toByteArray(
                    MergeHtmlWorker.class.getClassLoader().getResourceAsStream("template_htmlAddWatermark_head_js")));
            watermarkEndJs = new String(IOUtils.toByteArray(
                    MergeHtmlWorker.class.getClassLoader().getResourceAsStream("template_htmlAddWatermark_end_js")));
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
    }

    public MergeHtmlWorker(ConversionJob job, AbstractPreConvert preConvert) throws Exception{
        super(job, preConvert);
        sourceFile = preConvert.getSourceFile();
        String name = getNameWithoutExt(sourceFile.getName());
        sourceFolder = new File(sourceFile.getParentFile(), name + ".files");
        if(!sourceFolder.exists()) {
            sourceFolder = new File(sourceFile.getParentFile(), name + "_files");
        }
    }

    @Override
    protected void realPreStart() throws Exception {
        logger.info(job.getUUID(), "START: merge html [worker: {}]",
                getPath());
        getContext().self().tell(new StartConversion(), null);
    }

    @Override
    public void postStop() {
        logger.info(job.getUUID(), "STOP: merge html [worker: {}]",
                getPath());
    }

    @Override
    public boolean onMessageReceive(Object message) throws Exception {
        if (message instanceof StartConversion) {
            convert();
            getContext().parent().tell(new CompleteConversion(preConvert,
                    ConversionStatus.CONVERTED, job), getSelf());
            getContext().stop(getSelf());
        }
        else {
            return false;
        }
        return true;
    }


    private static String getNameWithoutExt(String name) {
        int index = name.lastIndexOf(".");
        if (index != -1) {
            name = name.substring(0, index);
        }
        return name;
    }

    public void convert() throws Exception {
        detectCharset();
        getTabInfo();
        boolean isAddWatermark = preConvert instanceof PreMerageHtmlAddWatermark;

        try (BufferedWriter writer = new BufferedWriter(new OutputStreamWriter(new FileOutputStream(preConvert.getTargetFilePath())))) {
            writer.write(templateHeadBegin);
            if(isAddWatermark){
                writer.write(watermarkCss);
                writer.newLine();
            }
            appendStyle(writer);
            writer.write(templateHeadEnd);
            if(isAddWatermark){
                writer.write(watermarkHeadJs);
                writer.newLine();
            }
            writer.write("</head>");
            writer.newLine();
            writer.write("<body>");
            writer.newLine();
            appendTabsElement(writer);
            writer.newLine();
            writer.write("<div class='wrapper'>");
            writer.newLine();
            if(isAddWatermark){
                writer.write("<div id=\"watermark-text\">" + getWatermarkContent() + "</div>");
                writer.newLine();
            }
            appendSheetsElement(writer);
            writer.write("</div>");
            writer.newLine();
            // 处理是否可以复制
            writer.write(templateBodyScript);
            writer.newLine();
            if(isAddWatermark){
                writer.write(watermarkEndJs);
                writer.newLine();
            }
            writer.write("</body>");
            writer.newLine();
            writer.write("</html>");
        }
    }

    private void getTabInfo() throws Exception{
        Document sourceDocument = Jsoup.parse(sourceFile, charset);
        Element jsElement = sourceDocument.selectFirst("script[language='JavaScript']");
        ScriptEngine engine = new ScriptEngineManager().getEngineByName("nashorn");
        String js = "var window={\"navigator\":{\"userAgent\":\"\"}};//" + jsElement.html()
                + "\nfunction getTabNames(){ return JSON.stringify(c_rgszSh);};";
        engine.eval(js);
        Invocable invocable = (Invocable) engine;
        tabs = GsonUtils.getGson().fromJson(invocable.invokeFunction("getTabNames").toString(),
                new TypeToken<ArrayList<String>>() {}.getType());

        activeIndex = 1;
        Element activeSheetElement = sourceDocument.selectFirst("frame[name='frSheet']");
        String activeSheetName = activeSheetElement.attr("src");
        Pattern pattern = Pattern.compile("(.+)files/sheet(\\d+)\\.htm(l?)");
        Matcher matcher = pattern.matcher(activeSheetName);
        if(matcher.matches()) {
            activeIndex = Integer.valueOf(matcher.group(2));
        }
    }

    private void appendTabsElement(BufferedWriter writer) throws IOException {
        Element tabsElement = new Element("div");
        tabsElement.addClass("tabstrip");
        for (int i = 0; i < tabs.size(); i++) {
            String sheetId = "sheet" + i;
            Boolean isActive = i + 1 == activeIndex;
            Element newTabElement = new Element("div");
            Element newLinkElement = new Element("a");
            if (isActive) {
                newLinkElement.addClass("active");
            }
            newLinkElement.attr("data-id", sheetId);
            newLinkElement.attr("onclick", "on_tab_click('" + sheetId + "')");
            newLinkElement.attr("href", "javascript:;");
            newLinkElement.html(tabs.get(i));
            newTabElement.appendChild(newLinkElement);
            tabsElement.appendChild(newTabElement);
        }
        writer.write(tabsElement.outerHtml());
    }

    private void appendSheetsElement(BufferedWriter writer) throws IOException {
        String divSheet = "<div id='%s' class='sheet %s' style='display:%s'>";
        for (int i = 0; i < tabs.size(); i++) {
            String sheetId = "sheet" + i;
            Boolean isActive = i + 1 == activeIndex;

            File sheetFile = new File(sourceFolder, String.format("sheet%03d.html", i + 1));
            if(!sheetFile.exists()) {
                sheetFile = new File(sourceFolder, String.format("sheet%03d.htm", i + 1));
            }

            String display = isActive ? "block" : "none";
            String divStart = String.format(divSheet, sheetId, sheetId, display+"%s");

            copySheet(sheetFile, writer, divStart);

            writer.newLine();
            writer.write("</div>");
            writer.newLine();
        }
    }

    private void copySheet(File sheetFile, BufferedWriter writer, String divStart) throws IOException {
        boolean isAddWatermark = preConvert instanceof PreMerageHtmlAddWatermark;
        String divStartFormat = "";
        try(BufferedReader reader = new BufferedReader(new InputStreamReader(new FileInputStream(sheetFile), charset))) {
            String line;
            boolean start = false;
            boolean inVml = false;

            while((line = reader.readLine()) != null) {
                if(line.startsWith("<table")) {
                    while (line.indexOf(">") == -1)
                        line += reader.readLine();
                    if(isAddWatermark){
                        Pattern widthPattern = Pattern.compile("(\\s*?)<table (?:[\\s\\S]*?)width:(\\S*?)[;\'\"]([\\s\\S]*?)>([\\s\\S]*?)");
                        Matcher matcher = widthPattern.matcher(line);
                        if(matcher.matches()){
                            divStartFormat = String.format(divStartFormat,";min-width:"+ matcher.group(2)+";");
                        }else{
                            divStartFormat = String.format(divStartFormat,"");
                        }
                        writer.write(divStartFormat);
                    }
                }
                line = replaceStyle(line);
                if(line.startsWith("<body")) {
                    String background = "";
                    while (line.indexOf(">") == -1) {
                        line += reader.readLine();
                    }
                    Matcher matcher = backgroundPattern.matcher(line);
                    if (matcher.matches()) {
                        background = ";background-image:url(" + extractImage(matcher.group(1)) + ")";
                    }
                    if (isAddWatermark) {
                        background += "%s";
                    }
                    divStartFormat = String.format(divStart, background);
                    if (!isAddWatermark) {
                        writer.write(divStartFormat);
                    }

                    writer.newLine();

                    start = true;
                }
                else if(line.startsWith("</body")) {
                    break;
                }
                else if(start) {
                    int index;
                    if(inVml) {
                        // <!--[if gte vml 1]>后面都是跟一长串的，
                        // 就不考虑<!--[if gte vml 1]>后面还有<![endif]--><![if !vml]>的情况了
                        if((index = line.indexOf("<![endif]--><![if !vml]>")) == -1){
                            continue;
                        }
                        line = line.substring(index + "<![endif]--><![if !vml]>".length());
                        inVml = false;
                    }
                    if((index = line.indexOf("<!--[if gte vml 1]>")) != -1){
                        line = line.substring(0, index);
                        inVml = true;
                    }

                    line = replaceImg(line, reader);
                    line = replaceCol(line, reader);

                    writer.write(line);
                }
            }
        }
    }

    private String replaceImg(String line, BufferedReader reader) throws IOException {
        // img的元素可能被拆成了多行
        int index;
        String tmpLine;
        if((index = line.indexOf("<img")) != -1) {
            while(line.indexOf(">", index) == -1) {
                if((tmpLine = reader.readLine()) == null) {
                    return line;
                }
                line += tmpLine;
            }
        }
        else {
            return line;
        }
        boolean match = false;
        Matcher matcher = imgPattern.matcher(line);
        StringBuffer sb = new StringBuffer();
        while(matcher.find()) {
            match = true;
            matcher.appendReplacement(sb, "$1'" + extractImage(matcher.group(2)) + "'$3");
        }
        if(match) {
            matcher.appendTail(sb);
            line = sb.toString();
        }
        return line;
    }

    private String replaceCol(String line, BufferedReader reader) throws IOException {
        // col的元素可能被拆成了多行
        int index;
        String tmpLine;
        if((index = line.indexOf("<col")) != -1) {
            while(line.indexOf(">", index) == -1) {
                if((tmpLine = reader.readLine()) == null) {
                    return line;
                }
                line += tmpLine;
            }
        }
        else {
            return line;
        }
        boolean match = false;
        Matcher matcher = hiddenColPattern.matcher(line);
        StringBuffer sb = new StringBuffer();
        while(matcher.find()) {
            match = true;
            matcher.appendReplacement(sb, "$1" + "width:80px" + "$3");
        }
        if(match) {
            matcher.appendTail(sb);
            line = sb.toString();
        }
        return line;
    }

    private void appendStyle(BufferedWriter writer) throws IOException {
        try(BufferedReader reader = new BufferedReader(new InputStreamReader(
                new FileInputStream(new File(sourceFolder, "stylesheet.css")), charset))) {
            String line;
            while((line = reader.readLine()) != null) {
                line = replaceStyle(line);
                writer.write(line);
                writer.newLine();
            }
        }
    }

    private String replaceStyle(String inputStr){
        String style = inputStr.replaceAll("windowtext","#000");
        style = style.replaceAll("(:+).5pt",":1pt");// 修复表格边框不显示
        style = style.replaceAll("(;+).5pt",";1pt");
        style = style.replaceAll("( +).5pt"," 1pt");
        if(inputStr.startsWith("<table")){
            // 修复表格内容重叠
            style = style.replaceAll("width","min-width");
            style = style.replaceAll("-min-width","-width");
        }
        return style;
    }

    private void detectCharset() {
        try (FileInputStream fis = new FileInputStream(sourceFile)) {
            UniversalDetector detector = new UniversalDetector(null);
            int nread;
            byte[] buf = new byte[1024];
            while ((nread = fis.read(buf)) > 0 && !detector.isDone()) {
                detector.handleData(buf, 0, nread);
            }
            detector.dataEnd();

            String encoding = detector.getDetectedCharset();
            // 文件可能会有很多，全部转编码性价比太低，直接判断一个源文件的编码
            // 对中文字符集的判断并不很准确，只判断非utf8就默认是gbk
            if (encoding != null && encoding.equals("UTF-8")) {
                charset = encoding;
            }

            detector.reset();
        } catch (IOException ignored) {
        }
    }

    private String extractImage(String imgName) throws IOException {
        InputStream in = new FileInputStream(new File(sourceFolder, imgName));
        byte[] data = new byte[in.available()];
        in.read(data);
        in.close();

        String extension = imgName.substring(imgName.lastIndexOf('.') + 1);
        return "data:image/" + extension + ";base64,"
                + base64Encoder.encode(data).replaceAll("[\n\r]", "");
    }

    private String getWatermarkContent() {
        PreMerageHtmlAddWatermark preMerageHtmlAddWatermark = PreMerageHtmlAddWatermark.class
                .cast(preConvert);
        return preMerageHtmlAddWatermark.getWatermarkContent();
    }
}
