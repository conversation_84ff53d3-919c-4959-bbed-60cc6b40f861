package com.egeio.services.transformer.actors.worker;

import com.egeio.core.config.Config;
import com.egeio.services.transformer.actors.messages.CompleteConversion;
import com.egeio.services.transformer.actors.messages.StartConversion;
import com.egeio.services.transformer.models.ConversionFlag;
import com.egeio.services.transformer.models.ConversionJob;
import com.egeio.services.transformer.models.ImageStatus;
import com.egeio.services.transformer.models.ConversionStatus;
import com.egeio.services.transformer.models.ImageConversionKind;
import com.egeio.services.transformer.preconvert.PreCAD2Image;

public class CADImageWorker extends CMDWorker {
    private ImageStatus imageStatus = new ImageStatus();
    private PreCAD2Image preConvert;
    private int standardSize = 3000;
    private int maxSize = 15000;

    public CADImageWorker(ConversionJob job, PreCAD2Image preConvert) {
        super(job, preConvert);
        this.preConvert = preConvert;
        imageStatus.setConversion1024(ConversionFlag.CONVERSION);
        imageStatus.setConversion2048(ConversionFlag.CONVERSION);
    }

    @Override
    public void realPreStart() {
        logger.info(job.getUUID(), "START: CAD Image [worker: {}]", getPath());

        standardSize = Config.getNumber("/configuration/cad/standard_size",
                standardSize);
        maxSize = Config.getNumber("/configuration/cad/max_size", maxSize);

        this.getSelf().tell(new StartConversion(), null);
    }

    @Override
    public void postStop() {
        logger.info(job.getUUID(), "STOP: CAD Image [worker: {}]", getPath());
    }

    private void doJob() throws Exception {
        ImageConversionKind kind = ImageConversionKind
                .getImageConversionKinds(preConvert.getConvertKind());
        if (kind == ImageConversionKind.IMAGE1024_ORIGIN) {
            preConvert.setSize(standardSize);
        }
        else if (kind == ImageConversionKind.IMAGE2048_ORIGIN) {
            preConvert.setSize(maxSize);
        }

        doTmpCmdJob(timeout);
    }

    @Override
    public boolean onMessageReceive(Object message) throws Exception {
        if (message instanceof StartConversion) {
            logger.info(job.getUUID(),
                    "start to convert from [source: {}] to [dest: {}]",
                    this.preConvert.getSourceFilePath(),
                    this.preConvert.getTargetFilePath());

            doJob();
            getSelf().tell(
                    new CompleteConversion(preConvert,
                            ConversionStatus.CONVERTED, imageStatus, job),
                    getSelf());
        }
        else if (message instanceof CompleteConversion) {
            this.getContext().parent().tell(message, getSelf());

            this.getContext().stop(this.getSelf());
        }
        else {
            return false;
        }
        return true;
    }
}
