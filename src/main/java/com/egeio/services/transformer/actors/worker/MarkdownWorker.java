package com.egeio.services.transformer.actors.worker;

import java.io.File;

import org.apache.commons.exec.CommandLine;

import com.egeio.core.cmd.CmdExecute;
import com.egeio.services.transformer.actors.messages.StartConversion;
import com.egeio.services.transformer.models.ConversionJob;
import com.egeio.services.transformer.preconvert.AbstractPreConvert;
import com.egeio.services.transformer.preconvert.PreMarkdown2Html;

public class MarkdownWorker extends CMDWorker {
    private File tmpFile;

    public MarkdownWorker(ConversionJob job, AbstractPreConvert preConvert) {
        super(job, preConvert);
    }

    @Override
    protected void doCmdJob(long timeout) throws Exception {
        PreMarkdown2Html markDownPre = PreMarkdown2Html.class.cast(preConvert);
        CommandLine utfCmd = markDownPre.get2UtfCMD();

        new CmdExecute().execute(utfCmd, preConvert.getWorkingDirPath(),
                timeout / 2);
        tmpFile = markDownPre.getTmpFile();
        if (!tmpFile.exists()) {
            throw new Exception("convert failed!");
        }

        CommandLine cmd = markDownPre.getCMD();
        new CmdExecute().execute(cmd, preConvert.getWorkingDirPath(),
                timeout / 2);
        if (new File(preConvert.getWorkingDirPath())
                .list(preConvert.getFileFilter()).length == 0) {
            throw new Exception("convert failed!");
        }

        tmpFile.delete();
    }

    @Override
    public void realPreStart() {
        logger.info(job.getUUID(), "START: Markdown [worker: {}]", getPath());
        getContext().self().tell(new StartConversion(), null);
    }

    @Override
    public void postStop() {
        logger.info(job.getUUID(), "STOP: Markdown [worker: {}]", getPath());
        if (tmpFile != null && tmpFile.exists()) {
            tmpFile.delete();
        }
    }
}
