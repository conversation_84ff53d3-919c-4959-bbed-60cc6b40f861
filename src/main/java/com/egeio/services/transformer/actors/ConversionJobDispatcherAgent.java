package com.egeio.services.transformer.actors;

import java.util.HashMap;
import java.util.List;
import java.util.ListIterator;
import java.util.Map;

import akka.actor.ActorRef;
import akka.actor.Props;
import com.egeio.core.config.Config;
import com.egeio.core.jobsystem.actors.AbstractJobAgent;
import com.egeio.core.jobsystem.actors.message.NewJobMessage;
import com.egeio.services.transformer.models.ConversionJob;
import org.dom4j.Attribute;
import org.dom4j.Document;
import org.dom4j.Element;

public class ConversionJobDispatcherAgent
        extends AbstractJobAgent<ConversionJob> {
    // for WinWorkManager
    private static final int DEFAULT_MAX_ALLOWED_WORKER_COUNT = 1;
    private static final int DEFAULT_MAX_ALLOWED_CALLED_TIMES = -1; // -1 means
                                                                    // infinite
    private static final long DEFAULT_PROCESS_TIMEOUT = 300000;

    private Map<String, Element> ext2WorkerMappings;

    private Map<String, Element> id2Tasks = new HashMap<>();
    private Map<String, Boolean> id2TasksInited = new HashMap<>();

    private HashMap<String, ActorRef> name2WorkManager = new HashMap<String, ActorRef>();

    public ConversionJobDispatcherAgent(Element e) {
        super(e);
    }

    @Override
    public void realPreStart() throws Exception {

        logger.info(uuid, "STARTUP: [agent: {}]", getPath());

        super.realPreStart();

        loadConversionMapping();

        initWorkManagers();
    }

    @Override
    public void postStop() throws Exception {
        logger.info(uuid, "STOP: [agent: {}]", getPath());
        super.postStop();
    }

    @SuppressWarnings("unchecked")
    public boolean onMessageReceive(Object message) throws Exception {
        if (message instanceof NewJobMessage) {
            for (ConversionJob job : ((NewJobMessage<ConversionJob>) message)
                    .getJobs()) {
                logger.info(job.getUUID(), "agent receives new job");
                this.getContext()
                        .actorOf(Props.create(ConversionMainWorker.class, job,
                                ext2WorkerMappings.get(job.getExtension()),
                                this.webClient, name2WorkManager));
            }
        }
        else {
            return false;
        }
        return true;
    }

    @SuppressWarnings("rawtypes")
    private void copyElementsViaConversionKind(List elements) {
        if (elements != null) {
            for (Object e : elements) {
                if (e instanceof Element) {
                    Element element = Element.class.cast(e);
                    String kind = element.attributeValue("convertKind");
                    String[] realKinds = kind.split(",");
                    Element parent = element.getParent();
                    // remove the origin child first
                    if (parent != null) {
                        parent.remove(element);
                    }
                    for (String realKind : realKinds) {
                        Element realElement = element.createCopy();
                        realElement.attribute("convertKind")
                                .setValue(realKind.trim());
                        // add the copied the element back
                        if (parent != null) {
                            parent.add(realElement);
                        }
                        // deal the children
                        copyElementsViaConversionKind(realElement.elements());
                    }
                }
            }
        }
    }

    private void copyElementsViaId(List<Element> elements) {
        if (elements != null) {
            for (Object e : elements) {
                if (e instanceof Element) {
                    Element element = Element.class.cast(e);
                    Element parent = element.getParent();
                    parent.remove(element);
                    parent.add(getTaskExpandedById(element));
                }
            }
        }
    }

    private Element getTaskById(String id) {
        if (id2TasksInited.get(id)) {
            return id2Tasks.get(id).createCopy();
        }
        else {
            Element task = id2Tasks.get(id).createCopy();
            expandChildren(task);
            id2TasksInited.put(id, Boolean.TRUE);
            id2Tasks.put(id, task);
            return task.createCopy();
        }
    }

    @SuppressWarnings("unchecked")
    private void expandChildren(Element task) {
        List<Element> children = task.elements();
        if (children != null) {
            ListIterator<Element> it = children.listIterator();
            while (it.hasNext()) {
                Element child = it.next();
                it.set(getTaskExpandedById(child));
            }
        }
    }

    private Element getTaskExpandedById(Element task) {
        String id = task.attributeValue("id");
        Element realTask = task;
        if (id != null && !id.isEmpty()) {
            realTask = getTaskById(id);
            boolean doInherit = true;
            // override attribute of reference
            for (Object attr : task.attributes()) {
                if (attr instanceof Attribute) {
                    Attribute attribute = (Attribute) attr;
                    if ("inherit".equals(attribute.getName())) {
                        doInherit = Boolean.parseBoolean(attribute.getValue());
                    }
                    realTask.addAttribute(attribute.getName(),
                            attribute.getValue());
                }
            }
            if (doInherit) {
                return realTask;
            }
            else {
                @SuppressWarnings("unchecked")
                List<Element> rmChildren = realTask.elements();
                if (rmChildren != null) {
                    for (Object child : rmChildren) {
                        if (child instanceof Element) {
                            realTask.remove((Element) child);
                        }
                    }
                }
                @SuppressWarnings("unchecked")
                List<Element> addChildren = task.elements();
                if (addChildren != null) {
                    for (Object child : addChildren) {
                        if (child instanceof Element) {
                            realTask.add(((Element) child).createCopy());
                        }
                    }
                }
            }

        }
        expandChildren(realTask);

        return realTask;

    }

    @SuppressWarnings("unchecked")
    private void initPresetTasks() throws Exception {
        Element tasksElement = Config.getConfig()
                .getElement("/configuration/conversion/tasks");
        if (tasksElement != null) {
            String filePath = tasksElement.attributeValue("file", null);
            List<Element> tasks;
            if (filePath != null) {
                Document doc = Config.loadConfigByFile(filePath);
                tasks = doc.selectNodes("/tasks/task");
            }
            else {
                tasks = tasksElement.elements("task");
            }

            for (Object t : tasks) {
                if (t instanceof Element) {
                    Element task = (Element) t;
                    String id = task.attributeValue("id");
                    id2Tasks.put(id, task);
                    id2TasksInited.put(id, Boolean.FALSE);
                }
            }

            for (String id : id2Tasks.keySet()) {
                if (!id2TasksInited.get(id)) {
                    getTaskById(id);
                }
            }
        }
    }

    @SuppressWarnings("unchecked")
    private void loadConversionMapping() throws Exception {

        initPresetTasks();

        this.ext2WorkerMappings = new HashMap<String, Element>();
        List<Element> mappings = Config.getConfig()
                .getElements("/configuration/conversion/mapping");

        for (Element mapping : mappings) {
            copyElementsViaId(mapping.elements());
            copyElementsViaConversionKind(mapping.elements());

            String[] exts = mapping.attributeValue("extensions").split(",");
            for (String ext : exts) {
                this.ext2WorkerMappings.put(ext, mapping);
            }
        }
    }

    @SuppressWarnings("unchecked")
    private void initWorkManagers() {
        List<Element> elements = element.elements("win-process-manager");
        for (Element e : elements) {
            String managerName = e.attributeValue("name");
            ActorRef workManager = initWorkManager(e);

            name2WorkManager.put(managerName, workManager);
        }
    }

    private ActorRef initWorkManager(Element e) {
        String managerName = e.attributeValue("name");

        String processName = Config.getString(e, "processName", null,
                "agent@processName");
        int maxAllowedWorkerCount = Config.getNumber(e, "maxAllowedWorkerCount",
                DEFAULT_MAX_ALLOWED_WORKER_COUNT,
                "agent@maxAllowedWorkerCount");
        int maxAllowedCalledTimes = Config.getNumber(e, "maxAllowedCalledTimes",
                DEFAULT_MAX_ALLOWED_CALLED_TIMES,
                "agent@maxAllowedCalledTimes");
        long processTimeout = Config.getNumber(e, "processTimeout",
                DEFAULT_PROCESS_TIMEOUT, "agent@processTimeout");

        ActorRef workManager = this.getContext()
                .actorOf(Props.create(WinWorkManager.class, managerName,
                        maxAllowedWorkerCount, maxAllowedCalledTimes,
                        processTimeout, processName));

        return workManager;
    }
}