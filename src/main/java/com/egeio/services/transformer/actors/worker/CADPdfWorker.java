package com.egeio.services.transformer.actors.worker;

import com.egeio.services.transformer.actors.messages.CompleteConversion;
import com.egeio.services.transformer.actors.messages.StartConversion;
import com.egeio.services.transformer.models.ConversionJob;
import com.egeio.services.transformer.models.ConversionStatus;
import com.egeio.services.transformer.preconvert.PreCAD2Pdf;

public class CADPdf<PERSON>orker extends CMDWorker {
    private PreCAD2Pdf preConvert;

    public CADPdfWorker(ConversionJob job, PreCAD2Pdf preConvert) {
        super(job, preConvert);
        this.preConvert = preConvert;
    }

    @Override
    public void realPreStart() {
        logger.info(job.getUUID(), "START: CAD Pdf [worker: {}]", getPath());

        this.getSelf().tell(new StartConversion(), null);
    }

    @Override
    public void postStop() {
        logger.info(job.getUUID(), "STOP: CAD Pdf [worker: {}]", getPath());
    }

    private void doJob() throws Exception {
        doTmpCmdJob(timeout);
    }

    @Override
    public boolean onMessageReceive(Object message) throws Exception {
        if (message instanceof StartConversion) {
            logger.info(job.getUUID(),
                    "start to convert from [source: {}] to [dest: {}]",
                    this.preConvert.getSourceFilePath(),
                    this.preConvert.getTargetFilePath());

            doJob();
            getSelf().tell(new CompleteConversion(preConvert,
                    ConversionStatus.CONVERTED, job), getSelf());
        }
        else if (message instanceof CompleteConversion) {
            this.getContext().parent().tell(message, getSelf());

            this.getContext().stop(this.getSelf());
        }
        else {
            return false;
        }
        return true;
    }
}
