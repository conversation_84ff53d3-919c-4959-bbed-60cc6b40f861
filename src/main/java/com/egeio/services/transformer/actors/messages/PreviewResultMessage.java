package com.egeio.services.transformer.actors.messages;

import com.egeio.core.utils.GsonUtils;
import com.egeio.core.webclient.message.WebResponseMessage;
import com.egeio.services.transformer.models.PreviewResult;
import com.google.gson.JsonSyntaxException;
import com.ning.http.client.Response;

public class PreviewResultMessage extends WebResponseMessage {
    private PreviewResult result;
    private static final long serialVersionUID = -8341447880255653540L;

    @Override
    public void setResponse(Response response) throws Exception {
        String body = response.getResponseBody();
        try {
            result = GsonUtils.getGson().fromJson(body, PreviewResult.class);
        }
        catch (JsonSyntaxException e) {
            throw new JsonSyntaxException("json format error! " + body, e);
        }
    }

    public PreviewResult getResult() {
        return result;
    }

    public void setResult(PreviewResult result) {
        this.result = result;
    }
}
