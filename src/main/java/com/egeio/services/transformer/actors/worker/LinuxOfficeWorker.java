package com.egeio.services.transformer.actors.worker;

import akka.actor.Cancellable;
import com.egeio.aspose.AsposeConverter;
import com.egeio.core.config.Config;
import com.egeio.core.jobsystem.actors.message.FailJobMessage;
import com.egeio.core.jobsystem.actors.message.MoveJobMessage;
import com.egeio.services.transformer.actors.messages.CompleteConversion;
import com.egeio.services.transformer.actors.messages.OfficePlatformMessage;
import com.egeio.services.transformer.actors.messages.StartConversion;
import com.egeio.services.transformer.actors.messages.TimeoutMessage;
import com.egeio.services.transformer.models.ConversionJob;
import com.egeio.services.transformer.models.ConversionStatus;
import com.egeio.services.transformer.models.OfficeConversionPlatform;
import com.egeio.services.transformer.models.OfficeType;
import com.egeio.services.transformer.preconvert.PreLinuxOffice2Pdf;
import scala.concurrent.duration.Duration;

import java.util.concurrent.TimeUnit;

public class LinuxOfficeWorker extends CMDWorker {
    private boolean windowsEnable = false;
    private PreLinuxOffice2Pdf preConvert;
    private OfficeType officeType;

    private Cancellable timeoutMessage;

    public LinuxOfficeWorker(ConversionJob job, PreLinuxOffice2Pdf preConvert) {
        super(job, preConvert);
        this.preConvert = preConvert;
    }

    @Override
    public void realPreStart() {
        logger.info(job.getUUID(), "START: Office [worker: {}]", getPath());

        windowsEnable = Config
                .getBoolean("/configuration/windows-office-converter",
                        "enabled", windowsEnable);

        timeoutMessage = getContext().system().scheduler()
                .scheduleOnce(Duration.create(timeout, TimeUnit.MILLISECONDS),
                        self(), new TimeoutMessage(job),
                        getContext().dispatcher(), self());

        getSelf().tell(new StartConversion(), getSelf());
    }

    @Override
    public void postStop() {
        if (timeoutMessage != null && !timeoutMessage.isCancelled()) {
            timeoutMessage.cancel();
        }
        logger.info(job.getUUID(), "STOP: Office [worker: {}]", getPath());
    }

    private void doAsposeConvert() throws Exception {
        logger.info(job.getUUID(),
                "aspose start to convert from [source: {}] to [dest: {}]",
                preConvert.getSourceFilePath(), preConvert.getTargetFilePath());
        getAgent()
                .tell(new OfficePlatformMessage(OfficeConversionPlatform.ASPOSE,
                        job), getSelf());

        // reserve 5 seconds for transfer to win platform
        doCmdJob(timeout - 5000);

        logger.info(job.getUUID(), "aspose convert success.");

        getSelf().tell(new CompleteConversion(preConvert,
                ConversionStatus.CONVERTED, job), getSelf());
    }

    private void tryAsposeConvert() throws Exception {
        try {
            doAsposeConvert();
        }
        catch (Exception e) {
            if (job.isTryAnotherWhenFail()) {
                job.setTryAnotherWhenFail(false);
                logger.warn(job.getUUID(),
                        "office worker failed, try to use windows worker", e);
                doWinConvert();
            }
            else {
                throw e;
            }
        }
    }

    private void doWinConvert() {
        getSelf().tell(new MoveJobMessage<ConversionJob>(job,
                officeType.getQueue(), 0), getSelf());
    }

    private void freshWinConvert() throws Exception {
        // file's content is text/html while extension is office
        if (AsposeConverter.tryAsposeExtensions.contains(job.getExtension())
                && AsposeConverter.isSupport(job.getExtension()) && !preConvert
                .getOriginalFileType().equals("text/html")) {
            tryAsposeConvert();
        }
        else {
            if (officeType == OfficeType.WORD && AsposeConverter
                    .isSupport(job.getExtension()) && AsposeConverter
                    .detectComments(preConvert.getSourceFilePath())) {
                tryAsposeConvert();
            }
            else {
                doWinConvert();
            }
        }
    }

    private void notSupport() {
        getSelf().tell(new CompleteConversion(preConvert,
                ConversionStatus.NOT_SUPPORT, job), getSelf());
    }

    private void convert() throws Exception {
        officeType = OfficeType.getTypeViaExt(job.getExtension());
        if (officeType == OfficeType.UNKNOWN) {
            notSupport();
            return;
        }

        if (!windowsEnable) {
            asposeConvert();
            return;
        }

        OfficeConversionPlatform lastPlatform = job.getLastPlatform();
        OfficeConversionPlatform targetPlatform = job.getTargetPlatform();
        int version = job.getVersion();

        switch (version) {
        case 2:
            doV2Convert(targetPlatform);
            break;
        default:
            doDefaultConvert(lastPlatform);
            break;
        }

    }

    private void doDefaultConvert(OfficeConversionPlatform lastPlatform)
            throws Exception {
        // always use win for ppt
        if (officeType == OfficeType.PPT) {
            doWinConvert();
        }
        else {
            if (lastPlatform == null
                    || lastPlatform == OfficeConversionPlatform.UNKNOWN) {

                freshWinConvert();
            }
            else {
                // if force convert, do the reverse, else do the same
                if (job.isForceConvert()) {
                    if (lastPlatform == OfficeConversionPlatform.ASPOSE) {
                        doWinConvert();
                    }
                    else {
                        tryAsposeConvert();
                    }
                }
                else {
                    if (lastPlatform == OfficeConversionPlatform.ASPOSE) {
                        tryAsposeConvert();
                    }
                    else {
                        doWinConvert();
                    }
                }

            }
        }
    }

    private void doV2Convert(OfficeConversionPlatform targetPlatform)
            throws Exception {
        switch (targetPlatform) {
        case ASPOSE:
            tryAsposeConvert();
            break;
        case WIN:
            doWinConvert();
            break;
        case UNKNOWN:
            freshWinConvert();
            break;
        default:
            freshWinConvert();
            break;
        }
    }

    private void asposeConvert() throws Exception {
        if (AsposeConverter.isSupport(job.getExtension())) {
            doAsposeConvert();
        }
        else {
            notSupport();
        }
    }

    @Override
    public boolean onMessageReceive(Object message) throws Exception {
        if (message instanceof StartConversion) {
            convert();
        }
        else if (message instanceof TimeoutMessage) {
            logger.error(job.getUUID(), "converter timeout");
            getSelf().tell(new CompleteConversion(preConvert,
                    ConversionStatus.FAILED, job), getSelf());
        }
        else if (message instanceof CompleteConversion) {
            this.getContext().parent().tell(message, getSelf());

            this.getContext().stop(this.getSelf());
        }
        else if (message instanceof FailJobMessage) {
            logger.error(job.getUUID(), "failed job");
            getAgent().tell(message, getSelf());
            this.getContext().stop(getSelf());
        }
        else {
            return false;
        }
        return true;
    }
}
