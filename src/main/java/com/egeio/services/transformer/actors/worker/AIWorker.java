package com.egeio.services.transformer.actors.worker;

import java.util.concurrent.TimeUnit;

import com.egeio.services.transformer.actors.messages.CompleteConversion;
import com.egeio.services.transformer.actors.messages.StartConversion;
import com.egeio.services.transformer.actors.messages.TimeoutMessage;
import com.egeio.services.transformer.models.ConversionJob;
import com.egeio.services.transformer.models.ConversionStatus;
import com.egeio.services.transformer.preconvert.PreAI2Image;

import akka.actor.Cancellable;
import scala.concurrent.duration.Duration;

public class AIWorker extends CMDWorker {

    private Cancellable timeoutMessage;

    public AIWorker(ConversionJob job, PreAI2Image preConvert) {
        super(job, preConvert);
    }

    @Override
    public void realPreStart() {
        logger.info(job.getUUID(), "START: AI [worker: {}]", getPath());
        this.getSelf().tell(new StartConversion(), null);

        timeoutMessage = getContext().system().scheduler().scheduleOnce(
                Duration.create(timeout, TimeUnit.MILLISECONDS), self(),
                new TimeoutMessage(job), getContext().dispatcher(), self());
    }

    @Override
    public void postStop() {
        logger.info(job.getUUID(), "STOP: AI [worker: {}]", getPath());

        if (timeoutMessage != null && !timeoutMessage.isCancelled()) {
            timeoutMessage.cancel();
        }
    }

    @Override
    protected void dealRetryFailed(Object message) {
        super.dealRetryFailed(message);
    }

    private void doJob() throws Exception {
        // Ai worker's timeout is managed by timeout message, not by cmd
        // executor.
        doCmdJob(timeout * 2);
    }

    @Override
    public boolean onMessageReceive(Object message) throws Exception {
        if (message instanceof StartConversion) {
            logger.info(job.getUUID(),
                    "start to convert from [source: {}] to [dest: {}]",
                    preConvert.getSourceFilePath(),
                    preConvert.getTargetFilePath());
            doJob();
            logger.info(job.getUUID(), "AI conversion completed");

            this.getContext().parent().tell(new CompleteConversion(preConvert,
                    ConversionStatus.CONVERTED, job), getSelf());
            this.getContext().stop(getSelf());
        }
        else if (message instanceof TimeoutMessage) {
            logger.info(job.getUUID(),
                    "convert from [source: {}] to [dest: {}] timeout",
                    preConvert.getSourceFilePath(),
                    preConvert.getTargetFilePath());

            this.getContext().parent().tell(message, getSelf());
            this.getContext().stop(getSelf());
        }
        else {
            return false;
        }

        return true;
    }
}
