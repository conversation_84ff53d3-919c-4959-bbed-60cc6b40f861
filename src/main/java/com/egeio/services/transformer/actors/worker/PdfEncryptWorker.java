package com.egeio.services.transformer.actors.worker;

import java.io.IOException;

import org.apache.commons.lang.RandomStringUtils;

import com.egeio.services.transformer.actors.AConversionBaseWorker;
import com.egeio.services.transformer.actors.messages.CompleteConversion;
import com.egeio.services.transformer.actors.messages.StartConversion;
import com.egeio.services.transformer.models.ConversionJob;
import com.egeio.services.transformer.models.ConversionStatus;
import com.egeio.services.transformer.preconvert.AbstractPreConvert;
import com.egeio.services.transformer.preconvert.PrePdfEncrypt;
import com.egeio.services.transformer.utils.TransformerConstants;
import com.itextpdf.kernel.pdf.EncryptionConstants;
import com.itextpdf.kernel.pdf.PdfDocument;
import com.itextpdf.kernel.pdf.PdfReader;
import com.itextpdf.kernel.pdf.PdfWriter;
import com.itextpdf.kernel.pdf.WriterProperties;

/**
 * Created by <PERSON><PERSON><PERSON> on 2017/9/6.
 */
public class PdfEncryptWorker extends AConversionBaseWorker {

    private String userPass;
    private String ownerPass;

    public PdfEncryptWorker(ConversionJob job, AbstractPreConvert preConvert) {
        super(job, preConvert);
        userPass = ((PrePdfEncrypt) preConvert).getPassword();
        ownerPass = ((PrePdfEncrypt) preConvert).getOwnerPassword();
        if (ownerPass == null || "".equals(ownerPass)) {
            ownerPass = RandomStringUtils
                    .randomAscii(TransformerConstants.DEFAULT_PASSWORD_LENGTH);
        }
    }

    @Override
    public boolean onMessageReceive(Object message) throws Exception {
        if (message instanceof StartConversion) {
            encryptPdf();
            this.getContext().parent().tell(new CompleteConversion(preConvert,
                    ConversionStatus.CONVERTED, job), getSelf());
            this.getContext().stop(this.getSelf());
        }
        else {
            return false;
        }
        return true;
    }

    @Override
    protected void realPreStart() throws Exception {
        logger.info(job.getUUID(), "START: Pdf Encrypt [worker: {}]",
                getPath());
        this.getSelf().tell(new StartConversion(), null);
    }

    @Override
    public void postStop() throws Exception {
        logger.info(job.getUUID(), "STOP: Pdf Encrypt [worker: {}]", getPath());
    }

    private void encryptPdf() throws IOException {
        WriterProperties prop = new WriterProperties();

        prop.setStandardEncryption(userPass.getBytes(), ownerPass.getBytes(), 0,
                EncryptionConstants.ENCRYPTION_AES_256);
        try (PdfReader reader = new PdfReader(preConvert.getSourceFilePath());
                PdfDocument pdfDoc = new PdfDocument(
                        reader.setUnethicalReading(true),
                        new PdfWriter(preConvert.getTargetFilePath(), prop))) {
            // PdfDocument close will write the document to PdfWriter
        }
    }

}
