package com.egeio.services.transformer.actors.messages;

import com.egeio.core.utils.GsonUtils;
import com.egeio.core.webclient.message.WebResponseMessage;
import com.egeio.services.transformer.models.PSPDFKitUploadResponse;
import com.egeio.services.transformer.models.PSPDFKitUploadResponse.PSPDFKitUploadResponseData;
import com.ning.http.client.Response;

public class PSPDFKitUploadedMessage extends WebResponseMessage {
    private static final long serialVersionUID = 8054248323810532617L;

    private PSPDFKitUploadResponseData pspdfkitUploadResponseData;

    public PSPDFKitUploadResponseData getPspdfkitUploadResponseData() {
        return pspdfkitUploadResponseData;
    }

    @Override
    public void setResponse(Response response) throws Exception {
        PSPDFKitUploadResponse pspdfkitUploadResponse = GsonUtils.getGson().fromJson(response.getResponseBody(), PSPDFKitUploadResponse.class);
        pspdfkitUploadResponseData = pspdfkitUploadResponse.getData();
    }
}
