package com.egeio.services.transformer.actors.worker;

import com.egeio.core.config.Config;
import com.egeio.services.transformer.actors.AConversionBaseWorker;
import com.egeio.services.transformer.actors.messages.CompleteConversion;
import com.egeio.services.transformer.actors.messages.StartConversion;
import com.egeio.services.transformer.models.ConversionJob;
import com.egeio.services.transformer.models.ConversionStatus;
import com.egeio.services.transformer.preconvert.AbstractPreConvert;
import com.egeio.services.transformer.utils.ImageUtils;

public class TrimImageWorker extends AConversionBaseWorker {
    private int border = 20;
    private String background = "black";

    public TrimImageWorker(ConversionJob job, AbstractPreConvert preConvert) {
        super(job, preConvert);
    }

    @Override
    public boolean onMessageReceive(Object message) throws Exception {
        if (message instanceof StartConversion) {
            ImageUtils.trimImage(preConvert.getSourceFilePath(),
                    preConvert.getTargetFilePath(), background, border,
                    job.getUUID());

            getAgent().tell(new CompleteConversion(preConvert,
                    ConversionStatus.CONVERTED, job), getSelf());
        }
        else {
            return false;
        }

        return true;
    }

    @Override
    public void realPreStart() {
        logger.info(job.getUUID(), "START: TrimImage [worker: {}]", getPath());

        border = Config.getNumber("/configuration/cad/border", border);
        background = Config.getString("/configuration/cad/background",
                background);

        this.getSelf().tell(new StartConversion(), null);
    }

    @Override
    public void postStop() {
        logger.info(job.getUUID(), "STOP: TrimImage [worker: {}]", getPath());
    }

}
