package com.egeio.services.transformer.actors.messages;

import com.egeio.core.jobsystem.actors.message.AbstractMessage;
import com.egeio.services.transformer.models.ConversionJob;
import com.egeio.services.transformer.models.OfficeConversionPlatform;

public class OfficePlatformMessage extends AbstractMessage {
    private static final long serialVersionUID = -2232955954302680675L;

    private OfficeConversionPlatform platform;
    private ConversionJob job;

    public OfficePlatformMessage(OfficeConversionPlatform platform, ConversionJob job) {
        this.platform = platform;
        this.job = job;
    }

    public OfficeConversionPlatform getPlatform() {
        return platform;
    }

    public void setPlatform(OfficeConversionPlatform platform) {
        this.platform = platform;
    }

    public ConversionJob getJob() {
        return job;
    }

    public void setJob(ConversionJob job) {
        this.job = job;
    }
}
