package com.egeio.services.transformer.actors.worker;

import com.egeio.services.transformer.actors.AConversionBaseWorker;
import com.egeio.services.transformer.actors.messages.CompleteConversion;
import com.egeio.services.transformer.actors.messages.StartConversion;
import com.egeio.services.transformer.models.ConversionJob;
import com.egeio.services.transformer.models.ConversionStatus;
import com.egeio.services.transformer.preconvert.AbstractPreConvert;
import com.egeio.services.transformer.preconvert.PreImageWatermark;
import com.egeio.services.transformer.utils.ImageUtils;

public class ImageWatermarkWorker extends AConversionBaseWorker {

    public ImageWatermarkWorker(ConversionJob job, AbstractPreConvert preConvert) {
        super(job, preConvert);
    }

    @Override
    public boolean onMessageReceive(Object message) throws Exception {
        if (message instanceof StartConversion) {
            // add watermark
            PreImageWatermark preImageWatermark = PreImageWatermark.class.cast(preConvert);
            ImageUtils.addWatermark(
                    preImageWatermark.getSourceFilePath(),
                    preImageWatermark.getRotateSourceFilePath(),
                    preImageWatermark.getTargetFilePath(),
                    preImageWatermark.getWatermarkInfo(),
                    uuid);

            this.getContext().parent().tell(new CompleteConversion(preImageWatermark,
                    ConversionStatus.CONVERTED, job), getSelf());
            this.getContext().stop(this.getSelf());
        }
        else {
            return false;
        }
        return true;
    }

    @Override
    protected void realPreStart() throws Exception {
        logger.info(job.getUUID(), "START: Image Watermark [worker: {}]", getPath());
        this.getSelf().tell(new StartConversion(), null);
    }

    @Override
    public void postStop() throws Exception {
        logger.info(job.getUUID(), "STOP: Image Watermark [worker: {}]", getPath());
    }
}
