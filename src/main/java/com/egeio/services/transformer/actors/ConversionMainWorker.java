package com.egeio.services.transformer.actors;

import akka.actor.ActorRef;
import akka.actor.Cancellable;
import akka.actor.Props;
import com.egeio.core.auth.domain.GuardUrl;
import com.egeio.core.auth.domain.WebRequest;
import com.egeio.core.auth.utils.GuardUtils;
import com.egeio.core.config.Config;
import com.egeio.core.encryption.EncryptUtils;
import com.egeio.core.jobsystem.actors.AJobWorker;
import com.egeio.core.jobsystem.actors.WebPostActor;
import com.egeio.core.jobsystem.actors.message.*;
import com.egeio.core.utils.GsonUtils;
import com.egeio.core.utils.Utils;
import com.egeio.core.web.entity.BucketInfo;
import com.egeio.core.webclient.AkkaWebClient;
import com.egeio.core.webclient.message.DownloadCompleted;
import com.egeio.services.transformer.ApplicationContextHolder;
import com.egeio.services.transformer.actors.messages.*;
import com.egeio.services.transformer.cache.CacheClient;
import com.egeio.services.transformer.fileio.LoadFileProxy;
import com.egeio.services.transformer.fileio.middle_preview.model.BizCallbackConfig;
import com.egeio.services.transformer.fileio.middle_preview.model.LoadFileQueryDto;
import com.egeio.services.transformer.fileio.middle_preview.model.PreviewCallbackConfig;
import com.egeio.services.transformer.fileio.model.DownloadInfo;
import com.egeio.services.transformer.fileio.model.UploadInfo;
import com.egeio.services.transformer.models.*;
import com.egeio.services.transformer.models.CompleteCallbackRequest.CompleteConvertInfo;
import com.egeio.services.transformer.preconvert.AbstractPreConvert;
import com.egeio.services.transformer.utils.FileTypeDetector;
import com.egeio.services.transformer.utils.ImageCompressUtils;
import com.egeio.services.transformer.utils.TransformerConstants;
import com.ning.http.client.FluentCaseInsensitiveStringsMap;
import com.ning.http.client.FluentStringsMap;
import com.ning.http.client.cookie.Cookie;
import org.apache.commons.io.FileUtils;
import org.apache.commons.io.IOUtils;
import org.dom4j.Element;
import scala.concurrent.duration.Duration;

import java.io.*;
import java.util.*;
import java.util.concurrent.TimeUnit;

/**
 * This class is used for base worker
 *
 * <AUTHOR> This class contains download, upload and prepare workdir
 *         Subclass need
 */

public class ConversionMainWorker extends AJobWorker<ConversionJob> {

    private static String TMP_CONVERSION_DIR = Config.getConfig()
            .getElement("/configuration/conversion/tmp-conversion-dir")
            .getText().trim();
    public static String EMPTY_PDF = Config
            .getString("/configuration/empty_pdf", "empty.pdf");

    private AkkaWebClient webClient;

    private Cancellable timeoutMessage;

    // The default convert kind for "root task"
    private static final String rootKind = "root";
    private File workingDir;

    // key is convert kind, value is task id
    // we can change version to invalidate old cache key
    private String previewCacheKeyVersion;
    private Map<String, String> taskIds;
    private Element rootTask;
    private List<Element> finalTaskList;
    private LinkedList<Element> waitingTaskList;
    private Element currentTask;
    private int finalTaskIndex = -1;
    private Set<String> forceUploadKinds = new HashSet<String>();

    private Map<String, Set<String>> skippedQueueToKind = new HashMap<String, Set<String>>();
    private Map<String, String> skippedKindToQueue = new HashMap<String, String>();

    // key is convert kind, value is precovert
    private Map<String, AbstractPreConvert> preconverts;

    private String webRoot;
    private Long webRootServiceId;
    private String completeUrl;
    private String checkPreviewUrl;
    private String updateStatusUrl;

    private File originalFile;
    private String originalFileType;

    private Map<String, ConversionStatus> convertResults;
    private FailJobMessage<ConversionJob> failedJobMessage = null;
    private ImageStatus imageStatus = new ImageStatus();
    private OfficeConversionPlatform officePlatform = OfficeConversionPlatform.UNKNOWN;
    private int successCount = 0;
    private Map<String, Integer> uploadFileCount;
    private Map<String, Long> uploadFileAggrSize;
    private boolean allConverted = false;
    private ArrayList<String> tasksDone = new ArrayList<>();

    protected CacheClient cacheClient;

    private long previewExpire;

    private long timeout;

    // 是否经历完整的任务链，用于统计打点
    private boolean isCompleteTransform = true;

    private HashMap<String, ActorRef> name2WorkManager;
    private ConversionWork currentDoingWork;
    private ActorRef currentWorkManager;

    private Map<String, Object> additionalInfos;

    private boolean cacheEnable = true;

    private BizCallbackConfig jobBizCallbackConfig = null;
    private LoadFileQueryDto jobLoadFileQueryDto = null;
    private PreviewCallbackConfig jobPreviewCallbackConfig = null;

    public ConversionMainWorker(ConversionJob job, Element element,
            AkkaWebClient webClient,
            HashMap<String, ActorRef> name2WorkManager) {
        super(job);
        this.webClient = webClient;
        this.rootTask = element;
        this.name2WorkManager = name2WorkManager;

        preconverts = new HashMap<String, AbstractPreConvert>();
        waitingTaskList = new LinkedList<Element>();
        convertResults = new HashMap<String, ConversionStatus>();
        uploadFileCount = new HashMap<String, Integer>();
        uploadFileAggrSize = new HashMap<>();
        additionalInfos = new HashMap<String, Object>();

        Map<String, Object> jobAdditionalInfos = job.getAdditionalInfos();
        if(jobAdditionalInfos != null) {
            if(jobAdditionalInfos.containsKey("is_complete_transform")){
                isCompleteTransform = (boolean) jobAdditionalInfos.get("is_complete_transform");
            }
            if(jobAdditionalInfos.containsKey("done_tasks")) {
                tasksDone = (ArrayList) jobAdditionalInfos.get("done_tasks");
            }
        }
    }

    private int getJobRetry(int defaultValue) {
        Object jobRetryCntObj = cacheClient.get(getJobRetryKey(job.getJobMd5()));
        int jobRetryCnt = defaultValue;
        if (jobRetryCntObj != null) {
            try {
                jobRetryCnt = Integer.parseInt(jobRetryCntObj.toString());
            }
            catch (Exception e) {

            }
        }

        return jobRetryCnt;
    }

    @Override
    public void realPreStart() throws Exception {
        logger.info(job.getUUID(), "START: Conversion Main [worker: {}]",
                getPath());
        logger.debug(job.getUUID(), "JOB DETAIL: {}", this.job);

        timeout = Config.getNumber("/configuration/timeout", 300000l);
        webRoot = Config.getConfig()
                .getElement("/configuration/web_client/web_root").getTextTrim();

        completeUrl = Config.getConfig()
                .getElement("/configuration/web_client/complete_callback")
                .getTextTrim();
        checkPreviewUrl = Config.getConfig()
                .getElement("/configuration/web_client/check_preview_url")
                .getTextTrim();
        updateStatusUrl = Config.getConfig()
                .getElement("/configuration/web_client/update_status_url")
                .getTextTrim();
        webRootServiceId = Config.getNumber(
                "/configuration/web_client/web_root_service_id", -1L);
        // update callback with job additionalInfo
        prepareCallbackAdditionalInfo();

        previewExpire = Config.getNumber("/configuration/memcache/expires",
                300000l);
        cacheClient = ApplicationContextHolder.getBean("cacheClient");
        previewCacheKeyVersion = Config
                .getString("/configuration/memcache/preview_cache_key_version",
                        "v1")
                .trim();
        cacheEnable = Config.getBoolean("/configuration/memcache", "enable",
                cacheEnable);
        taskIds = new HashMap<String, String>();

        for (String convertKind : job.getConvertMetas().keySet()) {
            taskIds.put(convertKind,
                    job.getConvertMetas().get(convertKind).getTaskId());
        }

        job.setJobMd5(EncryptUtils.md5DigestToString((job.getQueueName() + ":" + job.getData()).getBytes()));

        int jobRetryCnt = getJobRetry(-1);

        if (jobRetryCnt < 0) {
            cacheClient.set(getJobRetryKey(job.getJobMd5()), "1", TransformerConstants.JOB_RETRY_EXPIRE, TimeUnit.MILLISECONDS);
        }
        else {
            if(jobRetryCnt >= TransformerConstants.JOB_RETRY_LIMIT) {
                failedJobMessage = new FailJobMessage<ConversionJob>(job,
                        new Exception("job retry time reached limit"));

                updateStatus(taskIds, ConversionStatus.NOT_SUPPORT);

                sendFailMessage();
            }
            else {
                cacheClient.set(
                        getJobRetryKey(job.getJobMd5()),
                        String.valueOf(jobRetryCnt + 1),
                        TransformerConstants.JOB_RETRY_EXPIRE,
                        TimeUnit.MILLISECONDS
                );
            }
        }

        if (rootTask == null) {
            failedJobMessage = new FailJobMessage<ConversionJob>(job,
                    new Exception("no worker configured for "
                            + job.getExtension() + " files"));

            updateStatus(taskIds, ConversionStatus.NOT_SUPPORT);

            sendFailMessage();
        }
        else {
            finalTaskList = getTaskElements();
            this.self().tell(new JobStart(), null);
            timeoutMessage = getContext().system().scheduler().scheduleOnce(
                    Duration.create(timeout, TimeUnit.MILLISECONDS), self(),
                    new TimeoutMessage(job), getContext().dispatcher(), self());
        }

        // v2 specify target platform, will not try another when fail
        if (job.getVersion() == 2 && job
                .getTargetPlatform() != OfficeConversionPlatform.UNKNOWN) {
            job.setTryAnotherWhenFail(false);
        }
    }

    private void prepareCallbackAdditionalInfo() {
        Map<String, Object> additionalInfo = job.getAdditionalInfos();
        if (null != additionalInfo) {
            if (additionalInfo.containsKey(BizCallbackConfig.AdditionalInfo_Key_PREVIEW_BIZ_CALLBACK_CONFIG)) {
                Object gatewayConfig = additionalInfo.get(BizCallbackConfig.AdditionalInfo_Key_PREVIEW_BIZ_CALLBACK_CONFIG);
                try {
                    String gatewayConfigStr = GsonUtils.getGson().toJson(gatewayConfig);
                    // 序列化反序列化, 创建新对象, 保证线程安全
                    this.jobBizCallbackConfig = GsonUtils.getGson().fromJson(gatewayConfigStr, BizCallbackConfig.class);
                } catch (Exception e) {
                    logger.error(job.getUUID(), "Get field " + BizCallbackConfig.AdditionalInfo_Key_PREVIEW_BIZ_CALLBACK_CONFIG + " in job additionalInfo failed");
                }
            }
            if (additionalInfo.containsKey(PreviewCallbackConfig.AdditionalInfo_Key_PREVIEW_CALLBACK_CONFIG)) {
                Object previewCallbackConfig = additionalInfo.get(PreviewCallbackConfig.AdditionalInfo_Key_PREVIEW_CALLBACK_CONFIG);
                try {
                    String previewCallbackConfigStr = GsonUtils.getGson().toJson(previewCallbackConfig);
                    this.jobPreviewCallbackConfig = GsonUtils.getGson().fromJson(previewCallbackConfigStr, PreviewCallbackConfig.class);
                } catch (Exception e) {
                    logger.error(job.getUUID(), "Get field " + PreviewCallbackConfig.AdditionalInfo_Key_PREVIEW_CALLBACK_CONFIG + " in job additionalInfo failed");
                }
            }
            if (additionalInfo.containsKey(LoadFileQueryDto.AdditionalInfo_Key_LOADFILEQUERYDTO)) {
                Object fileQueryDto = additionalInfo.get(LoadFileQueryDto.AdditionalInfo_Key_LOADFILEQUERYDTO);
                try {
                    String fileQueryDtoStr = GsonUtils.getGson().toJson(fileQueryDto);
                    this.jobLoadFileQueryDto = GsonUtils.getGson().fromJson(fileQueryDtoStr, LoadFileQueryDto.class);
                } catch (Exception e) {
                    logger.error(job.getUUID(), "Get field " + LoadFileQueryDto.AdditionalInfo_Key_LOADFILEQUERYDTO + " in job additionalInfo failed");
                }
            }
        }

    }

    private void prepareWorkingDir() {
        workingDir = new File(new File(TMP_CONVERSION_DIR),
                UUID.randomUUID().toString());
        Utils.createFolder(workingDir);
    }

    private void convert(boolean directConvert) throws Exception {
        String kind = getCurrentConvertKind();
        ConversionStatus oldStatus = convertResults.get(kind);
        if (oldStatus != null) {
            currentTask = null;
            getSelf().tell(new StartConversion(), getSelf());
            return;
        }

        AbstractPreConvert preConvert = preparePreConvert();
        if (preConvert != null) {
            preConvert.setOriginalFileType(originalFileType);
        }
        if (job.isForceConvert() && !job.getConvertedKinds().contains(kind)) {
            directConvert = true;
        }
        if (directConvert || preConvert == null
                || !preConvert.isExactTarget()) {
            doConvert();
        }
        else {
            checkConvertResult();
        }
    }

    private void doOriginConvert() throws Exception {
        String convertKind = getCurrentConvertKind();

        AbstractPreConvert preConvert = preconverts.get(convertKind);

        CompleteConversion msg = new CompleteConversion(preConvert,
                ConversionStatus.IGNORE, job);

        getSelf().tell(msg, getSelf());
    }

    private void doDownloadConvert() throws Exception {
        String convertKind = getCurrentConvertKind();
        logger.info(job.getUUID(), "start download [kind: {}]", convertKind);
        AbstractPreConvert preConvert = preconverts.get(convertKind);

        DownloadInfo downloadInfo = new DownloadInfo(getBucket(), job,
                preConvert.getTargetFile().getName(), preConvert.getTargetFilePath(), getSelf(),
                PreviewFileDownloaded.class,
                PreviewFileDownloadFailed.class,
                webClient);

        downloadInfo.setConvertKind(convertKind);

        LoadFileProxy.doDownload(job, this.getContext(), webClient, downloadInfo);
    }

    private void doParentConvert() throws Exception {
        Element parentTask = currentTask.getParent();
        waitingTaskList.addLast(currentTask);
        currentTask = parentTask;
        getSelf().tell(new StartConversion(), getSelf());
    }

    private void doRootConvert() throws Exception {
        waitingTaskList.addLast(currentTask);
        currentTask = rootTask;
        getSelf().tell(new StartConversion(), getSelf());
    }

    private void doConvertJob(String currentKind, String parentKind,
            AbstractPreConvert preConvert, ConversionStatus parentStatus)
            throws Exception {
        if (parentKind == rootKind || parentStatus == ConversionStatus.IGNORE) {
            preConvert.setSourceFile(originalFile);
            preConvert.setSourceType(job.getExtension());
        }
        else {
            AbstractPreConvert parentPreconvert = preconverts.get(parentKind);
            preConvert.setSourceFile(parentPreconvert.getTargetFile());
            preConvert.setSourceType(parentPreconvert.getTargetType());
        }

        File sourceFile = preConvert.getSourceFile();

        if (sourceFile == null || !sourceFile.exists()) {
            self().tell(
                    new FailJobMessage<ConversionJob>(job, new Exception(
                            "No source file found for kind " + currentKind)),
                    getSelf());
        }
        else {
            logger.info(job.getUUID(), "start convert [kind: {}]", currentKind);
            if (sourceFile.length() == 0) {
                if (preConvert.getTargetType().equals("pdf")) {
                    doEmptyPdfConvert(preConvert);
                }
                else {
                    self().tell(new CompleteConversion(preConvert,
                            ConversionStatus.EMPTY, job), self());
                }
            }
            else {
                // 大jpg文件压缩处理，压缩至50m以内
                final int maxSize = 50 * 1024 * 1024;
                if (sourceFile.length() > maxSize) {
                    ImageCompressUtils.compressJpeg(sourceFile, maxSize);
                }

                String managerName = currentTask.attributeValue("workManager");
                boolean useWorkManager = false;
                if (managerName != null) {
                    currentWorkManager = name2WorkManager.get(managerName);
                    if (currentWorkManager != null) {
                        logger.info(job.getUUID(),
                                "send [job: {}] to [WinWorkManager: {}]",
                                job.getUUID().toString(),
                                currentWorkManager.path());
                        currentDoingWork = new ConversionWork(getSelf(),
                                Class.forName(currentTask
                                        .attributeValue("workClass")),
                                job, preConvert);
                        currentWorkManager.tell(
                                new NewWorkMessage4Manager(currentDoingWork),
                                getSelf());
                        useWorkManager = true;
                    }
                }

                if (!useWorkManager) {
                    this.getContext().actorOf(Props.create(
                            Class.forName(
                                    currentTask.attributeValue("workClass")),
                            job, preConvert));
                }
                tasksDone.add(getCurrentConvertKind());
            }
        }
    }

    private void addSkipKind(String kind, String transferQueue) {
        skippedKindToQueue.put(kind, transferQueue);

        Set<String> kinds = skippedQueueToKind.get(transferQueue);
        if (kinds == null) {
            kinds = new HashSet<String>();
            skippedQueueToKind.put(transferQueue, kinds);
        }
        kinds.add(kind);
    }

    private void doSkipJob(String convertKind, String transferQueue,
            AbstractPreConvert preConvert) {
        addSkipKind(convertKind, transferQueue);

        getSelf().tell(new CompleteConversion(preConvert,
                ConversionStatus.TRANSFER, job), getSelf());
    }

    private void doConvert() throws Exception {
        String currentKind = getCurrentConvertKind();
        if (currentKind == rootKind) {
            getSelf().tell(new DownloadStarted(), getSelf());
            return;
        }

        AbstractPreConvert preConvert = preconverts.get(currentKind);
        String parentKind = getParentConvertKind(currentTask);
        String transferQueue = currentTask.attributeValue("transferQueue");

        if (parentKind == rootKind && transferQueue != null
                && !job.getQueueName().equals(transferQueue)) {
            doSkipJob(currentKind, transferQueue, preConvert);
            return;
        }

        ConversionStatus parentStatus = convertResults.get(parentKind);
        if (parentStatus == null) {
            doParentConvert();
        }
        else if (parentStatus.isFail()) {
            if (parentStatus == ConversionStatus.EMPTY
                    && preConvert.getTargetType().equals("pdf")) {
                doEmptyPdfConvert(preConvert);
            }
            else {
                getSelf().tell(
                        new CompleteConversion(preConvert, parentStatus, job),
                        getSelf());
            }
        }
        else if (parentStatus == ConversionStatus.IGNORE
                && (originalFile == null || !originalFile.exists())) {
            doRootConvert();
        }
        else if (parentStatus == ConversionStatus.TRANSFER) {
            String parentQueue = skippedKindToQueue.get(parentKind);
            addSkipKind(currentKind, parentQueue);
            getSelf().tell(
                    new CompleteConversion(preConvert, parentStatus, job),
                    getSelf());
        }
        else {
            if (transferQueue == null
                    || job.getQueueName().equals(transferQueue)) {
                doConvertJob(currentKind, parentKind, preConvert, parentStatus);
            }
            else {
                doSkipJob(currentKind, transferQueue, preConvert);
            }
        }
    }

    private void startConversion() throws Exception {
        boolean dependencyResolved;
        if (currentTask != null) {
            dependencyResolved = false;
        }
        else {
            if (waitingTaskList.isEmpty()) {
                if (finalTaskIndex < finalTaskList.size() - 1) {
                    currentTask = finalTaskList.get(++finalTaskIndex);
                    dependencyResolved = false;
                }
                else {
                    allConverted = true;

                    if (uploadFileCount.keySet().size() == successCount) {
                        getSelf().tell(new CompleteJobStart(), getSelf());
                    }
                    return;
                }
            }
            else {
                currentTask = waitingTaskList.pollLast();
                dependencyResolved = true;
            }
        }

        convert(dependencyResolved);
    }

    @SuppressWarnings("unchecked")
    @Override
    public boolean onMessageReceive(Object message) throws Exception {
        // start work from downloading file
        if (message instanceof JobStart) {
            logger.info(job.getUUID(), "new conversion job");
            prepareWorkingDir();

            updateStatus(taskIds, JobStatus.STARTED);

            if (job.needCheck()) {
                job.checkJob(webClient, this, getSelf());
            }
            else {
                this.getSelf().tell(new StartConversion(), getSelf());
            }
        }
        else if (message instanceof CheckJobCompleted) {
            CheckJobCompleted msg = CheckJobCompleted.class.cast(message);
            job.assignCheckResult(msg.getResult());

            this.getSelf().tell(new StartConversion(), getSelf());
        }
        else if (message instanceof StartConversion) {
            startConversion();
        }
        else if (message instanceof PreviewResultMessage) {
            PreviewResultMessage msg = PreviewResultMessage.class.cast(message);
            if (msg.getResult().getStatus() == PreviewStatus.NOT_READY) {
                doConvert();
            }
            else if (msg.getResult().getStatus() == PreviewStatus.ORIGIN) {
                doOriginConvert();
            }
            else {
                doDownloadConvert();
            }
        }
        else if (message instanceof DownloadStarted) {
            downloadFile();
            Element finalTask = finalTaskList.get(finalTaskIndex);
            String convertKind = finalTask.attributeValue("convertKind");
            String taskId = taskIds.get(convertKind);
            updateStatus(taskId, JobStatus.DOWNLOADING);
        }
        else if (message instanceof PreviewFileDownloaded) {
            String convertKind = getCurrentConvertKind();

            // 如果有现成的预览件并且不是在windows平台转好的类型
            if(!job.getConvertedKinds().contains(convertKind)) {
                isCompleteTransform = false;
            }

            AbstractPreConvert preConvert = preconverts.get(convertKind);

            CompleteConversion msg = new CompleteConversion(preConvert,
                    ConversionStatus.UPLOADED, job);

            getSelf().tell(msg, getSelf());
        }
        else if (message instanceof DownloadCompleted) {
            logger.info(job.getUUID(), "download completed");
            updateStatus(taskIds, JobStatus.DOWNLOADED);
            parseOriginFile();
        }
        // must be ahead of download failed message
        else if (message instanceof PreviewFileDownloadFailed) {
            PreviewFileDownloadFailed msg = PreviewFileDownloadFailed.class
                    .cast(message);
            failedJobMessage = new FailJobMessage<ConversionJob>(job,
                    msg.getCause());
            if (job.getConvertedKinds().contains(getCurrentConvertKind())) {
                logger.error(job.getUUID(), "download preview file failed",
                        msg.getCause());
                getSelf().tell(
                        new FailJobMessage<ConversionJob>(job, msg.getCause()),
                        getSelf());
            }
            else {
                logger.warn(job.getUUID(), "download preview file failed",
                        msg.getCause());
                doConvert();
            }
        }
        else if (message instanceof DownloadFailed) {
            DownloadFailed msg = DownloadFailed.class.cast(message);
            failedJobMessage = new FailJobMessage<ConversionJob>(job,
                    msg.getCause());
            logger.error(job.getUUID(), "download file failed", msg.getCause());
            getSelf().tell(
                    new CompleteConversion(rootKind, originalFile,
                            job.getExtension(), ConversionStatus.FAILED, job),
                    getSelf());
        }
        else if (message instanceof UploadFailed) {
            UploadFailed msg = UploadFailed.class.cast(message);
            failedJobMessage = new FailJobMessage<ConversionJob>(job,
                    msg.getCause());
            logger.error(job.getUUID(), "upload file failed", msg.getCause());
            getSelf().tell(new UploadFailJobMessage(job, msg.getCause(),
                    msg.getConvertKind()), getSelf());
        }
        else if (message instanceof OfficePlatformMessage) {
            OfficePlatformMessage msg = OfficePlatformMessage.class
                    .cast(message);
            if (msg.getPlatform() != null
                    && msg.getPlatform() != OfficeConversionPlatform.UNKNOWN) {
                officePlatform = msg.getPlatform();
            }
        }
        // when complete converting one part, continue the next part
        else if (message instanceof CompleteConversion) {
            CompleteConversion msg = CompleteConversion.class.cast(message);
            String currentKind = msg.getConvertKind();

            updateStatus(currentKind, taskIds.get(currentKind),
                    msg.getStatus());

            if (msg.getStatus().isFail()) {
                String exception = String.format(
                        "conversion kind %s failed, status %s", currentKind,
                        msg.getStatus().toString());
                if (failedJobMessage == null
                        && msg.getStatus() != ConversionStatus.TOO_LARGE) {
                    failedJobMessage = new FailJobMessage<ConversionJob>(job,
                            new IOException(exception));
                }
                logger.error(job.getUUID(), exception);
            }
            else if (currentKind != rootKind) {
                ConversionMetainfo meta = job.getConvertMetas()
                        .get(msg.getConvertKind());
                boolean upload = Config.getBoolean(currentTask, "upload", false,
                        "task@upload");
                if (upload) {
                    forceUploadKinds.add(currentKind);
                }

                if (msg.getStatus() == ConversionStatus.CONVERTED) {
                    if (meta != null || upload) {
                        AbstractPreConvert preconvert = preconverts
                                .get(msg.getConvertKind());
                        List<File> targetFiles = getTargetFiles(preconvert);
                        long totalSize = 0;
                        for (File f : targetFiles) {
                            totalSize += f.length();
                        }
                        uploadFileAggrSize.put(msg.getConvertKind(),
                                totalSize);
                        BucketInfo bucket = getBucket();
                        successCount++;

                        UploadInfo uploadInfo = new UploadInfo(bucket, getBucketType().getStr(), job, targetFiles,
                                getSelf(), msg.getConvertKind(), webClient,
                                UploadCompleted.class, UploadFailed.class);
                        LoadFileProxy.doUpload(job, getContext(), webClient, uploadInfo);
                    }
                    else {
                        updateStatus(currentKind, taskIds.get(currentKind),
                                ConversionStatus.UPLOADED);
                    }
                }

                imageStatus.assign(msg.getImageStatus());
                if (msg.getStatus() == ConversionStatus.TRANSFER) {
                    logger.info(job.getUUID(), "skipped convert [kind: {}]",
                            msg.getConvertKind());
                }
                else {
                    logger.info(job.getUUID(), "finished convert [kind: {}]",
                            msg.getConvertKind());
                }
            }
            Map<String, Object> msgAddtionalInfos = msg.getAdditionalInfos();
            if (msgAddtionalInfos != null) {
                for (String key : msgAddtionalInfos.keySet()) {
                    additionalInfos.put(key, msgAddtionalInfos.get(key));
                }
            }
            currentTask = null;
            currentDoingWork = null;
            currentWorkManager = null;
            this.getSelf().tell(new StartConversion(), getSelf());
        }
        else if (message instanceof UploadCompleted) {
            UploadCompleted msg = UploadCompleted.class.cast(message);
            uploadFileCount.put(msg.getKind(), msg.getFileCount());

            ConversionStatus status = ConversionStatus.UPLOADED;

            updateStatus(msg.getKind(), taskIds.get(msg.getKind()), status);

            if (allConverted
                    && (uploadFileCount.keySet().size() == successCount)) {
                getSelf().tell(new CompleteJobStart(), getSelf());
            }
        }
        // start upload
        else if (message instanceof CompleteJobStart) {
            completeCallback();
        }
        // complete upload
        else if (message instanceof CompleteJobFinish) {
            logger.info(job.getUUID(), "finished complete callback");

            sendTransferMessage();
            removeSuccessKinds();
            this.getSelf().tell(new CompleteAll(), getSelf());
        }
        // job finished
        else if (message instanceof CompleteAll) {
            if (job.getConvertMetas().size() == 0) {
                sendCompleteMessage();
            }
            else {
                sendFailMessage();
            }
        }
        else if ((message instanceof TimeoutMessage)) {

            if (message instanceof TimeoutMessage) {
                failedJobMessage = new FailJobMessage<ConversionJob>(job,
                        new Exception("Job timeout in MainWorker"));
                logger.error(job.getUUID(), "timeout");
            }
            else {
                failedJobMessage = new FailJobMessage<ConversionJob>(job,
                        new Exception("Job timeout in conversion step"));
                logger.error(job.getUUID(), "conversion timeout");
            }

            updateStatus(taskIds, ConversionStatus.FAILED);

            completeCallback();
        }
        // if job failed, throw exception
        else if (message instanceof FailJobMessage) {
            String currentKind = getCurrentConvertKind();

            failedJobMessage = FailJobMessage.class.cast(message);

            if (currentKind == null) {
                String exception = String.format("failed conversion job");
                logger.error(job.getUUID(), exception,
                        failedJobMessage.getCause());

                updateStatus(taskIds, ConversionStatus.FAILED);

                sendFailMessage();
            }
            else {
                String exception = String.format("failed conversion kind %s",
                        currentKind);
                logger.error(job.getUUID(), exception,
                        failedJobMessage.getCause());

                updateStatus(currentKind, taskIds.get(currentKind),
                        ConversionStatus.FAILED);

                getSelf().tell(new StartConversion(), getSelf());
            }
        }
        else {
            return false;
        }
        return true;
    }

    @Override
    public void postStop() {
        if (timeoutMessage != null && !timeoutMessage.isCancelled()) {
            timeoutMessage.cancel();
        }

        if (currentDoingWork != null && currentWorkManager != null) {
            currentWorkManager.tell(
                    new CancelWorkMessage4Manager(currentDoingWork), getSelf());
        }

        clean();

        logger.info(job.getUUID(), "STOP: Conversion Main [worker: {}]",
                getPath());
    }

    private Bucket getBucketType() {
        Bucket type = Bucket.PREVIEW;
        if (currentTask != null) {
            String bucketStr = currentTask.attributeValue("bucket");
            if (bucketStr != null) {
                type = Bucket.getBucket(bucketStr);
            }
        }
        return type;
    }

    public BucketInfo getBucket() {
        Bucket b = getBucketType();

        switch (b) {
        case PREVIEW:
            return job.getPreviewBucketInfo();
        case THUMBNAIL:
            return job.getThumbnailBucketInfo();
        case WATERFLOW:
            return job.getWaterFlowBucketInfo();
        case TMP:
            return job.getTmpBucketInfo();
        case WATERMARK_PREVIEW:
            return job.getWatermarkPreviewBucketInfo();
        }

        return null;
    }

    private void doEmptyPdfConvert(AbstractPreConvert preConvert)
            throws IOException {
        try (InputStream input = getClass().getClassLoader()
                .getResourceAsStream(EMPTY_PDF);
                OutputStream output = new FileOutputStream(
                        preConvert.getTargetFile())) {
            IOUtils.copy(input, output);
        }
        officePlatform = OfficeConversionPlatform.ASPOSE;

        self().tell(new CompleteConversion(preConvert,
                ConversionStatus.CONVERTED, job), self());
    }

    private String getCurrentConvertKind() {
        if (currentTask == null) {
            return null;
        }
        else if (currentTask == rootTask) {
            return rootKind;
        }
        else {
            return currentTask.attributeValue("convertKind");
        }
    }

    private void checkConvertResult() throws Exception {
        String currentKind = getCurrentConvertKind();
        PreviewStatusRequest request = new PreviewStatusRequest();
        request.setConvertKind(currentKind);
        request.setFileStorageId(job.getFileStorageId());
        request.setUserId(job.getUserId());

        ConversionMetainfo meta = job.getConvertMetas().get(currentKind);
        WatermarkInfo watermarkInfo = null;
        if (meta != null) {
            // set watermark info
            watermarkInfo = meta.getWatermarkInfo();
        }
        if(watermarkInfo == null && job.getGlobalMeta() != null) {
            watermarkInfo = job.getGlobalMeta().getWatermarkInfo();
        }
        request.setWatermarkInfo(watermarkInfo);

        String requestBody = GsonUtils.getGson().toJson(request);

        if(jobBizCallbackConfig !=null && jobLoadFileQueryDto!=null && !jobBizCallbackConfig.getCheckPreviewUrl().trim().isEmpty()) {
            // 根据job中additional_info回调发送
            FluentCaseInsensitiveStringsMap appendHeaders = this.jobLoadFileQueryDto.getNettyHeaders();
            // guardurl会在com.egeio.core.web.WebClient的send的时候在BoundRequestBuilder的header中加上鉴权信息
            // 这里先复用, 虽然鉴权不适用, 唯一需要注意的是key名称不要冲突
            this.getContext()
                    .actorOf(Props.create(WebPostActor.class,
                            new WebRequest(new GuardUrl(jobBizCallbackConfig.getCheckPreviewUrl(), webRootServiceId),
                                    (FluentStringsMap) null, (FluentCaseInsensitiveStringsMap) appendHeaders, requestBody, (Cookie[]) null),
                            getSelf(), webClient, PreviewResultMessage.class,
                            job.getUUID()));
        }
        else {
            this.getContext()
                    .actorOf(Props.create(WebPostActor.class, requestBody,
                            GuardUtils.getWebAppGuardUrl(webRoot, checkPreviewUrl,
                                    webRootServiceId),
                            getSelf(), webClient, PreviewResultMessage.class,
                            job.getUUID()));
        }
    }

    private AbstractPreConvert preparePreConvert() throws Exception {
        String convertKind = getCurrentConvertKind();

        if (convertKind == rootKind) {
            return null;
        }

        AbstractPreConvert preConvert = (AbstractPreConvert) Class
                .forName(currentTask.attributeValue("argsClass")).newInstance();
        if(preConvert.isWebClientNeeded()) {
            preConvert.setWebClient(webClient);
        }

        ConversionMetainfo meta = job.getConvertMetas().get(convertKind);

        preConvert.prepareConversion(job, convertKind, meta, job.getGlobalMeta(), this.workingDir);

        preconverts.put(convertKind, preConvert);
        return preConvert;
    }

    @Override
    protected void dealRetryFailed(Object message) {
        try {
            updateStatus(taskIds, ConversionStatus.FAILED);
        }
        catch (Exception e) {
            logger.error(job.getUUID(), "update failed status failed", e);
        }

        super.dealRetryFailed(message);
    }

    private List<File> getTargetFiles(AbstractPreConvert preConvert)
            throws IOException {
        List<File> result;
        File targetFile = preConvert.getTargetFile();
        File[] files = workingDir.listFiles(preConvert.getFileFilter());
        if (files.length == 0) {
            throw new IOException("No target file found!");
        }

        if (preConvert.isExactTarget()) {
            if (!targetFile.exists()) {
                Utils.rename(files[0], targetFile);
            }
            else {
                if (!targetFile.equals(files[0])) {
                    throw new IOException("target not match");
                }
            }
            result = new ArrayList<File>();
            result.add(targetFile);
        }
        else {
            result = new ArrayList<File>();
            if (files.length == 1) {
                File source = files[0];
                File target = preConvert.getSingleTargetFile();
                if (target != null) {
                    Utils.rename(source, target);
                    result.add(target);
                }
                else {
                    result.add(source);
                }
            }
            else {
                for (File f : files) {
                    result.add(f);
                }
            }
        }

        return result;
    }

    private String getParentConvertKind(Element e) {
        String parentConvertKind = null;

        if (e != null) {
            e = e.getParent();
            if (e != null) {
                if (e != rootTask) {
                    parentConvertKind = e.attributeValue("convertKind");
                }
                else {
                    parentConvertKind = rootKind;
                }
            }
        }

        return parentConvertKind;
    }

    private void clean() {
        if (this.workingDir != null) {
            try {
                FileUtils.deleteDirectory(this.workingDir);
                logger.info(job.getUUID(), "Clean working [dest: {}] directory",
                        this.workingDir.getAbsolutePath());
            }
            catch (IOException e) {
                logger.error(job.getUUID(), e,
                        "Clean working directory failed [{}]",
                        this.workingDir.getAbsolutePath());
            }
        }
    }

    private void downloadFile() throws Exception {
        this.originalFile = new File(this.workingDir,
                UUID.randomUUID().toString() + "." + this.job.getExtension());

        DownloadInfo downloadInfo = new DownloadInfo(job.getFileBucketInfo(), job,
                job.getUniqueName(), originalFile.getAbsolutePath(),
                getSelf(), webClient);
        downloadInfo.setConvertKind("file"); // origin file
        LoadFileProxy.doDownload(job, this.getContext(), webClient, downloadInfo);
    }

    private void parseOriginFile() throws IOException {
        originalFileType = FileTypeDetector.checkTypeByExtension(
                originalFile.getAbsolutePath(), job.getExtension(),
                job.getUUID());

        boolean isCorrectExt = FileTypeDetector
                .checkIfCorrectExtension(originalFileType, job.getExtension());
        ConversionStatus status = ConversionStatus.CONVERTED;

        boolean isProtect = FileTypeDetector
                .checkIfProtectExtension(originalFile.getAbsolutePath(),originalFileType, job.getExtension());
        if(isProtect){
            // mark it as failed
            logger.error(job.getUUID(), "protected file extension ");
            status = ConversionStatus.PROTECTED_FILE;
        }
        else if (!isCorrectExt) {
            // mark it as failed
            logger.error(job.getUUID(), "incorrect file extension");
            status = ConversionStatus.EXT_NOT_CORRECT;
        }

        getSelf().tell(new CompleteConversion(rootKind, originalFile,
                job.getExtension(), status, job), getSelf());
    }

    private Element findConvertKind(Element element, String convertKind) {
        String kind = element.attributeValue("convertKind");
        if (kind != null) {
            kind = kind.trim();
            if (kind.equals(convertKind)) {
                return element;
            }
        }

        @SuppressWarnings("rawtypes")
        List children = element.elements();
        if (children != null) {
            for (Object child : children) {
                Element result = findConvertKind(Element.class.cast(child),
                        convertKind);
                if (result != null) {
                    return result;
                }
            }
        }
        return null;
    }

    private List<Element> getTaskElements() throws Exception {
        List<Element> jobs = new LinkedList<Element>();

        for (String convertKind : job.getConvertMetas().keySet()) {
            Element find = findConvertKind(rootTask, convertKind);

            if (find != null
                    && find.attributeValue("convertKind").equals(convertKind)) {
                jobs.add(find);
            }
            else {
                String type = rootTask.attributeValue("type");
                String message = "there is no " + convertKind
                        + " task configured for type " + type;
                logger.error(job.getUUID(), message);
                failedJobMessage = new FailJobMessage<ConversionJob>(job,
                        new Exception(message));

                updateStatus(convertKind, taskIds.get(convertKind),
                        ConversionStatus.NOT_SUPPORT);
            }
        }
        return jobs;
    }

    /**
     * @throws Exception
     */
    private void completeCallback() throws Exception {
        CompleteCallbackRequest request = new CompleteCallbackRequest();
        Map<String, CompleteConvertInfo> convertInofs = new HashMap<>();

        for (String convertKind : convertResults.keySet()) {
            ConversionStatus status = convertResults.get(convertKind);
            ConversionMetainfo globalMeta = job.getGlobalMeta();
            if (!convertKind.equals(rootKind)
                    && status != ConversionStatus.TRANSFER) {
                ConversionMetainfo metainfo = job.getConvertMetas()
                        .get(convertKind);
                String taskId = null;
                PreviewType previewType = PreviewType.PREVIEW;
                WatermarkInfo watermarkInfo = null;

                if (metainfo != null) {
                    taskId = metainfo.getTaskId();
                    previewType = metainfo.getType();
                    watermarkInfo = metainfo.getWatermarkInfo();
                    if(watermarkInfo == null && globalMeta != null) {
                        watermarkInfo = globalMeta.getWatermarkInfo();
                    }
                }

                CompleteConvertInfo convertInfo = new CompleteConvertInfo();

                convertInfo.setType(previewType);
                convertInfo.setWatermarkInfo(watermarkInfo);
                Integer uploadCount = uploadFileCount.get(convertKind);
                if (uploadCount == null) {
                    uploadCount = 0;
                }
                convertInfo.setPageCount(uploadCount);
                Long uploadSize = uploadFileAggrSize.get(convertKind);
                if (uploadSize == null) {
                    uploadSize = 0L;
                }
                convertInfo.setViewFileSize(uploadSize);
                if (taskId != null || forceUploadKinds.contains(convertKind)) {
                    convertInfo.setTaskId(taskId);

                    if (status == null) {
                        status = ConversionStatus.UNKNOWN;
                    }
                    convertInfo.setStatus(status.getJobStatus());

                    convertInofs.put(convertKind, convertInfo);
                }
            }
        }

        if (convertInofs.size() > 0) {
            request.setFileStorageId(this.job.getFileStorageId());
            request.setConvertInfos(convertInofs);
            request.setImageStatus(imageStatus);
            request.setPlatform(officePlatform);
            request.setUserId(this.job.getUserId());
            additionalInfos.put("is_complete_transform", isCompleteTransform);
            additionalInfos.put("done_tasks", GsonUtils.getGson().toJson(tasksDone));
            request.setAdditionalInfos(additionalInfos);

            String callbackBody = GsonUtils.getGson().toJson(request);
            logger.debug(job.getUUID(), "complete callback [body: {}]",
                    callbackBody);
            if(jobBizCallbackConfig !=null && jobLoadFileQueryDto!=null && !jobBizCallbackConfig.getCompleteCallbackUrl().trim().isEmpty()){
                // 根据job中additional_info回调发送
                FluentCaseInsensitiveStringsMap appendHeaders = this.jobLoadFileQueryDto.getNettyHeaders();
                // guardurl会在com.egeio.core.web.WebClient的send的时候在BoundRequestBuilder的header中加上鉴权信息
                // 这里先复用, 虽然鉴权不适用, 唯一需要注意的是key名称不要冲突
                this.getContext()
                        .actorOf(Props.create(WebPostActor.class,
                                new WebRequest(new GuardUrl(jobBizCallbackConfig.getCompleteCallbackUrl(), webRootServiceId),
                                        (FluentStringsMap)null, (FluentCaseInsensitiveStringsMap)appendHeaders, callbackBody, (Cookie[])null),
                                getSelf(), webClient, CompleteJobFinish.class,
                                job.getUUID()));
            }
            else {
                this.getContext()
                        .actorOf(Props.create(WebPostActor.class, callbackBody,
                                GuardUtils.getWebAppGuardUrl(webRoot, completeUrl,
                                        webRootServiceId),
                                getSelf(), webClient, CompleteJobFinish.class,
                                job.getUUID()));
            }
        }
        else {
            getSelf().tell(new CompleteJobFinish(), getSelf());
        }
    }

    private String getPreviewStatusKey(String taskId) {
        return "Preview:" + previewCacheKeyVersion + ":" + taskId;
    }

    private void updateStatus(String convertKind, String taskId,
            ConversionStatus status) {
        updateStatus(taskId, status.getJobStatus());

        if (convertKind != null) {
            convertResults.put(convertKind, status);
        }
    }

    private void updateStatusLocal(String taskId, JobStatus status) {
        if (taskId != null) {
            String key = getPreviewStatusKey(taskId);
            Object oldStatusObj = cacheClient.get(key);
            boolean updateStatus = true;
            if (oldStatusObj != null) {
                JobStatus oldStatus = JobStatus
                        .getJobStatus(oldStatusObj.toString());
                if (oldStatus.getIndex() != JobStatus.UNKNOWN.getIndex()
                        && status.getIndex() <= oldStatus.getIndex()) {
                    updateStatus = false;
                }
            }

            if (updateStatus) {
                cacheClient.set(key, status.getStr(), previewExpire, TimeUnit.MILLISECONDS);
            }
        }
    }

    private void updateStatusRemote(String taskId, JobStatus status) {
        if (taskId == null) {
            return;
        }
        try {
            String callbackBody = GsonUtils.getGson()
                    .toJson(new CacheStatus(taskId, status));
            WebRequest webRequest = null;
            if (jobBizCallbackConfig != null && jobLoadFileQueryDto != null && !jobBizCallbackConfig.getUpdateStatusUrl().trim().isEmpty()) {
                FluentCaseInsensitiveStringsMap appendHeaders = this.jobLoadFileQueryDto.getNettyHeaders();
                webRequest = new WebRequest(new GuardUrl(jobBizCallbackConfig.getUpdateStatusUrl(), webRootServiceId),
                        (FluentStringsMap) null, (FluentCaseInsensitiveStringsMap) appendHeaders, callbackBody, (Cookie[]) null);

            } else {
                webRequest = new WebRequest(GuardUtils.getWebAppGuardUrl(webRoot, updateStatusUrl, webRootServiceId), callbackBody);
            }
            UpdateStatusResult resp = webClient
                    .execute(webRequest, UpdateStatusResult.class);
            if (resp.getError() != null || resp.getError() != null) {
                throw new Exception(resp.getError().toString());
            }
            logger.info(uuid, "update status success, task id: {}, status: {}",
                    taskId, status.getStr());
        }
        catch (Exception e) {
            logger.error(uuid, e, "update cache failed");
        }
    }

    private void updateStatus(String taskId, JobStatus status) {
        if (cacheEnable) {
            updateStatusLocal(taskId, status);
        } else {
            updateStatusRemote(taskId, status);
        }
        updateStatusToCallback(taskId, status);
    }

    private void updateStatus(Map<String, String> taskIds,
            ConversionStatus status) {
        for (String kind : taskIds.keySet()) {
            updateStatus(kind, taskIds.get(kind), status);
        }
    }

    private void updateStatus(Map<String, String> taskIds, JobStatus status) {
        for (String kind : taskIds.keySet()) {
            updateStatus(taskIds.get(kind), status);
        }
    }

    private void sendFailMessage() {
        logger.info(job.getUUID(), "failed convert_kinds [kinds: {}]",
                job.getConvertMetas().keySet());
        if (failedJobMessage == null) {
            failedJobMessage = new FailJobMessage<ConversionJob>(job,
                    new Exception("convert failed"));
        }

        getAgent().tell(failedJobMessage, null);

        clearJobRetryCnt();

        this.getContext().stop(this.self());
    }

    private void sendTransferMessage() throws CloneNotSupportedException {
        Set<String> convertedKinds = new HashSet<String>();
        for (String kind : convertResults.keySet()) {
            ConversionStatus status = convertResults.get(kind);
            if (!kind.equals(rootKind) && status != ConversionStatus.TRANSFER) {
                convertedKinds.add(kind);
            }
        }

        for (String transferQueue : skippedQueueToKind.keySet()) {
            logger.info(job.getUUID(), "transfer job to queue {}",
                    transferQueue);

            Map<String, ConversionMetainfo> skipMap = new HashMap<String, ConversionMetainfo>();
            for (String kind : skippedQueueToKind.get(transferQueue)) {
                ConversionMetainfo meta = job.getConvertMetas().get(kind);
                if (meta != null) {
                    skipMap.put(kind, meta);
                }
            }

            ConversionJob newJob = (ConversionJob) job.clone();
            newJob.setConvertMetas(skipMap);
            newJob.setConvertedKinds(convertedKinds);
            Map<String, Object> jobAdditionalInfos = newJob.getAdditionalInfos();
            if(jobAdditionalInfos == null) {
                jobAdditionalInfos = new HashMap<>();
            }
            jobAdditionalInfos.put("is_complete_transform", isCompleteTransform);
            jobAdditionalInfos.put("done_tasks", tasksDone);
            newJob.setAdditionalInfos(jobAdditionalInfos);

            getAgent().tell(new MoveJobMessage<ConversionJob>(newJob,
                    transferQueue, 0, false), getSelf());
        }
    }

    private void sendCompleteMessage() {
        logger.info(job.getUUID(), "complete job");

        getAgent().tell(new CompleteJobMessage<ConversionJob>(job),
                this.getSelf());

        clearJobRetryCnt();

        this.getContext().stop(this.self());
    }

    private void removeSuccessKinds() {
        for (String convertKind : convertResults.keySet()) {
            ConversionStatus status = convertResults.get(convertKind);
            if (!status.isFail() || status == ConversionStatus.TOO_LARGE) {
                job.rmConvertMeta(convertKind);
            }
        }
        for (String convertKind : skippedKindToQueue.keySet()) {
            job.rmConvertMeta(convertKind);
        }
    }

    private String getJobRetryKey(String md5) {
        return "Transformer_Job_Retry:" + md5;
    }

    private void clearJobRetryCnt() {
        cacheClient.delete(getJobRetryKey(job.getJobMd5()));
    }

    private void updateStatusToCallback(String taskId, JobStatus status) {
        try {
            String callbackBody = GsonUtils.getGson()
                    .toJson(new CacheStatus(taskId, status));
            if (jobBizCallbackConfig != null && jobLoadFileQueryDto != null && !jobBizCallbackConfig.getUpdateStatusUrl().trim().isEmpty()) {
                FluentCaseInsensitiveStringsMap appendHeaders = this.jobLoadFileQueryDto.getNettyHeaders();
                // netty默认content-type为application/octet-stream;charset=UTF-8
                appendHeaders.add("Content-Type", "application/json;charset=UTF-8");
                // 先向预览模块更新状态, 再回调业务系统
                WebRequest previewWebRequest = new WebRequest(new GuardUrl(jobPreviewCallbackConfig.getUpdateStatusUrl(), webRootServiceId),
                        (FluentStringsMap) null, (FluentCaseInsensitiveStringsMap) appendHeaders, callbackBody, (Cookie[]) null);
                UpdateStatusResult previewResp = webClient
                        .execute(previewWebRequest, UpdateStatusResult.class);
                if (previewResp.getError() != null || previewResp.getErrors() != null) {
                    logger.error(uuid, "update status to [ " + jobPreviewCallbackConfig.getUpdateStatusUrl() + " ] failed");
                }
            }
        } catch (Exception e) {
            logger.error(uuid, e, "update cache failed");
        }
    }
}
