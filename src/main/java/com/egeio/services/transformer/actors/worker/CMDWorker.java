package com.egeio.services.transformer.actors.worker;

import com.egeio.core.cmd.CmdExecute;
import com.egeio.core.config.Config;
import com.egeio.core.utils.Utils;
import com.egeio.services.transformer.actors.AConversionBaseWorker;
import com.egeio.services.transformer.actors.messages.CompleteConversion;
import com.egeio.services.transformer.actors.messages.StartConversion;
import com.egeio.services.transformer.models.ConversionJob;
import com.egeio.services.transformer.models.ConversionStatus;
import com.egeio.services.transformer.preconvert.AbstractPreConvert;
import com.egeio.services.transformer.preconvert.ICmdPreConvert;
import org.apache.commons.exec.CommandLine;

import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.OutputStream;

public class CMDWorker extends AConversionBaseWorker {
    protected long timeout = Config.getNumber("/configuration/timeout",
            600000l);

    protected boolean doEnableCMDStandardOutput = Config.getBoolean(
            "/configuration/cmd_worker_standard_output_enable", false);

    public CMDWorker(ConversionJob job, AbstractPreConvert preConvert) {
        super(job, preConvert);
    }

    @Override
    public void realPreStart() {
        logger.info(job.getUUID(), "START: CMD [worker: {}]", getPath());
        getContext().self().tell(new StartConversion(), null);
    }

    @Override
    public void postStop() {
        logger.info(job.getUUID(), "STOP: CMD [worker: {}]", getPath());
    }

    protected void doCmdJob(long timeout) throws Exception {
        ICmdPreConvert cmdPre = ICmdPreConvert.class.cast(preConvert);
        CommandLine cmd = cmdPre.getCMD();
        OutputStream out = null;
        if (doEnableCMDStandardOutput) {
            out = new ByteArrayOutputStream();
        }
        new CmdExecute().execute(cmd, preConvert.getWorkingDirPath(), timeout,
                out);
        if (doEnableCMDStandardOutput && out != null) {
            logger.info(uuid, "cmd worker standard output: {}",
                    out.toString());
        }

        if (new File(preConvert.getWorkingDirPath())
                .list(preConvert.getFileFilter()).length == 0) {
            throw new Exception("convert failed!");
        }
    }

    protected void doTmpCmdJob(long timeout) throws Exception {
        ICmdPreConvert cmdPre = ICmdPreConvert.class.cast(preConvert);
        CommandLine cmd = cmdPre.getCMD();
        new CmdExecute().execute(cmd, preConvert.getWorkingDirPath(), timeout);

        File tmpFile = preConvert.getTmpTargeFile();
        if (!tmpFile.exists()) {
            throw new Exception("convert failed!");
        }

        Utils.rename(tmpFile, preConvert.getTargetFile());

        if (new File(preConvert.getWorkingDirPath())
                .list(preConvert.getFileFilter()).length == 0) {
            throw new Exception("convert failed!");
        }
    }

    @Override
    public boolean onMessageReceive(Object message) throws Exception {
        if (message instanceof StartConversion) {
            logger.info(job.getUUID(),
                    "start to convert from [source: {}] to [dest: {}]",
                    this.preConvert.getSourceFilePath(),
                    this.preConvert.getTargetFilePath());

            doCmdJob(timeout);

            this.getContext().parent().tell(new CompleteConversion(preConvert,
                    ConversionStatus.CONVERTED, job), getSelf());
            this.getContext().stop(this.getSelf());
        }
        else {
            return false;
        }
        return true;
    }
}