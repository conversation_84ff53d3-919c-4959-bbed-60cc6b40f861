package com.egeio.services.transformer.actors.worker;

import akka.actor.Props;
import com.alibaba.fastjson.JSONObject;
import com.bimface.api.bean.request.translate.FileTranslateRequest;
import com.bimface.api.bean.request.translate.TranslateSource;
import com.bimface.api.bean.response.FileTranslateBean;
import com.bimface.exception.BimfaceException;
import com.bimface.sdk.BimfaceClient;
import com.egeio.core.auth.domain.GuardUrl;
import com.egeio.core.config.Config;
import com.egeio.core.log.Logger;
import com.egeio.core.log.LoggerFactory;
import com.egeio.services.transformer.actors.AConversionBaseWorker;
import com.egeio.services.transformer.actors.messages.BimfaceSubmittedMessage;
import com.egeio.services.transformer.actors.messages.CompleteConversion;
import com.egeio.services.transformer.actors.messages.StartConversion;
import com.egeio.services.transformer.models.ConversionJob;
import com.egeio.services.transformer.models.ConversionStatus;
import com.egeio.services.transformer.preconvert.AbstractPreConvert;
import com.egeio.services.transformer.preconvert.PreCAD2Bimface;
import com.egeio.services.transformer.utils.BimfaceClientFactory;
import org.apache.commons.exec.util.MapUtils;

import java.io.File;
import java.util.HashMap;
import java.util.Map;

public class CADBimfaceWorker extends AConversionBaseWorker {

    private static Logger logger = LoggerFactory
            .getLogger(CADBimfaceWorker.class);

    private Map<String, Object> additionalInfos = new HashMap<String, Object>();;
    private static final String BIMFACE_VIEW_ID = "bimface_view_id";
    private GuardUrl callbackGuardUrl;

    public CADBimfaceWorker(ConversionJob job, AbstractPreConvert preConvert) {
        super(job, preConvert);
        String callback = Config.getConfig()
                .getElement("/configuration/bimface/callback").getTextTrim();
        String host = Config.getConfig()
                .getElement("/configuration/bimface/host").getTextTrim();
        String callbackUrl = host + callback;

        // anonymous service without service_id
        this.callbackGuardUrl = new GuardUrl(callbackUrl);
    }

    @Override
    public void realPreStart() {
        logger.info(job.getUUID(), "START: CAD Bimface [worker: {}]",
                getPath());
        getContext().self().tell(new StartConversion(), null);
    }

    @Override
    public void postStop() {
        logger.info(job.getUUID(), "STOP: CAD Bimface [worker: {}]", getPath());
    }

    @Override
    public boolean onMessageReceive(Object message) throws Exception {
        if (message instanceof StartConversion) {
            PreCAD2Bimface preConvert = (PreCAD2Bimface) (this.preConvert);
            File sourceFile = preConvert.getSourceFile();
            this.getContext().actorOf(
                    Props.create(BimfaceUploadWorker.class, job, sourceFile));
        }
        else if (message instanceof BimfaceSubmittedMessage) {
            BimfaceSubmittedMessage realMessage = BimfaceSubmittedMessage.class
                    .cast(message);
            try {
                BimfaceClient client = BimfaceClientFactory.getClient();
                FileTranslateRequest fileTranslateRequest = new FileTranslateRequest();
                TranslateSource source = new TranslateSource();
                source.setFileId(realMessage.getBimfaceFileId());
                fileTranslateRequest.setSource(source);
                fileTranslateRequest.setCallback(callbackGuardUrl.getUrl());
//                Map<String, String> config = new HashMap<>();
//                config.put("bimtilesVersion","V3");
//                config.put("toBimtiles","true");
//                fileTranslateRequest.setConfig(config);
                logger.info(uuid,"client.translate2Bimtiles is start params:{}", JSONObject.toJSONString(fileTranslateRequest));
                FileTranslateBean translate = client.translate2Bimtiles(fileTranslateRequest);
                logger.info(uuid,"client.translate2Bimtiles is end  result:{}", JSONObject.toJSONString(translate));
                logger.info(uuid,
                        "bimface info: name: {}, bim file id: {}, reason: {}, thumbnail: {}, transfer status: {}",
                        translate.getName(), realMessage.getBimfaceFileId(),
                        translate.getReason(), translate.getThumbnail(),
                        translate.getStatus());
                additionalInfos.put(BIMFACE_VIEW_ID,
                        realMessage.getBimfaceFileId());
            }
            catch (BimfaceException e) {
                logger.error(uuid, "bimface error code: {}",
                        "" + e.getMessage());
                throw e;
            }

            getSelf().tell(
                    new CompleteConversion(preConvert,
                            ConversionStatus.UPLOADED, job, additionalInfos),
                    getSelf());

        }
        else if (message instanceof CompleteConversion) {
            this.getContext().parent().tell(message, getSelf());
            this.getContext().stop(this.getSelf());
        }
        else {
            return false;
        }
        return true;
    }
}
