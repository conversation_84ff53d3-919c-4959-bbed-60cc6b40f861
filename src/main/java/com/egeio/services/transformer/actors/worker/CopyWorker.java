package com.egeio.services.transformer.actors.worker;

import java.io.FileInputStream;
import java.io.FileOutputStream;

import org.apache.commons.io.IOUtils;

import com.egeio.services.transformer.actors.AConversionBaseWorker;
import com.egeio.services.transformer.actors.messages.CompleteConversion;
import com.egeio.services.transformer.actors.messages.StartConversion;
import com.egeio.services.transformer.models.ConversionJob;
import com.egeio.services.transformer.models.ConversionStatus;
import com.egeio.services.transformer.preconvert.AbstractPreConvert;

public class CopyWorker extends AConversionBaseWorker {
    public CopyWorker(ConversionJob job, AbstractPreConvert preConvert) {
        super(job, preConvert);
    }

    @Override
    public void realPreStart() {
        logger.info(job.getUUID(), "START: Copy [worker: {}]", getPath());
        getContext().self().tell(new StartConversion(), null);
    }

    @Override
    public void postStop() {
        logger.info(job.getUUID(), "STOP: Copy [worker: {}]", getPath());
    }

    @Override
    public boolean onMessageReceive(Object message) throws Exception {
        if (message instanceof StartConversion) {
            logger.info(job.getUUID(),
                    "start to convert from [source: {}] to [dest: {}]",
                    preConvert.getSourceFilePath(),
                    preConvert.getTargetFilePath());

            try (FileInputStream in = new FileInputStream(
                    preConvert.getSourceFile());
                    FileOutputStream out = new FileOutputStream(
                            preConvert.getTargetFile())) {
                IOUtils.copy(in, out);
            }

            this.getContext().parent().tell(new CompleteConversion(preConvert,
                    ConversionStatus.CONVERTED, job), getSelf());
            this.getContext().stop(this.getSelf());

        }
        else {
            return false;
        }
        return true;
    }

}
