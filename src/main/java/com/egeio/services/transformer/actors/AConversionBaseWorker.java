package com.egeio.services.transformer.actors;

import com.egeio.core.jobsystem.actors.AJobWorker;

import com.egeio.services.transformer.models.ConversionJob;
import com.egeio.services.transformer.preconvert.AbstractPreConvert;

public abstract class AConversionBaseWorker extends AJobWorker<ConversionJob> {
    protected AbstractPreConvert preConvert;

    public AConversionBaseWorker(ConversionJob job, AbstractPreConvert preConvert) {
        super(job);
        this.preConvert = preConvert;
    }
}
