package com.egeio.services.transformer.actors.messages;

import com.egeio.core.jobsystem.actors.message.AbstractMessage;
import com.egeio.services.transformer.models.ConversionWork;

public class NewWorkMessage4Manager extends AbstractMessage {

    private static final long serialVersionUID = -6261285839962161044L;
    private ConversionWork work;

    public NewWorkMessage4Manager(ConversionWork work) {
        super();
        this.work = work;
    }

    public ConversionWork getWork() {
        return work;
    }

}
