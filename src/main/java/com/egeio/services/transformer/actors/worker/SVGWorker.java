package com.egeio.services.transformer.actors.worker;

import com.egeio.core.log.Logger;
import com.egeio.core.log.LoggerFactory;
import com.egeio.services.transformer.actors.AConversionBaseWorker;
import com.egeio.services.transformer.actors.messages.CompleteConversion;
import com.egeio.services.transformer.actors.messages.StartConversion;
import com.egeio.services.transformer.models.ConversionFlag;
import com.egeio.services.transformer.models.ConversionJob;
import com.egeio.services.transformer.models.ImageStatus;
import com.egeio.services.transformer.models.ConversionStatus;
import com.egeio.services.transformer.models.ImageMeta;
import com.egeio.services.transformer.preconvert.AbstractPreConvert;
import com.egeio.services.transformer.preconvert.PreImage2Type;
import com.egeio.services.transformer.utils.ImageUtils;

public class SVGWorker extends AConversionBaseWorker {
    private static Logger logger = LoggerFactory.getLogger(SVGWorker.class);
    private ImageStatus imageStatus = new ImageStatus();

    public SVGWorker(ConversionJob job, AbstractPreConvert preConvert) {
        super(job, preConvert);
        imageStatus.setConversion1024(ConversionFlag.CONVERSION);
    }

    @Override
    public void realPreStart() {
        logger.info(job.getUUID(), "START: Svg [worker: {}]", getPath());
        getContext().self().tell(new StartConversion(), null);
    }

    @Override
    public void postStop() {
        logger.info(job.getUUID(), "STOP: Svg [worker: {}]", getPath());
    }

    @Override
    public boolean onMessageReceive(Object message) throws Exception {
        if (message instanceof StartConversion) {
            PreImage2Type preConvert = (PreImage2Type) (this.preConvert);

            // execute the operation
            logger.info(job.getUUID(),
                    "start to convert from [source: {}] to [dest: {}]",
                    preConvert.getSourceFilePath(),
                    preConvert.getTargetFilePath());
            ImageMeta meta = new ImageMeta();

            ConversionStatus result = ImageUtils.doSvgWork(
                    preConvert.getSourceFilePath(),
                    preConvert.getTargetFilePath(), preConvert.getWidth(),
                    preConvert.getHeight(), preConvert.getPreviewType(), meta,
                    job.getUUID());

            imageStatus.setRatio(meta.getHeight(), meta.getWidth());

            this.getContext().parent().tell(new CompleteConversion(preConvert,
                    result, imageStatus, job), getSelf());
            this.getContext().stop(this.getSelf());
        }
        else {
            return false;
        }
        return true;
    }
}
