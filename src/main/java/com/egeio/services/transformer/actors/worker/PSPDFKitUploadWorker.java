package com.egeio.services.transformer.actors.worker;

import akka.actor.Props;
import com.auth0.jwt.JWT;
import com.auth0.jwt.algorithms.Algorithm;
import com.egeio.core.auth.domain.GuardUrl;
import com.egeio.core.auth.domain.WebRequest;
import com.egeio.core.config.Config;
import com.egeio.core.jobsystem.actors.WebPostActor;
import com.egeio.core.jobsystem.actors.message.FailJobMessage;
import com.egeio.core.log.Logger;
import com.egeio.core.log.LoggerFactory;
import com.egeio.core.utils.GsonUtils;
import com.egeio.services.transformer.actors.AConversionBaseWorker;
import com.egeio.services.transformer.actors.messages.*;
import com.egeio.services.transformer.models.ConversionJob;
import com.egeio.services.transformer.models.ConversionStatus;
import com.egeio.services.transformer.models.PSPDFKitInfo;
import com.egeio.services.transformer.models.PSPDFKitUploadResponse.PSPDFKitUploadResponseData;
import com.egeio.services.transformer.preconvert.AbstractPreConvert;
import com.egeio.services.transformer.preconvert.PrePSPDFKitEncryptUpload;
import com.ning.http.client.FluentCaseInsensitiveStringsMap;
import org.bouncycastle.util.encoders.Base64;

import java.security.KeyFactory;
import java.security.PrivateKey;
import java.security.interfaces.RSAPrivateKey;
import java.security.spec.PKCS8EncodedKeySpec;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

public class PSPDFKitUploadWorker extends AConversionBaseWorker {
    private static Logger logger = LoggerFactory
            .getLogger(PSPDFKitUploadWorker.class);

    private Map<String, Object> additionalInfos = new HashMap<>();

    private static final String DOCUMENT_UPLOAD_URL = "/api/documents";
    private static final String PREVIEW_AUTH_URL = "/i/d/%s/auth";
    private static final String PREVIEW_DOCUMENT_URL = "/i/d/%s/document.json";
    private static final String AUTHORIZATION_HEADER = "Token token=\"%s\"";
    private static final String VERSION_HEADER_VALUE = "protocol=%d, client=%s, client-git=%s";

    private static PSPDFKitInfo defaultPspdfkitInfo;
    private static Algorithm defaultAlgorithm;

    private PSPDFKitInfo pspdfkitInfo;
    private Algorithm algorithm;
    private PSPDFKitUploadResponseData pspdfkitUploadResponseData;

    private static FluentCaseInsensitiveStringsMap previewHeaders;

    static {
        String defaultUploadHost = Config.getString("/configuration/pspdfkit/upload_host", "http://*************:5000");
        String defaultPreviewHost = Config.getString("/configuration/pspdfkit/preview_host", "http://*************:5000");
        String defaultSecretToken = Config.getString("/configuration/pspdfkit/secret_token", "api-auth-token");
        String defaultPrivateKey = Config.getString("/configuration/pspdfkit/private_key");
        defaultPspdfkitInfo = new PSPDFKitInfo();
        defaultPspdfkitInfo.setUploadHost(defaultUploadHost);
        defaultPspdfkitInfo.setPreviewHost(defaultPreviewHost);
        defaultPspdfkitInfo.setSecretToken(defaultSecretToken);
        defaultPspdfkitInfo.setPrivateKey(defaultPrivateKey);

        try {
            defaultAlgorithm = Algorithm.RSA256(null, (RSAPrivateKey) getPrivateKeyFromPemKey(defaultPrivateKey));
        }
        catch (Exception e) {
            throw new RuntimeException(e);
        }

        int protocol = Config.getNumber("/configuration/pspdfkit/protocol", 0);
        String client = Config.getString("/configuration/pspdfkit/client");
        String client_git = Config.getString("/configuration/pspdfkit/client_git");
        previewHeaders = new FluentCaseInsensitiveStringsMap();
        previewHeaders.add("PSPDFKit-Platform", "web");
        previewHeaders.add("PSPDFKit-Version", String.format(VERSION_HEADER_VALUE, protocol, client, client_git));
    }

    public PSPDFKitUploadWorker(ConversionJob job, AbstractPreConvert preConvert) throws Exception{
        super(job, preConvert);
        pspdfkitInfo = defaultPspdfkitInfo;
        algorithm = defaultAlgorithm;
        if(job.getPspdfkitInfo() != null) {
            pspdfkitInfo = job.getPspdfkitInfo();
            algorithm = Algorithm.RSA256(null, (RSAPrivateKey) getPrivateKeyFromPemKey(pspdfkitInfo.getPrivateKey()));
        }
    }

    @Override
    protected void realPreStart() throws Exception {
        logger.info(job.getUUID(), "START: PSPDFKit Upload [worker: {}]",
                getPath());
        getContext().self().tell(new StartConversion(), null);
    }

    @Override
    public void postStop() {
        logger.info(job.getUUID(), "STOP: PSPDFKit Upload [worker: {}]",
                getPath());
    }

    @Override
    public boolean onMessageReceive(Object message) throws Exception {
        if (message instanceof StartConversion) {
            upload();
        }
        else if (message instanceof PSPDFKitUploadedMessage) {
            pspdfkitUploadResponseData = ((PSPDFKitUploadedMessage) message).getPspdfkitUploadResponseData();
            if(pspdfkitUploadResponseData.getErrors() != null && !pspdfkitUploadResponseData.getErrors().isEmpty()) {
                logger.error(job.getUUID(), "PSPDFKit upload failed, errors: "
                        + GsonUtils.getGson().toJson(pspdfkitUploadResponseData.getErrors()));
                getAgent().tell(new FailJobMessage<ConversionJob>(job,
                        new Exception("PSPDFKit upload failed: "
                                + GsonUtils.getGson().toJson(pspdfkitUploadResponseData.getErrors()))), getSelf());
                getContext().stop(getSelf());
            }
            else {
                logger.info(job.getUUID(), "PSPDFKit success uploaded, start preview");
                previewAuth();
            }
        }
        else if (message instanceof PSPDFKitPreviewAuthenticatedMessage) {
            logger.info(job.getUUID(), "preview in PSPDFKit, authenticated");

            PSPDFKitPreviewAuthenticatedMessage pspdfkitPreviewAuthenticatedMessage = (PSPDFKitPreviewAuthenticatedMessage) message;
            previewGetDocument(pspdfkitPreviewAuthenticatedMessage.getToken());
        }
        else if (message instanceof PSPDFKitPreviewedMessage) {
            logger.info(job.getUUID(), "PSPDFKit previewed");

            additionalInfos.put("document_id", pspdfkitUploadResponseData.getDocumentId());
            additionalInfos.put("sha256", pspdfkitUploadResponseData.getSourcePdfSha256());
            additionalInfos.put("pspdfkit_service_server_id", pspdfkitInfo.getServiceServerId());
            getAgent().tell(new CompleteConversion(preConvert, ConversionStatus.UPLOADED, job, additionalInfos), getSelf());
            getContext().stop(getSelf());
        }
        else {
            return false;
        }
        return true;
    }

    private void upload() throws Exception {
        logger.info(job.getUUID(), "start upload to PSPDFKit");

        WebRequest webRequest = new WebRequest(new GuardUrl(pspdfkitInfo.getUploadHost() + DOCUMENT_UPLOAD_URL));
        webRequest.setFileBody(preConvert.getSourceFile());

        FluentCaseInsensitiveStringsMap header = new FluentCaseInsensitiveStringsMap();
        header.add("Authorization", String.format(AUTHORIZATION_HEADER, pspdfkitInfo.getSecretToken()));
        header.add("Content-Type", "application/pdf");
        webRequest.setHeaders(header);
        webRequest.setPost(true);

        this.getContext()
                .actorOf(Props.create(WebPostActor.class, webRequest,
                        getSelf(), preConvert.getWebClient(),
                        PSPDFKitUploadedMessage.class, job.getUUID()));
    }

    private void previewAuth() throws Exception{
        Map<String, Object> body = new HashMap<>();
        body.put("jwt", generateJwt());
        body.put("origin", Config.getString("/configuration/pspdfkit/origin", "http://127.0.0.1"));
        WebRequest webRequest = new WebRequest(new GuardUrl(pspdfkitInfo.getPreviewHost()
                + String.format(PREVIEW_AUTH_URL, pspdfkitUploadResponseData.getDocumentId())),
                GsonUtils.getGson().toJson(body));
        FluentCaseInsensitiveStringsMap headers = new FluentCaseInsensitiveStringsMap(previewHeaders);
        headers.add("Origin", Config.getString("/configuration/pspdfkit/origin", "http://127.0.0.1"));
        webRequest.setHeaders(previewHeaders);

        this.getContext()
                .actorOf(Props.create(WebPostActor.class, webRequest,
                        getSelf(), preConvert.getWebClient(),
                        PSPDFKitPreviewAuthenticatedMessage.class, job.getUUID()));

        logger.info(job.getUUID(), "preview in PSPDFKit, request auth");
    }

    private void previewGetDocument(String token) throws Exception{
        WebRequest webRequest = new WebRequest(new GuardUrl(pspdfkitInfo.getPreviewHost()
                + String.format(PREVIEW_DOCUMENT_URL, pspdfkitUploadResponseData.getDocumentId())));

        FluentCaseInsensitiveStringsMap headers = new FluentCaseInsensitiveStringsMap(previewHeaders);
        headers.add("X-PSPDFKit-Token", token);
        webRequest.setHeaders(headers);

        this.getContext()
                .actorOf(Props.create(WebPostActor.class, webRequest,
                        getSelf(), preConvert.getWebClient(),
                        PSPDFKitPreviewedMessage.class, job.getUUID()));

        logger.info(job.getUUID(), "preview in PSPDFKit, request document.json");
    }

    private static PrivateKey getPrivateKeyFromPemKey(String pemKey) throws Exception {
        pemKey = pemKey.replace("-----BEGIN RSA PRIVATE KEY-----\n", "")
                     .replace("-----END RSA PRIVATE KEY-----", "");

        java.security.Security.addProvider(
                new org.bouncycastle.jce.provider.BouncyCastleProvider()
        );
        PKCS8EncodedKeySpec keySpec = new PKCS8EncodedKeySpec(Base64.decode(pemKey));
        KeyFactory kf = KeyFactory.getInstance("RSA");
        return kf.generatePrivate(keySpec);
    }

    private String generateJwt() {
        String[] permissions = {"read-document"};
        com.auth0.jwt.JWTCreator.Builder builder = JWT.create();
        builder.withExpiresAt(new Date(System.currentTimeMillis()+3600000))
                  .withClaim("document_id", String.valueOf(pspdfkitUploadResponseData.getDocumentId()))
                  .withArrayClaim("permissions", permissions);
        if(preConvert instanceof PrePSPDFKitEncryptUpload) {
            String password = ((PrePSPDFKitEncryptUpload) preConvert).getPassword();
            builder.withClaim("password", password);
        }

        return builder.sign(algorithm);
    }
}
