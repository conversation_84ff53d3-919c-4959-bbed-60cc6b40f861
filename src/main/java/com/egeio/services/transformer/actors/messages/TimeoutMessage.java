package com.egeio.services.transformer.actors.messages;

import com.egeio.core.jobsystem.actors.message.HighPriorityMessage;
import com.egeio.services.transformer.models.ConversionJob;

public class TimeoutMessage extends HighPriorityMessage {

    private static final long serialVersionUID = 8610660029830919755L;

    private ConversionJob job; // to identity each working worker in Agent

    public TimeoutMessage(ConversionJob job) {
        super();
        this.job = job;
    }

    public ConversionJob getJob() {
        return job;
    }
}
