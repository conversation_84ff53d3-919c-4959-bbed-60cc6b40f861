package com.egeio.services.transformer.actors.worker;

import java.awt.Color;
import java.awt.Rectangle;
import java.awt.image.BufferedImage;
import java.io.File;
import java.io.IOException;
import java.io.RandomAccessFile;
import java.nio.ByteBuffer;
import java.nio.channels.FileChannel;

import javax.imageio.ImageIO;

import org.im4java.core.IM4JavaException;

import com.egeio.core.config.Config;
import com.egeio.services.transformer.actors.messages.CompleteConversion;
import com.egeio.services.transformer.actors.messages.StartConversion;
import com.egeio.services.transformer.models.ConversionFlag;
import com.egeio.services.transformer.models.ConversionJob;
import com.egeio.services.transformer.models.ConversionStatus;
import com.egeio.services.transformer.models.ImageStatus;
import com.egeio.services.transformer.preconvert.PrePdf2Image;
import com.egeio.services.transformer.utils.ImageUtils;
import com.sun.pdfview.PDFFile;
import com.sun.pdfview.PDFPage;

public class PdfToImageWorker extends CMDWorker {
    private static final int maxSize = Config
            .getNumber("/configuration/image/max_accept_size", 10000);

    private PrePdf2Image preConvert;
    private ImageStatus imageStatus = new ImageStatus();

    public PdfToImageWorker(ConversionJob job, PrePdf2Image preConvert) {
        super(job, preConvert);
        this.preConvert = preConvert;
        imageStatus.setConversion1024(ConversionFlag.CONVERSION);
        imageStatus.setConversion2048(ConversionFlag.NO_CONVERSION);
    }

    private Rectangle getSizeRect(double width, double height,
            PrePdf2Image preConvert) {
        double size = width;
        if (height > size) {
            size = height;
        }

        double ratio = size / preConvert.getSize();

        return new Rectangle(0, 0, (int) (width / ratio),
                (int) (height / ratio));
    }

    private void tryPdfFile() throws Exception {
        try (RandomAccessFile raf = new RandomAccessFile(
                preConvert.getSourceFilePath(), "r")) {
            FileChannel channel = raf.getChannel();
            ByteBuffer buf = channel.map(FileChannel.MapMode.READ_ONLY, 0,
                    channel.size());
            PDFFile pdffile = new PDFFile(buf);

            int num = pdffile.getNumPages();
            if (num > preConvert.getMaxPages()) {
                num = preConvert.getMaxPages();
            }
            for (int i = 0; i < num; i++) {
                PDFPage page = pdffile.getPage(i);

                // get the width and height for the doc at the default zoom
                double width = page.getBBox().getWidth();
                double height = page.getBBox().getHeight();

                Rectangle sizeRect = getSizeRect(width, height, preConvert);
                int rotation = page.getRotation();
                Rectangle cropRect = new Rectangle(0, 0, (int) width,
                        (int) height);
                if (rotation == 90 || rotation == 270)
                    cropRect = new Rectangle(0, 0, (int) height, (int) width);

                // generate the image
                BufferedImage img = (BufferedImage) page.getImage(
                        sizeRect.width, sizeRect.height, // width & height
                        cropRect, // clip rect
                        null, // null for the ImageObserver
                        true, // fill background with white
                        true // block until drawing is done
                );

                BufferedImage newBufferedImage = new BufferedImage(
                        img.getWidth(), img.getHeight(),
                        BufferedImage.TYPE_INT_RGB);
                newBufferedImage.createGraphics().drawImage(img, 0, 0,
                        Color.WHITE, null);

                File outputFile = new File(
                        String.format(preConvert.getTargetFilePath(), i + 1));

                ImageIO.write(newBufferedImage, preConvert.getTargetType(),
                        outputFile);
            }
        }
    }

    private void compressFile()
            throws IOException, InterruptedException, IM4JavaException {
        File dir = preConvert.getWorkingDir();
        File[] targeFiles = dir.listFiles(preConvert.getFileFilter());
        if (targeFiles != null) {
            for (File f : targeFiles) {
                ImageUtils.compressImage(f, preConvert.getWorkingDir(),
                        preConvert.getTargetType(), uuid, maxSize, null);
            }
        }
    }

    @Override
    public boolean onMessageReceive(Object message) throws Exception {
        if (message instanceof StartConversion) {
            logger.info(job.getUUID(),
                    "start to convert from [source: {}] to [dest: {}]",
                    this.preConvert.getSourceFilePath(),
                    this.preConvert.getTargetFilePath());
            try {
                doCmdJob(timeout);
            }
            catch (Exception e) {
                logger.error(job.getUUID(),
                        "failed to convert using ghost script, try to use com.sun.pdfview",
                        e);
                tryPdfFile();
            }

            compressFile();

            this.getContext().parent()
                    .tell(new CompleteConversion(preConvert,
                            ConversionStatus.CONVERTED, imageStatus, job),
                    getSelf());
            this.getContext().stop(this.getSelf());
        }
        else {
            return false;
        }
        return true;
    }

    @Override
    public void realPreStart() {
        logger.info(job.getUUID(), "START: PdfToImage [worker: {}]", getPath());
        this.getSelf().tell(new StartConversion(), null);
    }

    @Override
    public void postStop() {
        logger.info(job.getUUID(), "STOP: PdfToImage [worker: {}]", getPath());
    }

}
