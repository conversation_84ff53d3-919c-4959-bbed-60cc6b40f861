package com.egeio.services.transformer.preconvert;

import java.io.File;
import java.io.FilenameFilter;

import com.egeio.core.webclient.AkkaWebClient;
import com.egeio.services.transformer.models.ConversionJob;
import com.egeio.services.transformer.models.ConversionMetainfo;
import com.egeio.services.transformer.models.ImageConversionKind;

import org.apache.commons.io.filefilter.NameFileFilter;

public abstract class AbstractPreConvert {
    protected String originalFileType;
    protected File workingDir; // conversion working dir
    protected File sourceFile; // conversion source file
    protected File targetFile; // conversion target file
    protected File singleTargetFile; // target file may contain variable. If
                                     // there is only one target file whose
                                     // exactTarget is false, this target file
                                     // is used. null means do not treat single
                                     // target specially
    protected String targetType; // conversion target type
    protected String sourceType; // conversion source type
    protected String convertKind; // conversion kind
    protected String uniqueName;
    protected String date;
    protected String hour;

    protected AkkaWebClient webClient;

    // The target file contains no variable like %d
    protected boolean exactTarget = true;

    protected boolean webClientNeeded = false;

    public boolean isExactTarget() {
        return exactTarget;
    }

    public void setExactTarget(boolean exactTarget) {
        this.exactTarget = exactTarget;
    }

    public boolean isWebClientNeeded() {
        return webClientNeeded;
    }

    public void setWebClient(AkkaWebClient webClient) {
        this.webClient = webClient;
    }

    public AkkaWebClient getWebClient() {
        return webClient;
    }

    public void prepareConversion(ConversionJob job, String convertKind,
            ConversionMetainfo metainfo, ConversionMetainfo globalMeta, File workingDir) throws Exception {
        this.uniqueName = job.getUniqueName();
        this.date = job.getDate();
        this.hour = job.getHour();
        this.convertKind = convertKind;

        this.workingDir = workingDir;

        prepareMetainfo(metainfo, globalMeta);

        if (metainfo == null) {
            targetType = getDefaultTargetType();
        }
        else {
            targetType = metainfo.getFormat();
        }

        prepareTargetFile();
    }

    protected void prepareTargetFile() throws Exception {
        targetFile = new File(this.workingDir,
                uniqueName + "." + convertKind + "." + targetType);
    }

    protected abstract String getDefaultTargetType() throws Exception;

    protected void prepareMetainfo(ConversionMetainfo metainfo, ConversionMetainfo globalMeta)
            throws Exception {

    }

    public FilenameFilter getFileFilter() {
        return new NameFileFilter(targetFile.getName());
    }

    public void setSourceFile(File sourceFile) {
        this.sourceFile = sourceFile;
    }

    public void setTargetFile(File targetFile) {
        this.targetFile = targetFile;
    }

    public File getSourceFile() {
        return this.sourceFile;
    }

    public String getUniqueName() {
        return uniqueName;
    }

    public void setUniqueName(String uniqueName) {
        this.uniqueName = uniqueName;
    }

    public String getDate() {
        return date;
    }

    public void setDate(String date) {
        this.date = date;
    }

    public String getHour() {
        return hour;
    }

    public void setHour(String hour) {
        this.hour = hour;
    }

    public void setWorkingDir(File workingDir) {
        this.workingDir = workingDir;
    }

    public void setTargetType(String targetType) {
        this.targetType = targetType;
    }

    public void setSourceType(String sourceType) {
        this.sourceType = sourceType;
    }

    public void setConvertKind(String convertKind) {
        this.convertKind = convertKind;
    }

    public File getTargetFile() {
        return this.targetFile;
    }

    public String getSourceFilePath() {
        return this.sourceFile.getAbsolutePath();
    }

    public String getTargetFilePath() {
        return this.targetFile.getAbsolutePath();
    }

    public File getWorkingDir() {
        return workingDir;
    }

    public String getWorkingDirPath() {
        return workingDir.getAbsolutePath();
    }

    public String getSourceType() {
        return this.sourceType;
    }

    public String getTargetType() {
        return this.targetType;
    }

    public String getConvertKind() {
        return convertKind;

    }

    public File getSingleTargetFile() {
        return singleTargetFile;
    }

    public void setSingleTargetFile(File singleTargetFile) {
        this.singleTargetFile = singleTargetFile;
    }

    public String getOriginalFileType() {
        return originalFileType;
    }

    public void setOriginalFileType(String originalFileType) {
        this.originalFileType = originalFileType;
    }

    /**
     * @return enum value of convertKind
     */
    public ImageConversionKind getConvertKindValue() {
        return ImageConversionKind.getImageConversionKinds(convertKind);
    }

    public File getTmpTargeFile() {
        String sourcePath = getSourceFilePath();
        int index = sourcePath.lastIndexOf(getSourceType());
        if (index != -1) {
            sourcePath = sourcePath.substring(0, index);
        }
        String targeFileName = sourcePath + getTargetType();

        return new File(targeFileName);
    }
}
