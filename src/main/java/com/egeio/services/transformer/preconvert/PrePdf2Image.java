package com.egeio.services.transformer.preconvert;

import java.io.File;
import java.io.FilenameFilter;
import java.io.IOException;

import org.apache.commons.exec.CommandLine;
import org.apache.commons.io.filefilter.RegexFileFilter;

import com.egeio.core.config.Config;
import com.egeio.core.utils.Utils;

public class PrePdf2Image extends AbstractImagePreConvert
        implements ICmdPreConvert {
    private int ppi = 150;
    private int size = 1500;
    private int maxPages = 100;

    public PrePdf2Image() {
        // The target file contains variable %d
        exactTarget = false;

        ppi = Config.getNumber("/configuration/pdf2image/ppi", ppi);
        size = Config.getNumber("/configuration/pdf2image/size", size);
        maxPages = Config.getNumber("/configuration/pdf2image/max_pages",
                maxPages);
    }

    @Override
    public void prepareTargetFile() throws Exception {
        targetFile = new File(this.workingDir,
                uniqueName + "." + convertKind + ".%d." + targetType);
        singleTargetFile = new File(this.workingDir,
                uniqueName + "." + convertKind + "." + targetType);
    }

    @Override
    public CommandLine getCMD() throws IOException {
        return Utils.getCMD("pdf2image", getPPI(), 1, getMaxPages(),
                getTargetFilePath(), getSourceFilePath());
    }

    public int getPPI() {
        return ppi;
    }

    public int getSize() {
        return size;
    }

    public int getMaxPages() {
        return maxPages;
    }

    public FilenameFilter getFileFilter() {
        return new RegexFileFilter(this.uniqueName + "\\." + this.convertKind
                + "\\." + "[0-9]+" + "\\." + this.targetType);
    }

    @Override
    protected String getDefaultTargetType() throws Exception {
        return "jpg";
    }
}