package com.egeio.services.transformer.preconvert;

import java.io.File;
import java.io.IOException;
import java.util.UUID;

import org.apache.commons.exec.CommandLine;

import com.egeio.core.utils.Utils;

public class PreMarkdown2Html extends AbstractPreConvert
        implements ICmdPreConvert {
    @Override
    public CommandLine getCMD() throws IOException {
        return Utils.getCMD("markdown2html", getTargetFilePath(),
                tmpFile.getAbsolutePath());
    }

    private File tmpFile;

    public CommandLine get2UtfCMD() throws IOException {
        tmpFile = new File(workingDir,
                UUID.randomUUID().toString() + "." + sourceType);

        return Utils.getCMD("2utf8", tmpFile.getAbsolutePath(),
                getSourceFilePath());
    }

    @Override
    protected String getDefaultTargetType() throws Exception {
        return "html";
    }

    public File getTmpFile() {
        return tmpFile;
    }
}
