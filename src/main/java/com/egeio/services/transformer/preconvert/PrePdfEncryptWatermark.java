package com.egeio.services.transformer.preconvert;

import com.egeio.services.transformer.models.ConversionMetainfo;
import com.egeio.services.transformer.models.WatermarkInfo;

import java.io.File;

/**
 * Created by <PERSON><PERSON><PERSON> on 2017/9/6.
 */
public class PrePdfEncryptWatermark extends PrePdfEncrypt {
    private WatermarkInfo watermarkInfo;

    @Override
    protected void prepareMetainfo(ConversionMetainfo metainfo, ConversionMetainfo globalMeta) throws Exception {
        super.prepareMetainfo(metainfo, globalMeta);
        if(metainfo != null) {
            watermarkInfo = metainfo.getWatermarkInfo();
        }
        if(watermarkInfo == null && globalMeta != null) {
            watermarkInfo = globalMeta.getWatermarkInfo();
        }
    }

    @Override
    protected void prepareTargetFile() throws Exception {
        targetFile = new File(this.workingDir,
                uniqueName + "." + convertKind + "." + watermarkInfo.getTargetFileUniqueId() + "." + targetType);
    }
}
