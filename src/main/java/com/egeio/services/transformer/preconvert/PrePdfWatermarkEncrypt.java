package com.egeio.services.transformer.preconvert;

import com.egeio.services.transformer.models.ConversionMetainfo;

/**
 * Created by <PERSON><PERSON><PERSON> on 2017/9/11.
 */
public class PrePdfWatermarkEncrypt extends PrePdfWatermark {
    private String password;
    private String ownerPassword;

    @Override
    protected void prepareMetainfo(ConversionMetainfo metainfo, ConversionMetainfo globalMeta)
            throws Exception {
        password = metainfo.getPassword();
        ownerPassword = metainfo.getOwnerPassword();
        super.prepareMetainfo(metainfo, globalMeta);
    }

    public String getPassword() {
        return password;
    }

    public void setPassword(String password) {
        this.password = password;
    }

    public String getOwnerPassword() {
        return ownerPassword;
    }

    public void setOwnerPassword(String ownerPassword) {
        this.ownerPassword = ownerPassword;
    }
}
