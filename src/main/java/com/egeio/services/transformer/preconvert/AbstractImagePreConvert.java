package com.egeio.services.transformer.preconvert;

import com.egeio.services.transformer.models.ConversionMetainfo;
import com.egeio.services.transformer.models.PreviewType;

public abstract class AbstractImagePreConvert extends AbstractPreConvert {
    public static final int MAX_PPI = 72;

    private int width;
    private int height;
    private PreviewType previewType = PreviewType.PREVIEW;

    // Some picture should convert only first layer
    protected boolean onlyFirstLayer = false;

    public boolean isOnlyFirstLayer() {
        return onlyFirstLayer;
    }

    public void setOnlyFirstLayer(boolean onlyFirstLayer) {
        this.onlyFirstLayer = onlyFirstLayer;
    }

    public int getWidth() {
        return this.width;
    }

    public int getHeight() {
        return this.height;
    }

    public int getPPI() {
        return 72;
    }

    public PreviewType getPreviewType() {
        return previewType;
    }

    public void setPreviewType(PreviewType previewType) {
        this.previewType = previewType;
    }

    @Override
    protected void prepareMetainfo(ConversionMetainfo metainfo, ConversionMetainfo globalMeta)
            throws Exception {
        if (metainfo != null) {
            width = metainfo.getWidth();
            height = metainfo.getHeight();

            previewType = metainfo.getType();
        }
    }
}
