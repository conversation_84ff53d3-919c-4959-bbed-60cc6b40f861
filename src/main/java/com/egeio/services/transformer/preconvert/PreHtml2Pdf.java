package com.egeio.services.transformer.preconvert;

import java.io.BufferedInputStream;
import java.io.FileInputStream;
import java.io.IOException;

import org.apache.commons.exec.CommandLine;
import org.apache.tika.parser.txt.CharsetMatch;

import com.egeio.core.tika.utils.TikaUtils;
import com.egeio.core.utils.Utils;

public class PreHtml2Pdf extends AbstractPreConvert implements ICmdPreConvert {
    private static final String TRY_ENCODING = "GB18030";
    private static final String DEFAULT_ENCODING = "UTF-8";

    private String fileEncoding = null;

    public String getFileEncoding() {
        if (fileEncoding == null) {
            fileEncoding = DEFAULT_ENCODING;

            try (BufferedInputStream in = new BufferedInputStream(
                    new FileInputStream(sourceFile))) {
                CharsetMatch m = TikaUtils.detectCharset(in, TRY_ENCODING);
                if (m != null) {
                    fileEncoding = m.getName();
                }
            }
            catch (Exception e) {

            }
        }

        return fileEncoding;
    }

    @Override
    public CommandLine getCMD() throws IOException {
        return Utils.getCMD("html2pdf", getFileEncoding(), getSourceFilePath(),
                getTargetFilePath());
    }

    @Override
    protected String getDefaultTargetType() throws Exception {
        return "pdf";
    }
}