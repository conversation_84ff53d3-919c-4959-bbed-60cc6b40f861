package com.egeio.services.transformer.preconvert;

import com.egeio.services.transformer.models.ConversionMetainfo;
import com.egeio.services.transformer.models.WatermarkInfo;

import java.io.File;

public class PreMergeHtmlWatermark extends AbstractPreConvert {
    private WatermarkInfo watermarkInfo;

    @Override
    protected void prepareMetainfo(ConversionMetainfo metainfo, ConversionMetainfo globalMeta) throws Exception {
        if(metainfo != null) {
            watermarkInfo = metainfo.getWatermarkInfo();
        }
        if(watermarkInfo == null && globalMeta != null) {
            watermarkInfo = globalMeta.getWatermarkInfo();
        }
    }

    @Override
    protected void prepareTargetFile() throws Exception {
        targetFile = new File(this.workingDir,
                uniqueName + "." + convertKind + "." + watermarkInfo.getTargetFileUniqueId() + "." + targetType);
    }

    @Override
    protected String getDefaultTargetType() throws Exception {
        return "html";
    }

    public String getWatermarkContent() {
        return watermarkInfo.getWatermark();
    }
}
