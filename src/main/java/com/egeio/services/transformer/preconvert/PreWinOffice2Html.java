package com.egeio.services.transformer.preconvert;

import com.egeio.core.utils.Utils;
import com.egeio.services.transformer.models.OfficeType;
import org.apache.commons.exec.CommandLine;

import java.io.IOException;

public class PreWinOffice2Html extends AbstractWinOfficePreConvert
        implements ICmdPreConvert {
    @Override
    protected String getDefaultTargetType() throws Exception {
        return "html";
    }

    @Override
    public CommandLine getCMD() throws IOException {
        return Utils.getCMD("winoffice2html", type.getId(), getSourceFilePath(),
                getTargetFilePath());
    }
}