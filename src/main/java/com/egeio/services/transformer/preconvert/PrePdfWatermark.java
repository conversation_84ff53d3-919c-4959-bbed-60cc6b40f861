package com.egeio.services.transformer.preconvert;

import com.egeio.core.config.Config;
import com.egeio.core.log.Logger;
import com.egeio.core.log.LoggerFactory;
import com.egeio.core.log.MyUUID;
import com.egeio.services.transformer.models.ConversionMetainfo;
import com.egeio.services.transformer.models.WatermarkInfo;
import com.egeio.services.transformer.utils.PdfWatermarkUtil;
import com.google.common.base.Strings;
import com.itextpdf.kernel.font.PdfFont;
import com.itextpdf.kernel.geom.Rectangle;
import org.apache.commons.lang.StringEscapeUtils;
import org.springframework.util.CollectionUtils;

import java.io.File;
import java.nio.file.Paths;
import java.util.List;

public class PrePdfWatermark extends AbstractPreConvert{

    private Logger logger = LoggerFactory.getLogger(this.getClass());

    public static int REPEAT_X;
    public static int REPEAT_Y;
    public static String DELIMITER_X;
    public static String FONT;
    public static int FONTSIZE;
    public static float OPACITY;
    public static float ROTATE;
    public static float MULTIPLIED_LEADING;
    public static float GRAY;
    public static String COLOR;

    static {
        REPEAT_X = Config.getNumber("/configuration/watermark/pdf/repeat", "x", 4);
        REPEAT_Y = Config.getNumber("/configuration/watermark/pdf/repeat", "y", 8);
        DELIMITER_X = StringEscapeUtils.unescapeJava(Config.getString("/configuration/watermark/pdf/repeat", "delimiter_x", "\\t"));
        FONT = Paths.get(
                Config.getString("/configuration/watermark/font_dir", "/usr/share/fonts/my_fonts"),
                Config.getString("/configuration/watermark/pdf/font", "msyh.ttf")
        ).toString();
        FONTSIZE = Config.getNumber("/configuration/watermark/pdf/fontsize", 30);
        OPACITY = Config.getNumber("/configuration/watermark/pdf/opacity", 0.5f);
        ROTATE = Config.getNumber("/configuration/watermark/pdf/rotate", 30.0f);
        MULTIPLIED_LEADING = Config.getNumber("/configuration/watermark/pdf/multiplied_leading", 1.0f);
        GRAY = Config.getNumber("/configuration/watermark/pdf/gray", 0.85f);
        COLOR = Config.getString("/configuration/watermark/pdf/color", "#D3D3D3");
    }

    private WatermarkInfo watermarkInfo;
    private String watermarkLine;
    private String watermarkPara;
    private int repeatX = REPEAT_X;
    private int repeatY = REPEAT_Y;

    @Override
    protected void prepareMetainfo(ConversionMetainfo metainfo, ConversionMetainfo globalMeta) throws Exception {
        if(metainfo != null) {
            watermarkInfo = metainfo.getWatermarkInfo();
        }
        if(watermarkInfo == null && globalMeta != null) {
            watermarkInfo = globalMeta.getWatermarkInfo();
        }
    }

    @Override
    protected String getDefaultTargetType() throws Exception {
        return "pdf";
    }

    @Override
    protected void prepareTargetFile() throws Exception {
        targetFile = new File(this.workingDir,
                uniqueName + "." + convertKind + "." + watermarkInfo.getTargetFileUniqueId() + "." + targetType);
    }

    public static float getMultipliedLeading(boolean isCustom){
        if (isCustom){
            return MULTIPLIED_LEADING;
        }
        return 3.0f;
    }
    public String getWatermarkParagraph(int repeatX, int repeatY) {
        this.repeatX = repeatX;
        this.repeatY = repeatY;
        StringBuilder builder = new StringBuilder();
        builder.append(watermarkInfo.getWatermark());

        String watermarkString = builder.append(DELIMITER_X).toString();
        watermarkLine = Strings.repeat(watermarkString, repeatX);

        builder = new StringBuilder();
        for (int i=0; i<repeatY; i++) {
            builder
                    .append(Strings.repeat("\u00a0", (repeatY-i-1)*10))
                    .append(watermarkLine)
                    .append("\n");
        }
        watermarkPara = builder.toString();
        return watermarkPara;
    }


    public String getCustomWatermark(int repeatX, int repeatY) {
        this.repeatX = repeatX;
        this.repeatY = repeatY;
        StringBuilder builder = new StringBuilder();

        List<String> contentList = watermarkInfo.getContentList();
        String maxContent = "";
        int max = 0;
        for (String content : contentList) {

            if(content.length()>max){
                maxContent = content;
                max = content.length();
            }
        }

        String watermarkLineString = builder.append(maxContent).append(DELIMITER_X).toString();
        watermarkLine = Strings.repeat(watermarkLineString, repeatX);

        builder = new StringBuilder();
        for (int i = 0; i < repeatY; i++) {

            for (String content : contentList) {
                StringBuilder builderLine = new StringBuilder();
                String watermarkLineStringTmp = builderLine.append(content).append("    ").toString();

                String watermarkLineTmp = Strings.repeat(watermarkLineStringTmp, repeatX);

                builder
                        .append(Strings.repeat("\u00a0", (repeatY - i - 1) * 10))
                        .append(watermarkLineTmp)
                        .append("\n");
            }
            builder.append("\n");
        }
        watermarkPara = builder.toString();
        return watermarkPara;
    }


    public String getWatermarkParagraph(boolean regenerate) {
        if (watermarkPara != null && !regenerate) {
            return watermarkPara;
        } else {
            return getWatermarkParagraph(repeatX, repeatY);
        }
    }

    public String getWatermarkLine() {
        return watermarkLine;
    }

    public int getRepeatY() {
        return repeatY;
    }

    public int getRepeatX() {
        return repeatX;
    }


    public void setWatermarkInfo(WatermarkInfo watermarkInfo) {
        this.watermarkInfo = watermarkInfo;
    }

    public WatermarkInfo getWatermarkInfo() {
        return watermarkInfo;
    }
}
