package com.egeio.services.transformer.preconvert;

import java.io.IOException;

import org.apache.commons.exec.CommandLine;

import com.egeio.core.utils.Utils;
import com.egeio.services.transformer.models.OfficeType;

public class PreLinuxOffice2Pdf extends AbstractPreConvert
        implements ICmdPreConvert {
    private OfficeType type;

    @Override
    protected String getDefaultTargetType() throws Exception {
        return "pdf";
    }

    public OfficeType getType() {
        return type;
    }

    public void setType(OfficeType type) {
        this.type = type;
    }

    @Override
    public CommandLine getCMD() throws IOException {
        return Utils.getCMD("aspose2pdf", getSourceFilePath(),
                getTargetFilePath(), getSourceType());
    }
}