package com.egeio.services.transformer.preconvert;

import com.egeio.services.transformer.models.ConversionJob;
import com.egeio.services.transformer.models.ConversionMetainfo;
import com.egeio.services.transformer.models.WatermarkInfo;

import java.io.File;

public class PreImageWatermark extends AbstractPreConvert{

    private WatermarkInfo watermarkInfo;
    private File watermarkFile;
    private File rotateSourceFile;

    @Override
    public void prepareConversion(ConversionJob job, String convertKind, ConversionMetainfo metainfo,
                                  ConversionMetainfo globalMeta, File workingDir) throws Exception {
        super.prepareConversion(job, convertKind, metainfo, globalMeta, workingDir);
        // prepare watermark file
        prepareWatermarkFile();
        // prepare rotate source file
        prepareRotateSourceFile();
    }

    @Override
    protected void prepareMetainfo(ConversionMetainfo metainfo, ConversionMetainfo globalMeta) throws Exception {
        if(metainfo != null) {
            watermarkInfo = metainfo.getWatermarkInfo();
        }
        if(watermarkInfo == null && globalMeta != null) {
            watermarkInfo = globalMeta.getWatermarkInfo();
        }
    }

    @Override
    protected String getDefaultTargetType() throws Exception {
        return "jpg";
    }

    @Override
    protected void prepareTargetFile() throws Exception {
        targetFile = new File(this.workingDir,
                uniqueName + "." + convertKind + "." + watermarkInfo.getTargetFileUniqueId() + "." + targetType);
    }

    protected void prepareWatermarkFile() throws Exception {
        watermarkFile = new File(this.workingDir,
                uniqueName + "." + watermarkInfo.getTargetFileUniqueId() + ".png");
    }

    public String getWatermarkFilePath() {
        return watermarkFile.getAbsolutePath();
    }

    protected void prepareRotateSourceFile() throws Exception {
        rotateSourceFile = new File(this.workingDir,
                uniqueName + ".rotate.png");
    }

    public String getRotateSourceFilePath() {
        return rotateSourceFile.getAbsolutePath();
    }

    public String getWatermarkParagraph() {
        // !! imageMagick annotate does not work with tab
        StringBuilder builder = new StringBuilder();
        builder.append(watermarkInfo.getWatermark());

        return builder.toString();
    }

    public WatermarkInfo getWatermarkInfo() {
        return watermarkInfo;
    }

    public void setWatermarkInfo(WatermarkInfo watermarkInfo) {
        this.watermarkInfo = watermarkInfo;
    }

}
