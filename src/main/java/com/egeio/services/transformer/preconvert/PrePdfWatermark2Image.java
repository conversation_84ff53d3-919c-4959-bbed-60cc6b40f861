package com.egeio.services.transformer.preconvert;

import com.egeio.services.transformer.models.ConversionMetainfo;
import com.egeio.services.transformer.models.WatermarkInfo;
import org.apache.commons.io.filefilter.RegexFileFilter;

import java.io.File;
import java.io.FilenameFilter;

public class PrePdfWatermark2Image extends PrePdf2Image {

    private WatermarkInfo watermarkInfo;

    @Override
    protected void prepareMetainfo(ConversionMetainfo metainfo, ConversionMetainfo globalMeta) throws Exception {
        if(metainfo != null) {
            watermarkInfo = metainfo.getWatermarkInfo();
        }
        if(watermarkInfo == null && globalMeta != null) {
            watermarkInfo = globalMeta.getWatermarkInfo();
        }
    }

    @Override
    public void prepareTargetFile() throws Exception {
        targetFile = new File(this.workingDir,
                uniqueName + "." + convertKind + ".%d." + watermarkInfo.getTargetFileUniqueId() + "." + targetType);
        singleTargetFile = new File(this.workingDir,
                uniqueName + "." + convertKind + "." + watermarkInfo.getTargetFileUniqueId() + "." + targetType);
    }

    public FilenameFilter getFileFilter() {
        return new RegexFileFilter(this.uniqueName + "\\." + this.convertKind
                + "\\." + "[0-9]+" + "\\." + watermarkInfo.getTargetFileUniqueId() + "\\." + this.targetType);
    }

}
