package com.egeio.services.transformer.preconvert;

import java.io.IOException;

import org.apache.commons.exec.CommandLine;

import com.egeio.core.utils.Utils;

public class PreCAD2Pdf extends AbstractPreConvert implements ICmdPreConvert {
    public CommandLine getCMD() throws IOException {
        return Utils.getCMD("cad2pdf", getSourceFilePath());
    }

    @Override
    protected String getDefaultTargetType() throws Exception {
        return "pdf";
    }
}
