package com.egeio.services.transformer.preconvert;

import java.io.IOException;

import org.apache.commons.exec.CommandLine;

import com.egeio.core.utils.Utils;

public class PreCode2Html extends AbstractPreConvert implements ICmdPreConvert {

    @Override
    public CommandLine getCMD() throws IOException {
        return Utils.getCMD("code2html", getSourceFilePath(),
                getTargetFilePath());

    }

    @Override
    protected String getDefaultTargetType() throws Exception {
        return "html";
    }
}