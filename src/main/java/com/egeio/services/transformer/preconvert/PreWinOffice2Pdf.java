package com.egeio.services.transformer.preconvert;

import java.io.IOException;

import org.apache.commons.exec.CommandLine;

import com.egeio.core.utils.Utils;
import com.egeio.services.transformer.models.OfficeType;

public class PreWinOffice2Pdf extends AbstractWinOfficePreConvert
        implements ICmdPreConvert {
    @Override
    protected String getDefaultTargetType() throws Exception {
        return "pdf";
    }

    @Override
    public CommandLine getCMD() throws IOException {
        return Utils.getCMD("winoffice2pdf", type.getId(), getSourceFilePath(),
                getTargetFilePath());
    }
}