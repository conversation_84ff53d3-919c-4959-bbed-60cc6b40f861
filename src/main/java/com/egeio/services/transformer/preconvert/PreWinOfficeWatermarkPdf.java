package com.egeio.services.transformer.preconvert;

import com.egeio.core.utils.Utils;
import com.egeio.services.transformer.models.ConversionMetainfo;
import com.egeio.services.transformer.models.WatermarkInfo;
import org.apache.commons.exec.CommandLine;

import java.io.File;
import java.io.IOException;

public class PreWinOfficeWatermarkPdf extends AbstractWinOfficePreConvert
        implements ICmdPreConvert {
    private WatermarkInfo watermarkInfo;

    @Override
    protected void prepareMetainfo(ConversionMetainfo metainfo, ConversionMetainfo globalMeta) throws Exception {
        if(metainfo != null) {
            watermarkInfo = metainfo.getWatermarkInfo();
        }
        if(watermarkInfo == null && globalMeta != null) {
            watermarkInfo = globalMeta.getWatermarkInfo();
        }
    }

    @Override
    protected void prepareTargetFile() throws Exception {
        targetFile = new File(this.workingDir,
                uniqueName + "." + convertKind + "." + watermarkInfo.getTargetFileUniqueId() + "." + targetType);
    }

    @Override
    protected String getDefaultTargetType() throws Exception {
        return "pdf";
    }

    @Override
    public CommandLine getCMD() throws IOException {
        return Utils.getCMD("winofficewatermark", type.getId(), getSourceFilePath(),
                getTargetFilePath(), getWatermarkContent());
    }

    private String getWatermarkContent() {
        return watermarkInfo.getWatermark();
    }
}