package com.egeio.services.transformer.preconvert;

import com.egeio.core.utils.Utils;
import org.apache.commons.exec.CommandLine;

import java.io.IOException;

public class PreDwg2Ocf extends AbstractPreConvert implements ICmdPreConvert {
    @Override
    public CommandLine getCMD() throws IOException {
        return Utils.getCMD("dwg2ocf", getSourceFilePath(), getTargetFilePath());
    }

    @Override
    protected String getDefaultTargetType() throws Exception {
        return "ocf";
    }


}
