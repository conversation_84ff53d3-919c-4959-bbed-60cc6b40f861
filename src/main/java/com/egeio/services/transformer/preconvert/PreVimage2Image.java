package com.egeio.services.transformer.preconvert;

import java.io.IOException;

import org.apache.commons.exec.CommandLine;

import com.egeio.core.utils.Utils;

public class PreVimage2Image extends AbstractImagePreConvert
        implements ICmdPreConvert {
    public CommandLine getCMD() throws IOException {
        int ppi = getPPI();
        return Utils.getCMD("vi2image", ppi, getTargetFilePath(),
                getSourceFilePath());
    }

    @Override
    protected String getDefaultTargetType() throws Exception {
        return "jpg";
    }
}