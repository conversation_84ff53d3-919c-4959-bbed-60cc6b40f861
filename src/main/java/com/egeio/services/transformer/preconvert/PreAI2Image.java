package com.egeio.services.transformer.preconvert;

import java.io.IOException;

import org.apache.commons.exec.CommandLine;

import com.egeio.core.utils.Utils;

public class PreAI2Image extends AbstractImagePreConvert
        implements ICmdPreConvert {

    @Override
    public CommandLine getCMD() throws IOException {
        return Utils.getCMD("ai2image", getSourceFilePath(),
                getTargetFilePath());
    }

    @Override
    protected String getDefaultTargetType() throws Exception {
        return "jpg";
    }
}
