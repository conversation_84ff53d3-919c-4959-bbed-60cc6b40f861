package com.egeio.services.transformer.preconvert;

import com.egeio.services.transformer.models.ConversionMetainfo;

public class PrePSPDFKitEncryptUpload extends PrePSPDFKitUpload {

    private String password;

    @Override
    protected void prepareMetainfo(ConversionMetainfo metainfo, ConversionMetainfo globalMeta)
            throws Exception {
        password = metainfo.getPassword();
    }

    public String getPassword() {
        return password;
    }

    public void setPassword(String password) {
        this.password = password;
    }
}
