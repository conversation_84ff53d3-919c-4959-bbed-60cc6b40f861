package com.egeio.services.transformer.preconvert;

import java.io.IOException;

import org.apache.commons.exec.CommandLine;

import com.egeio.core.utils.Utils;

public class PreCAD2Image extends AbstractPreConvert implements ICmdPreConvert {

    private int size = 3000;

    public int getSize() {
        return size;
    }

    public void setSize(int size) {
        this.size = size;
    }

    public CommandLine getCMD() throws IOException {
        return Utils.getCMD("cad2jpg", getSize(), getSize(),
                getSourceFilePath());
    }

    @Override
    protected String getDefaultTargetType() throws Exception {
        return "jpg";
    }

}
