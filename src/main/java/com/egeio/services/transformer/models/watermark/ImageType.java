package com.egeio.services.transformer.models.watermark;

import com.egeio.core.config.Config;
import org.dom4j.Element;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/3/22 上午11:56
 */
public enum ImageType {

    NORMAL("normal"),
    TIF("tif"),
    PS("ps"),
    UNKNOWN("");
    private String id;
    private List<String> exts = new ArrayList<String>();

    ImageType(String id) {
        this.id = id;
    }

    public String getId() {
        return id;
    }


    public List<String> getExts() {
        return exts;
    }

    public void addExts(String ext) {
        this.exts.add(ext);
    }

    public static ImageType getTypeViaId(String id) {
        for (ImageType type : values()) {
            if (type.getId().equals(id)) {
                return type;
            }
        }
        return UNKNOWN;
    }

    public static ImageType getTypeViaExt(String ext) {
        for (ImageType type : values()) {
            if (type.getExts().contains(ext.trim().toLowerCase())) {
                return type;
            }
        }
        return UNKNOWN;
    }

    public static boolean isImageType(String ext){
        for (ImageType type : values()) {
            if (type.getExts().contains(ext.trim().toLowerCase())) {
                return true;
            }
        }
        return false;

    }

    static {
        Element el = Config.getConfig().getElement(
                "/configuration/image/mapping_list");
        @SuppressWarnings("unchecked")
        List<Element> mappings = el.elements();
        for (Element e : mappings) {
            String extsStr = e.attributeValue("extensions");
            String[] exts = extsStr.split(",");
            String id = e.attributeValue("id").trim();

            ImageType type = getTypeViaId(id);
            for (String ext : exts) {
                type.addExts(ext.trim().toLowerCase());
            }
        }
    }


}
