package com.egeio.services.transformer.models.watermark;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2022/3/30 下午7:11
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class WatermarkContentParam {
    private String watermarkLine;
    private String watermarkAll;

    @Override
    public String toString() {
        return "WatermarkContentParam{" +
                "watermarkLine='" + watermarkLine + '\'' +
                ", watermarkAll='" + watermarkAll + '\'' +
                '}';
    }
}
