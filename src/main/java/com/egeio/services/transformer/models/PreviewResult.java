package com.egeio.services.transformer.models;

import com.google.gson.annotations.SerializedName;

public class PreviewResult {
    @SerializedName("file_storage_id")
    private long fileStorageId;
    @SerializedName("convert_kind")
    private String convertKind;
    @SerializedName("status")
    private PreviewStatus status;

    public long getFileStorageId() {
        return fileStorageId;
    }

    public void setFileStorageId(long fileStorageId) {
        this.fileStorageId = fileStorageId;
    }

    public String getConvertKind() {
        return convertKind;
    }

    public void setConvertKind(String convertKind) {
        this.convertKind = convertKind;
    }

    public PreviewStatus getStatus() {
        return status;
    }

    public void setStatus(PreviewStatus status) {
        this.status = status;
    }
}
