package com.egeio.services.transformer.models.watermark;

import com.egeio.core.utils.Utils;
import com.egeio.services.transformer.preconvert.ICmdPreConvert;
import lombok.Data;
import org.apache.commons.exec.CommandLine;

import java.io.IOException;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2022/3/22 下午6:02
 */
@Data
public class AsposeWatermarkParam extends AbstractWatermarkParam
        implements ICmdPreConvert, Serializable {

    @Override
    public String getDefaultTargetType() throws Exception {
        return "pdf";
    }

    @Override
    public CommandLine getCMD() throws IOException {
        return Utils.getCMD("aspose2pdf", getSourceFilePath(),
                getTargetFilePath(), getSourceType());
    }
}
