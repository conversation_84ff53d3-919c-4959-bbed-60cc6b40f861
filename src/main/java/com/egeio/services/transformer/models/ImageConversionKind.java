package com.egeio.services.transformer.models;

public enum ImageConversionKind {
    IMAGE64("image_64"),
    IMAGE128("image_128"),
    IMAGE1024("image_1024"),
    IMAGE2048("image_2048"),
    IMAGE1024_ORIGIN("image_1024_origin"),
    IMAGE2048_ORIGIN("image_2048_origin"),
    IMAGE_VI_ORIGIN("image_vi_origin"),
    UNKNOWN("");

    private String str;

    private ImageConversionKind(String str) {
        this.str = str;
    }

    public String getStr() {
        return str;
    }

    public static ImageConversionKind getImageConversionKinds(String str) {
        if (str != null) {
            for (ImageConversionKind kind : values()) {
                if (kind.getStr().equals(str)) {
                    return kind;
                }
            }
        }

        return UNKNOWN;
    }
}
