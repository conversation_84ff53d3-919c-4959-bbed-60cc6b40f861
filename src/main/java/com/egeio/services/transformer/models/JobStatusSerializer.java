package com.egeio.services.transformer.models;

import java.lang.reflect.Type;

import com.google.gson.JsonDeserializationContext;
import com.google.gson.JsonDeserializer;
import com.google.gson.JsonElement;
import com.google.gson.JsonParseException;
import com.google.gson.JsonPrimitive;
import com.google.gson.JsonSerializationContext;
import com.google.gson.JsonSerializer;

public class JobStatusSerializer
        implements JsonSerializer<JobStatus>, JsonDeserializer<JobStatus> {

    @Override
    public JobStatus deserialize(JsonElement json, Type typeOfT,
            JsonDeserializationContext context) throws JsonParseException {
        return JobStatus.getJobStatus(json.getAsString());
    }

    @Override
    public JsonElement serialize(JobStatus src, Type typeOfSrc,
            JsonSerializationContext context) {
        return new JsonPrimitive(src.getStr());
    }

}
