package com.egeio.services.transformer.models.watermark;

import com.egeio.services.transformer.preconvert.AbstractPreConvert;
import com.google.common.base.Strings;
import lombok.Data;

/**
 * 重写转换成pdf文件的所需要的参数类
 *
 * <AUTHOR>
 * @date 2022/3/23 下午2:59
 */
@Data
public class PdfWatermarkParam extends AbstractPreConvert {

    public int REPEAT_X;
    public int REPEAT_Y;
    public String DELIMITER_X;
    public String FONT;
    public int FONTSIZE;
    public float OPACITY;
    public float ROTATE;
    public float MULTIPLIED_LEADING;
    public float GRAY;

    /**
     * 水印每一行的样式
     */
    private String watermarkLine;
    /**
     * pdf上所有水印的样式
     */
    private String watermarkAll;
    private int repeatX = REPEAT_X;
    private int repeatY = REPEAT_Y;

    /**
     * @param repeatX
     * @param repeatY
     * @param watermarkContent：水印内容
     * @return
     */
    public String getWatermarkParagraph(int repeatX, int repeatY, String watermarkContent) {
        this.repeatX = repeatX;
        this.repeatY = repeatY;
        StringBuilder builder = new StringBuilder();

        //添加水印内容
        builder.append(watermarkContent);

        String watermarkString = builder.append(DELIMITER_X).toString();
        watermarkLine = Strings.repeat(watermarkString, repeatX);

        builder = new StringBuilder();
        for (int i = 0; i < repeatY; i++) {
            builder
                    .append(Strings.repeat("\u00a0", (repeatY - i - 1) * 10))
                    .append(watermarkLine)
                    .append("\n");
        }
        watermarkAll = builder.toString();
        return watermarkAll;
    }


    @Override
    protected String getDefaultTargetType() throws Exception {
        return "pdf";
    }
}
