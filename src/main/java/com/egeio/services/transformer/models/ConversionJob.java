package com.egeio.services.transformer.models;

import java.util.HashSet;
import java.util.Map;
import java.util.Set;

import com.egeio.core.jobsystem.job.OssJob;
import com.egeio.core.utils.GsonUtils;
import com.google.gson.annotations.SerializedName;

public class ConversionJob extends OssJob implements Cloneable {
    private static final long serialVersionUID = -1615651547395497056L;
    private String extension;
    @SerializedName("convert_kinds")
    private Map<String, ConversionMetainfo> convertMetas;
    @SerializedName("global_meta_info")
    private ConversionMetainfo globalMeta;
    @SerializedName("converted_kinds")
    private Set<String> convertedKinds = new HashSet<String>();
    @SerializedName("force_convert")
    private boolean forceConvert;
    @SerializedName("last_platform")
    private OfficeConversionPlatform lastPlatform = OfficeConversionPlatform.UNKNOWN;
    @SerializedName("need_encrypt")
    private boolean needEncrypt;
    private boolean tryAnotherWhenFail = true;
    @SerializedName("target_platform")
    private OfficeConversionPlatform targetPlatform = OfficeConversionPlatform.UNKNOWN;
    private int version;
    @SerializedName("pspdfkit_info")
    private PSPDFKitInfo pspdfkitInfo;
    @SerializedName("additional_infos")
    private Map<String, Object> additionalInfos;
    private transient String jobMd5;

    public OfficeConversionPlatform getLastPlatform() {
        return lastPlatform;
    }

    public void setLastPlatform(OfficeConversionPlatform lastPlatform) {
        this.lastPlatform = lastPlatform;
    }

    public boolean isForceConvert() {
        return forceConvert;
    }

    public void setForceConvert(boolean forceConvert) {
        this.forceConvert = forceConvert;
    }

    public Map<String, ConversionMetainfo> getConvertMetas() {
        return this.convertMetas;
    }

    public void rmConvertMeta(String convertKind) {
        if (convertMetas != null && convertMetas.containsKey(convertKind)) {
            convertMetas.remove(convertKind);
        }
    }

    public void setExtension(String ext) {
        this.extension = ext;
    }

    public String getExtension() {
        if (extension == null) {
            return null;
        }
        return this.extension.trim().toLowerCase();
    }

    public void setConvertMetas(Map<String, ConversionMetainfo> convertKinds) {
        this.convertMetas = convertKinds;
    }

    public Set<String> getConvertedKinds() {
        return convertedKinds;
    }

    public void setConvertedKinds(Set<String> convertedKinds) {
        this.convertedKinds = convertedKinds;
    }

    public boolean isTryAnotherWhenFail() {
        return tryAnotherWhenFail;
    }

    public void setTryAnotherWhenFail(boolean tryAnotherWhenFail) {
        this.tryAnotherWhenFail = tryAnotherWhenFail;
    }

    public boolean isNeedEncrypt() {
        return needEncrypt;
    }

    public void setNeedEncrypt(boolean needEncrypt) {
        this.needEncrypt = needEncrypt;
    }

    public OfficeConversionPlatform getTargetPlatform() {
        return targetPlatform;
    }

    public void setTargetPlatform(OfficeConversionPlatform targetPlatform) {
        this.targetPlatform = targetPlatform;
    }

    public int getVersion() {
        return version;
    }

    public void setVersion(int version) {
        this.version = version;
    }

    public Map<String, Object> getAdditionalInfos() {
        return additionalInfos;
    }

    public void setAdditionalInfos(Map<String, Object> additionalInfos) {
        this.additionalInfos = additionalInfos;
    }

    // Deep copy
    @Override
    public Object clone() throws CloneNotSupportedException {
        try {
            String strJson = GsonUtils.getGson().toJson(this);
            ConversionJob job = GsonUtils.getGson().fromJson(strJson,
                    ConversionJob.class);
            return job;
        }
        catch (Exception e) {
            throw new CloneNotSupportedException(e.getMessage());
        }
    }

    public PSPDFKitInfo getPspdfkitInfo() {
        return pspdfkitInfo;
    }

    public void setPspdfkitInfo(PSPDFKitInfo pspdfkitInfo) {
        this.pspdfkitInfo = pspdfkitInfo;
    }

    public String getJobMd5() {
        return jobMd5;
    }

    public void setJobMd5(String jobMd5) {
        this.jobMd5 = jobMd5;
    }

    public ConversionMetainfo getGlobalMeta() {
        return globalMeta;
    }

    public void setGlobalMeta(ConversionMetainfo globalMeta) {
        this.globalMeta = globalMeta;
    }
}
