package com.egeio.services.transformer.models;

public enum ConversionStatus {
    CONVERTED(JobStatus.UPLOADING, false),
    UPLOADED(JobStatus.UPLOADED, false),
    FAILED(JobStatus.FAILED, true),
    IGNORE(JobStatus.IGNORED, false),
    EMPTY(JobStatus.EMPTY, true),
    NOT_SUPPORT(JobStatus.NOT_SUPPORT, true),
    TOO_LARGE(JobStatus.TOO_LARGE, true),
    TRANSFER(JobStatus.STARTED, false),
    UNKNOWN(JobStatus.UNKNOWN, true),
    EXT_NOT_CORRECT(JobStatus.EXTENSION_NOT_CORRECT, true),
    PROTECTED_FILE(JobStatus.PROTECTED_FILE, true);

    private JobStatus jobStatus;
    private boolean fail;

    public JobStatus getJobStatus() {
        return jobStatus;
    }

    public boolean isFail() {
        return fail;
    }

    private ConversionStatus(JobStatus status, boolean fail) {
        this.jobStatus = status;
        this.fail = fail;
    }
}
