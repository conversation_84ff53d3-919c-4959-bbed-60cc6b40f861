package com.egeio.services.transformer.models;

import com.google.gson.annotations.SerializedName;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

@Slf4j
public class WatermarkInfo extends CustomWatermarkInfo {
    @SerializedName("user_id")
    private long userId;
    @SerializedName("user_name")
    private String userName;
    @SerializedName("login")
    private String login;

    public long getUserId() {
        return userId;
    }

    public void setUserId(long userId) {
        this.userId = userId;
    }

    public String getUserName() {
        return userName;
    }

    public void setUserName(String userName) {
        this.userName = userName;
    }

    public String getLogin() {
        return login;
    }

    public void setLogin(String login) {
        this.login = login;
    }

    @Override
    public String getWatermark() {

        StringBuilder builder = new StringBuilder();
        String watermarkContent = builder.append(userName).append(" ").append(login).toString();

        if (!isCustom()) {
            return watermarkContent;
        }else{
            //如果是自定义水印为空 返回普通水印
            String customWatermark = super.getCustomWatermark();
            if(StringUtils.isNotEmpty(customWatermark)){
                watermarkContent = customWatermark;
                for (int i =0;i< getWatermarkLineDistance();i++){
                    watermarkContent = watermarkContent + "\n";
                }
            }else{
                watermarkContent = " ";
            }
        }
        log.info("[水印信息] [是否自定义水印]isCustom():{} [水印内容]watermarkContent():{}",isCustom(),watermarkContent);
        return watermarkContent;
    }

    @Override
    public String getTargetFileUniqueId() {
        if (!isCustom())
            return String.valueOf(userId);
        return super.getTargetFileUniqueId();
    }
}
