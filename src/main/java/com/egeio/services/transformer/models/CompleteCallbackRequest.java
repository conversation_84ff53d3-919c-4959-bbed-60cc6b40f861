package com.egeio.services.transformer.models;

import java.util.Map;

import com.egeio.core.jobsystem.actors.model.AbstractAkkaModel;
import com.egeio.core.webclient.BaseRequest;
import com.google.gson.annotations.SerializedName;

public class CompleteCallbackRequest extends BaseRequest {
    private static final long serialVersionUID = -8245172038818292326L;

    public static class CompleteConvertInfo extends AbstractAkkaModel {
        private static final long serialVersionUID = -497217650183245371L;
        @SerializedName("type")
        private PreviewType type;
        @SerializedName("page_count")
        private int pageCount;
        @SerializedName("view_file_size")
        private long viewFileSize;
        @SerializedName("task_id")
        private String taskId;
        @SerializedName("status")
        private JobStatus status;
        @SerializedName("watermark")
        private WatermarkInfo watermarkInfo;

        public PreviewType getType() {
            return this.type;
        }

        public JobStatus getStatus() {
            return status;
        }

        public void setStatus(JobStatus status) {
            this.status = status;
        }

        public void setType(PreviewType type) {
            this.type = type;
        }

        public int getPageCount() {
            return this.pageCount;
        }

        public void setPageCount(int pageCount) {
            this.pageCount = pageCount;
        }

        public void setTaskId(String taskId) {
            this.taskId = taskId;
        }

        public WatermarkInfo getWatermarkInfo() {
            return watermarkInfo;
        }

        public void setWatermarkInfo(WatermarkInfo watermarkInfo) {
            this.watermarkInfo = watermarkInfo;
        }

        public long getViewFileSize() {
            return viewFileSize;
        }

        public void setViewFileSize(long viewFileSize) {
            this.viewFileSize = viewFileSize;
        }
    }

    @SerializedName("file_storage_id")
    private long fileStorageId;
    @SerializedName("representation_files")
    private Map<String, CompleteConvertInfo> convertInfos;
    @SerializedName("image_status")
    private ImageStatus imageStatus;
    @SerializedName("conversion_platform")
    private OfficeConversionPlatform platform = OfficeConversionPlatform.UNKNOWN;
    @SerializedName("additional_infos")
    private Map<String, Object> additionalInfos;

    public OfficeConversionPlatform getPlatform() {
        return platform;
    }

    public void setPlatform(OfficeConversionPlatform platform) {
        this.platform = platform;
    }

    public long getFileStorageId() {
        return this.fileStorageId;
    }

    public void setFileStorageId(long fileStorageId) {
        this.fileStorageId = fileStorageId;
    }

    public Map<String, CompleteConvertInfo> getConvertInfos() {
        return this.convertInfos;
    }

    public void setConvertInfos(Map<String, CompleteConvertInfo> convertInfos) {
        this.convertInfos = convertInfos;
    }

    public ImageStatus getImageStatus() {
        return imageStatus;
    }

    public void setImageStatus(ImageStatus imageStatus) {
        this.imageStatus = imageStatus;
    }

    public Map<String, Object> getAdditionalInfos() {
        return additionalInfos;
    }

    public void setAdditionalInfos(Map<String, Object> additionalInfos) {
        this.additionalInfos = additionalInfos;
    }

}
