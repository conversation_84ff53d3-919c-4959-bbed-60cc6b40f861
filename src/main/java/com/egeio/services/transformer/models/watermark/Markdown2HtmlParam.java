package com.egeio.services.transformer.models.watermark;

import com.egeio.core.utils.Utils;
import com.egeio.services.transformer.preconvert.AbstractPreConvert;
import com.egeio.services.transformer.preconvert.ICmdPreConvert;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.exec.CommandLine;

import java.io.File;
import java.io.IOException;

/**
 * 水印下载markdown参数
 */
@Data
@NoArgsConstructor
public class Markdown2HtmlParam extends AbstractPreConvert implements ICmdPreConvert {

    private File tmpFile;

    public Markdown2HtmlParam(File tmpFile){
        this.tmpFile = tmpFile;
    }

    @Override
    public CommandLine getCMD() throws IOException {
        return Utils.getCMD("markdown2html", getTargetFilePath(), tmpFile.getAbsolutePath());
    }


    public CommandLine get2UtfCMD() throws IOException {
        return Utils.getCMD("2utf8", tmpFile.getAbsolutePath(), getSourceFilePath());
    }

    @Override
    public String getDefaultTargetType() throws Exception {
        return "html";
    }

}
