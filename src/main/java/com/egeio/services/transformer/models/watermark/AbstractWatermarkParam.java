package com.egeio.services.transformer.models.watermark;

import lombok.Data;

import java.io.File;

/**
 * <AUTHOR>
 * @date 2022/3/22 下午6:04
 */
@Data
public abstract class AbstractWatermarkParam {
    protected File workingDir; // conversion working dir
    protected File sourceFile; // conversion source file
    protected File targetFile; // conversion target file

    protected String targetType; // conversion target type
    protected String sourceType; // conversion source type
    protected String convertKind; // conversion kind

    public String getSourceFilePath() {
        return this.sourceFile.getAbsolutePath();
    }

    public String getTargetFilePath() {
        return this.targetFile.getAbsolutePath();
    }

    public String getSourceType() {
        return this.sourceType;
    }

    protected abstract String getDefaultTargetType() throws Exception;



}
