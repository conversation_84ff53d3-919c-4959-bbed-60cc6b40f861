package com.egeio.services.transformer.models;

import java.util.Map;

import com.egeio.core.jobsystem.actors.model.AbstractAkkaModel;
import com.google.gson.annotations.SerializedName;

public class ConversionMetainfo extends AbstractAkkaModel {
    private static final long serialVersionUID = -5086055104066130978L;
    @SerializedName("content_type")
    private String contentType;
    @SerializedName("type")
    private PreviewType type;
    @SerializedName("format")
    private String format;
    @SerializedName("spec")
    private Map<String, Integer> spec;
    @SerializedName("watermark")
    private WatermarkInfo watermarkInfo;
    private String password;
    @SerializedName("owner_password")
    private String ownerPassword;
    @SerializedName("task_id")
    private String taskId;

    public String getContentType() {
        return this.contentType;
    }

    public PreviewType getType() {
        return this.type;
    }

    public String getFormat() {
        return this.format;
    }

    public String getTaskId() {
        return this.taskId;
    }

    public int getHeight() {
        return this.spec.get("height");
    }

    public int getWidth() {
        return this.spec.get("width");
    }

    public WatermarkInfo getWatermarkInfo() {
        return watermarkInfo;
    }

    public String getPassword() {
        return password;
    }

    public void setPassword(String password) {
        this.password = password;
    }

    public String getOwnerPassword() {
        return ownerPassword;
    }

    public void setOwnerPassword(String ownerPassword) {
        this.ownerPassword = ownerPassword;
    }
}
