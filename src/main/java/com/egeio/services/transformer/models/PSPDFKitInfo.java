package com.egeio.services.transformer.models;

import com.egeio.core.jobsystem.actors.model.AbstractAkkaModel;
import com.google.gson.annotations.SerializedName;

public class PSPDFKitInfo extends AbstractAkkaModel {

    private static final long serialVersionUID = 1718483640743354259L;

    @SerializedName("service_server_id")
    private int serviceServerId;
    @SerializedName("upload_host")
    private String uploadHost;
    @SerializedName("preview_host")
    private String previewHost;
    @SerializedName("secret_token")
    private String secretToken;
    @SerializedName("jwt_private_key")
    private String privateKey;

    public String getSecretToken() {
        return secretToken;
    }

    public void setSecretToken(String secretToken) {
        this.secretToken = secretToken;
    }

    public String getUploadHost() {
        return uploadHost;
    }

    public void setUploadHost(String host) {
        this.uploadHost = host;
    }

    public int getServiceServerId() {
        return serviceServerId;
    }

    public void setServiceServerId(int serviceServerId) {
        this.serviceServerId = serviceServerId;
    }

    public String getPrivateKey() {
        return privateKey;
    }

    public void setPrivateKey(String privateKey) {
        this.privateKey = privateKey;
    }

    public String getPreviewHost() {
        return previewHost;
    }

    public void setPreviewHost(String previewHost) {
        this.previewHost = previewHost;
    }
}
