package com.egeio.services.transformer.models;

import java.lang.reflect.Type;

import com.google.gson.JsonDeserializationContext;
import com.google.gson.JsonDeserializer;
import com.google.gson.JsonElement;
import com.google.gson.JsonParseException;
import com.google.gson.JsonPrimitive;
import com.google.gson.JsonSerializationContext;
import com.google.gson.JsonSerializer;

public class PreviewStatusSerializer implements JsonSerializer<PreviewStatus>,
        JsonDeserializer<PreviewStatus> {

    @Override
    public PreviewStatus deserialize(JsonElement json, Type typeOfT,
            JsonDeserializationContext context) throws JsonParseException {
        return PreviewStatus.getPreviewStatus(json.getAsInt());
    }

    @Override
    public JsonElement serialize(PreviewStatus src, Type typeOfSrc,
            JsonSerializationContext context) {
        return new JsonPrimitive(src.getInt());
    }

}