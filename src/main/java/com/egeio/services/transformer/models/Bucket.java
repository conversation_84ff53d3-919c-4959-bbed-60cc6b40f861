package com.egeio.services.transformer.models;

public enum Bucket {
    TMP("tmp"),
    PREVIEW("preview"),
    THUMBNAIL("thumbnail"),
    WATERFLOW("waterflow"),
    WATERMARK_PREVIEW("watermark_preview");

    private String s;

    private Bucket(String s) {
        this.s = s;
    }

    public String getStr() {
        return s;
    }

    public static Bucket getBucket(String s) {
        for (Bucket b : values()) {
            if (b.s.equals(s)) {
                return b;
            }
        }

        return PREVIEW;
    }
}
