package com.egeio.services.transformer.models;

import com.egeio.services.transformer.preconvert.AbstractPreConvert;

import akka.actor.ActorRef;

public class ConversionWork {
    private ActorRef caller; // mainWorker
    private Class<?> workClass;
    private ConversionJob job;
    private AbstractPreConvert preConvert;

    private ActorRef appWorker; // the worker for conversion

    public ConversionWork(ActorRef caller, Class<?> workClass,
            ConversionJob job, AbstractPreConvert preConvert) {
        super();
        this.caller = caller;
        this.workClass = workClass;
        this.job = job;
        this.preConvert = preConvert;
    }

    public ActorRef getCaller() {
        return caller;
    }

    public Class<?> getWorkClass() {
        return workClass;
    }

    public ConversionJob getJob() {
        return job;
    }

    public AbstractPreConvert getPreConvert() {
        return preConvert;
    }

    public ActorRef getAppWorker() {
        return appWorker;
    }

    public void setAppWorker(ActorRef appWorker) {
        this.appWorker = appWorker;
    }

    @Override
    public boolean equals(Object obj) {
        if (job != null && obj != null && obj instanceof ConversionWork) {
            return job.equals(ConversionWork.class.cast(obj));
        }

        return super.equals(obj);
    }

    @Override
    public int hashCode() {
        if (job != null) {
            return job.hashCode();
        }
        return super.hashCode();
    }
}
