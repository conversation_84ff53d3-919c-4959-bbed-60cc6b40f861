package com.egeio.services.transformer.models;

public enum OfficeConversionPlatform {
    UNKNOWN(0),
    WIN(1),
    ASPOSE(2);

    private int intValue;

    private OfficeConversionPlatform(int i) {
        this.intValue = i;
    }

    public int getInt() {
        return intValue;
    }

    public static OfficeConversionPlatform getConversionPaltform(int i) {
        for (OfficeConversionPlatform c : values()) {
            if (c.intValue == i) {
                return c;
            }
        }
        return UNKNOWN;
    }
}
