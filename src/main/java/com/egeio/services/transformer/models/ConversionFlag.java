package com.egeio.services.transformer.models;

public enum ConversionFlag {
    UNKNOWN(0),
    NO_CONVERSION(1),
    CONVERSION(2),
    ORIGIN(3);

    private int intValue;

    private ConversionFlag(int i) {
        this.intValue = i;
    }

    public int getInt() {
        return intValue;
    }

    public static ConversionFlag getConversionFlag(int i) {
        for (ConversionFlag flag : values()) {
            if (flag.getInt() == i) {
                return flag;
            }
        }

        return UNKNOWN;
    }
}
