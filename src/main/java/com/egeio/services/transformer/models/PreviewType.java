package com.egeio.services.transformer.models;

public enum PreviewType {
    THUMB<PERSON>IL("thumbnail"),
    PREVIEW("preview"),
    WATER_FLOW("water_flow"),
    WATERMARK_PREVIEW("watermark_preview"),
    UNKNOWN("");

    private PreviewType(String str) {
        this.str = str;
    }

    private String str;

    public String getStr() {
        return str;
    }

    public static PreviewType getPreviewType(String str) {
        for (PreviewType type : values()) {
            if (type.str.equals(str)) {
                return type;
            }
        }

        return UNKNOWN;
    }
}
