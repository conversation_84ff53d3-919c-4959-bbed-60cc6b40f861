package com.egeio.services.transformer.models;

import com.egeio.core.jobsystem.actors.model.AbstractAkkaModel;

public class Image<PERSON>eta extends AbstractAkkaModel {
    private static final long serialVersionUID = 1737889130265755907L;
    private long height;
    private long width;
    private boolean forceConvert;
    private boolean reachMax;
    private int rotation = 1;

    public boolean isReachMax() {
        return reachMax;
    }

    public void setReachMax(boolean reachMax) {
        this.reachMax = reachMax;
    }

    public long getHeight() {
        return height;
    }

    public void setHeight(long height) {
        this.height = height;
    }

    public long getWidth() {
        return width;
    }

    public void setWidth(long width) {
        this.width = width;
    }

    public boolean isForceConvert() {
        return forceConvert;
    }

    public void setForceConvert(boolean forceConvert) {
        this.forceConvert = forceConvert;
    }

    public int getRotation() {
        return rotation;
    }

    public void setRotation(int rotation) {
        this.rotation = rotation;
    }
}
