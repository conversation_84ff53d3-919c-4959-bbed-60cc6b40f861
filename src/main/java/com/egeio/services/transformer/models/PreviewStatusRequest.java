package com.egeio.services.transformer.models;

import com.egeio.core.webclient.BaseRequest;
import com.google.gson.annotations.SerializedName;

public class PreviewStatusRequest extends BaseRequest {
    private static final long serialVersionUID = -1736076750666001526L;
    @SerializedName("file_storage_id")
    private long fileStorageId;
    @SerializedName("convert_kind")
    private String convertKind;
    @SerializedName("watermark")
    private WatermarkInfo watermarkInfo;

    public long getFileStorageId() {
        return fileStorageId;
    }

    public void setFileStorageId(long fileStorageId) {
        this.fileStorageId = fileStorageId;
    }

    public String getConvertKind() {
        return convertKind;
    }

    public void setConvertKind(String convertKind) {
        this.convertKind = convertKind;
    }

    public WatermarkInfo getWatermarkInfo() {
        return watermarkInfo;
    }

    public void setWatermarkInfo(WatermarkInfo watermarkInfo) {
        this.watermarkInfo = watermarkInfo;
    }
}
