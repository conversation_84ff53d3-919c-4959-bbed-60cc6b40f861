package com.egeio.services.transformer.models;

import java.lang.reflect.Type;

import com.google.gson.JsonDeserializationContext;
import com.google.gson.JsonDeserializer;
import com.google.gson.JsonElement;
import com.google.gson.JsonParseException;
import com.google.gson.JsonPrimitive;
import com.google.gson.JsonSerializationContext;
import com.google.gson.JsonSerializer;

public class ConversionFlagSerializer implements JsonSerializer<ConversionFlag>,
        JsonDeserializer<ConversionFlag> {

    @Override
    public ConversionFlag deserialize(JsonElement json, Type typeOfT,
            JsonDeserializationContext context) throws JsonParseException {
        return ConversionFlag.getConversionFlag(json.getAsInt());
    }

    @Override
    public JsonElement serialize(ConversionFlag src, Type typeOfSrc,
            JsonSerializationContext context) {
        return new JsonPrimitive(src.getInt());
    }

}
