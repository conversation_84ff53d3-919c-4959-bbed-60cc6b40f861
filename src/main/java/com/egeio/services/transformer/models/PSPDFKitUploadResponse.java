package com.egeio.services.transformer.models;

import com.egeio.core.jobsystem.actors.model.AbstractAkkaModel;
import com.google.gson.annotations.SerializedName;

import java.util.List;
import java.util.Map;

public class PSPDFKitUploadResponse extends AbstractAkkaModel {
    private static final long serialVersionUID = -6772555577894481441L;

    private PSPDFKitUploadResponseData data;

    public PSPDFKitUploadResponseData getData() {
        return data;
    }

    public void setData(PSPDFKitUploadResponseData data) {
        this.data = data;
    }

    public class PSPDFKitUploadResponseData extends AbstractAkkaModel {

        private static final long serialVersionUID = 3776821219234684546L;

        private String createdAt;
        @SerializedName("document_id")
        private String documentId;
        private List<Map<String, String>> errors;
        @SerializedName("password_protected")
        private boolean passwordProtected;
        private String sourcePdfSha256;
        private String title;

        public String getCreatedAt() {
            return createdAt;
        }

        public void setCreatedAt(String createdAt) {
            this.createdAt = createdAt;
        }

        public String getDocumentId() {
            return documentId;
        }

        public void setDocumentId(String documentId) {
            this.documentId = documentId;
        }

        public boolean isPasswordProtected() {
            return passwordProtected;
        }

        public void setPasswordProtected(boolean passwordProtected) {
            this.passwordProtected = passwordProtected;
        }

        public String getSourcePdfSha256() {
            return sourcePdfSha256;
        }

        public void setSourcePdfSha256(String sourcePdfSha256) {
            this.sourcePdfSha256 = sourcePdfSha256;
        }

        public String getTitle() {
            return title;
        }

        public void setTitle(String title) {
            this.title = title;
        }

        public List<Map<String, String>> getErrors() {
            return errors;
        }

        public void setErrors(List<Map<String, String>> errors) {
            this.errors = errors;
        }
    }
}
