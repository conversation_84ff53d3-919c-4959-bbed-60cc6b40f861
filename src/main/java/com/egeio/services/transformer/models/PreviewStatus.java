package com.egeio.services.transformer.models;

public enum PreviewStatus {
    NOT_READY(0),
    ORIGIN(1),
    CONVERTED(2);

    private int intValue;

    private PreviewStatus(int i) {
        this.intValue = i;
    }

    public int getInt() {
        return intValue;
    }

    public static PreviewStatus getPreviewStatus(int i) {
        for (PreviewStatus flag : values()) {
            if (flag.getInt() == i) {
                return flag;
            }
        }

        return NOT_READY;
    }
}
