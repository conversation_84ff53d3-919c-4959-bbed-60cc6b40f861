package com.egeio.services.transformer.models;

import java.util.ArrayList;
import java.util.List;

import org.dom4j.Element;
import com.egeio.core.config.Config;

public enum OfficeType {
    WORD("word"),
    EXCEL("excel"),
    PPT("ppt"),
    VISIO("visio"),
    PROJECT("project"),
    UNKNOWN("");

    private String id;
    private List<String> exts = new ArrayList<String>();
    private String queue;

    private OfficeType(String id) {
        this.id = id;
    }

    public String getId() {
        return id;
    }

    public List<String> getExts() {
        return exts;
    }

    public void addExts(String ext) {
        this.exts.add(ext);
    }

    public String getQueue() {
        return queue;
    }

    public void setQueue(String queue) {
        this.queue = queue;
    }

    public static OfficeType getTypeViaId(String id) {
        for (OfficeType type : values()) {
            if (type.getId().equals(id)) {
                return type;
            }
        }
        return UNKNOWN;
    }

    public static OfficeType getTypeViaExt(String ext) {
        for (OfficeType type : values()) {
            if (type.getExts().contains(ext.trim().toLowerCase())) {
                return type;
            }
        }
        return UNKNOWN;
    }

    public static boolean isOfficeType(String ext){
        for (OfficeType type : values()) {
            if (type.getExts().contains(ext.trim().toLowerCase())) {
                return true;
            }
        }
        return false;

    }

    static {
        Element el = Config.getConfig().getElement(
                "/configuration/windows-office-converter/mapping_list");
        @SuppressWarnings("unchecked")
        List<Element> mappings = el.elements();
        for (Element e : mappings) {
            String extsStr = e.attributeValue("extensions");
            String[] exts = extsStr.split(",");
            String id = e.attributeValue("id").trim();
            String queue = e.attributeValue("queue").trim();

            OfficeType type = getTypeViaId(id);
            for (String ext : exts) {
                type.addExts(ext.trim().toLowerCase());
            }
            type.setQueue(queue);
        }
    }
}
