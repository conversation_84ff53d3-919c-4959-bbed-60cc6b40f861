package com.egeio.services.transformer.models.watermark;

import com.egeio.services.transformer.preconvert.PrePdfWatermark;
import com.egeio.services.transformer.utils.ImageUtils;
import lombok.*;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.io.Serializable;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2022/3/30 下午4:44
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class WatermarkParam implements Serializable {

    private Integer fontSize;

    private Float opacity;

    private String fontColor;

    private List<String> watermarkContent;

    private Integer imageWatermarkSizeWidth;

    private Integer imageWatermarkSizeHeight;

    private Integer lineDistance = 5;

    private String extension;

    private Boolean disableEdit = false;
    /**
     * 未指定密码会生成随机密码
     */
    private String disableEditPassword;


    @Override
    public String toString() {
        return "WatermarkParam{" +
                "fontSize=" + fontSize +
                ", opacity=" + opacity +
                ", fontColor='" + fontColor + '\'' +
                ", watermarkContent=" + watermarkContent +
                ", imageWatermarkSizeWidth=" + imageWatermarkSizeWidth +
                ", imageWatermarkSizeHeight=" + imageWatermarkSizeHeight +
                ", extension='" + extension + '\'' +
                ", watermarkLineDistance='" + lineDistance + '\'' +
                '}';
    }

    public void genWatermarkParam() {
//        try {
//            List<String> watermarkContentListTmp = new ArrayList<>();
//            for (String watermarkContent:this.watermarkContent) {
//                watermarkContentListTmp.add(new String(watermarkContent.getBytes("ISO-8859-1"), "utf8"));
//            }
//            this.watermarkContent = watermarkContentListTmp;
//        } catch (UnsupportedEncodingException e) {
//            throw new TransformerException(Errors.SYSTEM_ERROR);
//        }
        this.fontSize = Objects.nonNull(this.fontSize) ? fontSize : PrePdfWatermark.FONTSIZE;
        this.opacity = Objects.nonNull(this.opacity) ? this.opacity : PrePdfWatermark.OPACITY;
        this.fontColor = StringUtils.hasLength(this.fontColor) ? this.fontColor : PrePdfWatermark.COLOR;
        this.imageWatermarkSizeWidth = Objects.nonNull(this.imageWatermarkSizeWidth) ? this.imageWatermarkSizeWidth : ImageUtils.WATERMARK_SIZE_WIDTH;
        this.imageWatermarkSizeHeight = Objects.nonNull(this.imageWatermarkSizeHeight) ? this.imageWatermarkSizeHeight : ImageUtils.WATERMARK_SIZE_HEIGHT;

    }


    public String getImageWatermarkContent() {
        StringBuilder builder = new StringBuilder();

        if(!CollectionUtils.isEmpty(watermarkContent)){
            for (String content:watermarkContent) {
                builder.append(content).append("\n");
            }
            return builder.toString();
        }
        return " ";
    }
}
