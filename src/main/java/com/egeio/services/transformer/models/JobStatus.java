package com.egeio.services.transformer.models;

public enum JobStatus {
    STARTED("started", false, 0),
    DOWNLOADING("downloading", false, 1),
    DOWNLOADED("downloaded", false, 2),
    UPLOADING("uploading", false, 3),
    UPLOADED("uploaded", false, 4),
    FAILED("failed", true, 5),
    TOO_LARGE("too_large", true, 5),
    EXTENSION_NOT_CORRECT("extension_not_correct", true, 5),
    NOT_SUPPORT("not_support", true, 5),
    IGNORED("ignored", false, 5),
    EMPTY("empty", true, 5),
    UNKNOWN("", true, 5),
    PROTECTED_FILE("protected_file", true, 5);

    private String str;
    private int index;
    private boolean fail;

    public boolean isFail() {
        return fail;
    }

    private JobStatus(String str, boolean fail, int index) {
        this.str = str;
        this.fail = fail;
        this.index = index;
    }

    public String getStr() {
        return str;
    }

    public int getIndex() {
        return index;
    }

    public static JobStatus getJobStatus(String str) {
        for (JobStatus status : values()) {
            if (status.str.equals(str)) {
                return status;
            }
        }
        return UNKNOWN;
    }
}
