package com.egeio.services.transformer.models;

import com.google.gson.annotations.SerializedName;
import org.springframework.util.CollectionUtils;

import javax.annotation.Nullable;
import java.util.List;

/**
 * 自定义水印
 */
public class CustomWatermarkInfo {
    @SerializedName("content")
    protected String content;
    @SerializedName("unique_id")
    protected String uniqueId;
    @SerializedName("is_custom")
    protected boolean custom;

    @SerializedName("font_size")
    protected Integer fontSize;

    @SerializedName("opacity")
    protected Float opacity;

    @SerializedName("font_color")
    protected String fontColor;

    /**
     * 水印文本列表：预览者 修改者 自定的文本三部分组成
     */
    @SerializedName("content_list")
    protected List<String> contentList;

    @SerializedName("now_time")
    private String nowTime = "";

    @SerializedName("ip")
    private String ip = "";

    /**
     * 水印展示宽度比例
     */
    @SerializedName("position_ratio_with")
    private int positionRatioWith = 7;

    /**
     * 水印展示高度比例
     */
    @SerializedName("position_ratio_height")
    private int positionRatioHeight = 7;

    /**
     * 水印行之间偏移量
     */
    @SerializedName("watermark_line_offset")
    private int watermarkLineOffset = 10;

    /**
     * 水印行之间间距
     */
    @SerializedName("watermark_line_Distance")
    private int watermarkLineDistance = 5;

    /**
     * 行水印之间间距多少个DELIMITER_X
     */
    @SerializedName("watermark_line_repeat")
    private int watermarkLineRepeat = 10;


    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }

    public String getUniqueId() {
        return uniqueId;
    }

    public void setUniqueId(String uniqueId) {
        this.uniqueId = uniqueId;
    }

    public String getWatermark() {
        return getCustomWatermark();
    }

    public String getCustomWatermark() {

        StringBuilder builder = new StringBuilder();
        if(!CollectionUtils.isEmpty(contentList)){
            for (String content : contentList) {
                builder.append(content).append("\n");
            }
            return builder.toString();
        }
        return "";
    }


    public String getTargetFileUniqueId() {
        return uniqueId;
    }

    public boolean isCustom(){
        return custom;
    }

    public void setCustom(boolean custom) {
        this.custom = custom;
    }


    public int getFontSize() {
        return fontSize;
    }

    public void setFontSize(int fontSize) {
        this.fontSize = fontSize;
    }

    public float getOpacity() {
        return opacity;
    }

    public void setOpacity(float opacity) {
        this.opacity = opacity;
    }

    public String getFontColor() {
        return fontColor;
    }

    public void setFontColor(String fontColor) {
        this.fontColor = fontColor;
    }

    public void setFontSize(Integer fontSize) {
        this.fontSize = fontSize;
    }

    public void setOpacity(Float opacity) {
        this.opacity = opacity;
    }

    public List<String> getContentList() {
        return contentList;
    }

    public void setContentList(List<String> contentList) {
        this.contentList = contentList;
    }

    public String getNowTime() {
        return nowTime;
    }

    public String getIp() {
        return ip;
    }

    public void setNowTime(String nowTime) {
        this.nowTime = nowTime;
    }

    public void setIp(String ip) {
        this.ip = ip;
    }

    public int getPositionRatioWith() {
        return positionRatioWith;
    }

    public void setPositionRatioWith(int positionRatioWith) {
        this.positionRatioWith = positionRatioWith;
    }

    public int getPositionRatioHeight() {
        return positionRatioHeight;
    }

    public void setPositionRatioHeight(int positionRatioHeight) {
        this.positionRatioHeight = positionRatioHeight;
    }

    public int getWatermarkLineOffset() {
        return watermarkLineOffset;
    }

    public void setWatermarkLineOffset(int watermarkLineOffset) {
        this.watermarkLineOffset = watermarkLineOffset;
    }

    public int getWatermarkLineDistance() {
        return watermarkLineDistance;
    }

    public void setWatermarkLineDistance(int watermarkLineDistance) {
        this.watermarkLineDistance = watermarkLineDistance;
    }

    public int getWatermarkLineRepeat() {
        return watermarkLineRepeat;
    }

    public void setWatermarkLineRepeat(int watermarkLineRepeat) {
        this.watermarkLineRepeat = watermarkLineRepeat;
    }
}
