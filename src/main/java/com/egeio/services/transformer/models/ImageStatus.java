package com.egeio.services.transformer.models;

import com.google.gson.annotations.SerializedName;

public class ImageStatus {
    @SerializedName("conversion_on_2048")
    private ConversionFlag conversion2048 = ConversionFlag.UNKNOWN;
    @SerializedName("conversion_on_1024")
    private ConversionFlag conversion1024 = ConversionFlag.UNKNOWN;
    @SerializedName("rotation")
    private int rotation = 1;
    @SerializedName("ratio")
    private double ratio;

    public ConversionFlag getConversion2048() {
        return conversion2048;
    }

    public void setConversion2048(ConversionFlag conversion2048) {
        this.conversion2048 = conversion2048;
    }

    public ConversionFlag getConversion1024() {
        return conversion1024;
    }

    public void setConversion1024(ConversionFlag conversion1024) {
        this.conversion1024 = conversion1024;
    }

    public int getRotation() {
        return rotation;
    }

    public void setRotation(int rotation) {
        this.rotation = rotation;
    }

    public double getRatio() {
        return ratio;
    }

    public void setRatio(double ratio) {
        this.ratio = ratio;
    }

    public void setRatio(long height, long width) {
        if (width == 0) {
            this.ratio = 0;
        }
        else {
            this.ratio = height * 1.0 / width;
        }
    }

    private ConversionFlag assignConversionFlag(ConversionFlag originFlag,
            ConversionFlag newFlag) {
        if (originFlag == ConversionFlag.UNKNOWN) {
            return newFlag;
        }
        else if (newFlag != ConversionFlag.UNKNOWN) {
            return newFlag;
        }

        return originFlag;
    }

    public void assign(ImageStatus newStatus) {
        if (newStatus != null) {
            conversion2048 = assignConversionFlag(conversion2048,
                    newStatus.conversion2048);
            conversion1024 = assignConversionFlag(conversion1024,
                    newStatus.conversion1024);
            rotation = newStatus.rotation;
            ratio = newStatus.ratio;
        }
    }
}
