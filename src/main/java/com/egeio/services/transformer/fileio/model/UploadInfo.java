package com.egeio.services.transformer.fileio.model;

import akka.actor.ActorRef;
import com.egeio.core.jobsystem.actors.message.UploadCompleted;
import com.egeio.core.jobsystem.actors.message.UploadFailed;
import com.egeio.core.web.entity.BucketInfo;
import com.egeio.core.webclient.AkkaWebClient;
import com.egeio.services.transformer.models.ConversionJob;

import java.io.File;
import java.util.List;

public class UploadInfo {
    private BucketInfo bucketInfo;
    private ConversionJob conversionJob;
    private ActorRef sender;
    private AkkaWebClient webClient;

    private List<File> localFiles;
    private String kind;
    private Class<? extends UploadFailed> failMessage;
    private Class<? extends UploadCompleted> completeMessage;
    private int filesUploaded = 0;
    private final int filesTotal;
    private String bucketType;


    public UploadInfo(BucketInfo bucketInfo, String bucketType, ConversionJob job, List<File> localFiles, ActorRef sender, String kind, AkkaWebClient webClient, Class<? extends UploadCompleted> completeMessage, Class<? extends UploadFailed> failMessage) {
        this.bucketInfo = bucketInfo;
        this.bucketType = bucketType;
        this.conversionJob = job;
        this.localFiles = localFiles;
        this.sender = sender;
        this.kind = kind;
        this.webClient = webClient;
        this.failMessage = failMessage;
        this.completeMessage = completeMessage;
        this.filesTotal = localFiles.size();
    }

    public BucketInfo getBucketInfo() {
        return bucketInfo;
    }

    public void setBucketInfo(BucketInfo bucketInfo) {
        this.bucketInfo = bucketInfo;
    }

    public ActorRef getSender() {
        return sender;
    }

    public void setSender(ActorRef sender) {
        this.sender = sender;
    }

    public AkkaWebClient getWebClient() {
        return webClient;
    }

    public void setWebClient(AkkaWebClient webClient) {
        this.webClient = webClient;
    }

    public List<File> getLocalFiles() {
        return localFiles;
    }

    public void setLocalFiles(List<File> localFiles) {
        this.localFiles = localFiles;
    }

    public String getKind() {
        return kind;
    }

    public void setKind(String kind) {
        this.kind = kind;
    }

    public Class<? extends UploadFailed> getFailMessage() {
        return failMessage;
    }

    public void setFailMessage(Class<? extends UploadFailed> failMessage) {
        this.failMessage = failMessage;
    }

    public Class<? extends UploadCompleted> getCompleteMessage() {
        return completeMessage;
    }

    public void setCompleteMessage(Class<? extends UploadCompleted> completeMessage) {
        this.completeMessage = completeMessage;
    }

    public int getFilesUploaded() {
        return filesUploaded;
    }

    public void setFilesUploaded(int filesUploaded) {
        this.filesUploaded = filesUploaded;
    }

    public int getFilesTotal() {
        return filesTotal;
    }

    public ConversionJob getConversionJob() {
        return conversionJob;
    }

    public void setConversionJob(ConversionJob conversionJob) {
        this.conversionJob = conversionJob;
    }

    public String getBucketType() {
        return bucketType;
    }
}
