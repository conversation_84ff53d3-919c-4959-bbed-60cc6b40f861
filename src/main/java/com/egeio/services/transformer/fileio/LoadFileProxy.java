package com.egeio.services.transformer.fileio;

import akka.actor.UntypedActorContext;
import com.egeio.core.web.WebClient;
import com.egeio.services.transformer.fileio.direct.DirectLoadFileImpl;
import com.egeio.services.transformer.fileio.middle_preview.GatewayLoadFileImpl;
import com.egeio.services.transformer.fileio.middle_preview.model.LoadFileQueryDto;
import com.egeio.services.transformer.fileio.model.DownloadInfo;
import com.egeio.services.transformer.fileio.model.UploadInfo;
import com.egeio.services.transformer.models.ConversionJob;

import java.util.Map;

public class LoadFileProxy {

    private static ILoadFIle getLoadFileService(ConversionJob job, UntypedActorContext context, WebClient webClient) throws Exception {
        ILoadFIle loadFIleService = null;
        Map<String, Object> additionalInfo = job.getAdditionalInfos();
        if (null != additionalInfo && additionalInfo.containsKey(LoadFileQueryDto.AdditionalInfo_Key_LOADFILEQUERYDTO)) {
            loadFIleService = new GatewayLoadFileImpl(context, webClient, job);
        } else {
            loadFIleService = new DirectLoadFileImpl(context, webClient, job);
        }
        return loadFIleService;
    }

    public static void doUpload(ConversionJob job, UntypedActorContext context, WebClient webClient, UploadInfo uploadInfo) throws Exception {
        ILoadFIle loadFIleService = getLoadFileService(job, context, webClient);
        loadFIleService.setUploadInfo(uploadInfo);
        loadFIleService.uploadFile();
    }

    public static void doDownload(ConversionJob job, UntypedActorContext context, WebClient webClient, DownloadInfo downloadInfo) throws Exception {
        ILoadFIle loadFIleService = getLoadFileService(job, context, webClient);
        loadFIleService.setDownloadInfo(downloadInfo);
        loadFIleService.downloadFile();
    }

}
