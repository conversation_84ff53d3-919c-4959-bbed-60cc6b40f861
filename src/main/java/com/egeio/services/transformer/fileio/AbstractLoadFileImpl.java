package com.egeio.services.transformer.fileio;

import akka.actor.UntypedActorContext;
import com.aliyuncs.http.HttpRequest;
import com.egeio.core.log.Logger;
import com.egeio.core.log.LoggerFactory;
import com.egeio.core.web.WebClient;
import com.egeio.services.transformer.fileio.model.DownloadInfo;
import com.egeio.services.transformer.fileio.model.UploadInfo;
import com.egeio.services.transformer.models.ConversionJob;

public abstract class AbstractLoadFileImpl implements ILoadFIle {
    protected Logger logger = LoggerFactory.getLogger(this.getClass());

    protected UntypedActorContext actorContext;
    protected WebClient webClient;
    protected ConversionJob job;

    protected DownloadInfo downloadInfo;
    protected UploadInfo uploadInfo;

    public AbstractLoadFileImpl(UntypedActorContext context, WebClient webClient, ConversionJob job) {
        this(context, webClient, job, null, null);
    }

    public AbstractLoadFileImpl(UntypedActorContext context, WebClient webClient, ConversionJob job, UploadInfo uploadInfo) {
        this(context, webClient, job, uploadInfo, null);
    }

    public AbstractLoadFileImpl(UntypedActorContext context, WebClient webClient, ConversionJob job, DownloadInfo downloadInfo) {
        this(context, webClient, job, null, downloadInfo);
    }

    public AbstractLoadFileImpl(UntypedActorContext context, WebClient webClient, ConversionJob job, UploadInfo uploadInfo, DownloadInfo downloadInfo) {
        this.actorContext = context;
        this.webClient = webClient;
        this.job = job;
        this.uploadInfo = uploadInfo;
        this.downloadInfo = downloadInfo;
    }

    protected void prepareAuthInfo(HttpRequest request) {

    }

    public DownloadInfo getDownloadInfo() {
        return downloadInfo;
    }

    @Override
    public void setDownloadInfo(DownloadInfo downloadInfo) {
        this.downloadInfo = downloadInfo;
    }

    public UploadInfo getUploadInfo() {
        return uploadInfo;
    }

    @Override
    public void setUploadInfo(UploadInfo uploadInfo) {
        this.uploadInfo = uploadInfo;
    }


    @Override
    public void downloadFile() throws Exception {

    }

    @Override
    public void uploadFile() throws Exception {

    }
}
