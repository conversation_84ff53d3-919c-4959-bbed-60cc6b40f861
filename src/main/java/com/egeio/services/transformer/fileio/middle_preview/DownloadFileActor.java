package com.egeio.services.transformer.fileio.middle_preview;

import akka.actor.ActorRef;
import com.egeio.core.auth.domain.GuardUrl;
import com.egeio.core.auth.domain.WebRequest;
import com.egeio.core.exception.WebClientException;
import com.egeio.core.jobsystem.actors.ABaseUntypedActor;
import com.egeio.core.jobsystem.actors.message.DownloadFailed;
import com.egeio.core.jobsystem.actors.message.DownloadStarted;
import com.egeio.core.jobsystem.actors.message.RetryFailedMessage;
import com.egeio.core.jobsystem.actors.message.WebCallFailed;
import com.egeio.core.utils.GsonUtils;
import com.egeio.core.web.entity.BaseWebResult;
import com.egeio.core.webclient.AkkaWebClient;
import com.egeio.core.webclient.message.FailWebClientTask;
import com.egeio.services.transformer.fileio.middle_preview.model.BizCallbackConfig;
import com.egeio.services.transformer.fileio.middle_preview.model.LoadFileQueryDto;
import com.egeio.services.transformer.fileio.model.DownloadInfo;
import com.google.gson.Gson;
import com.ning.http.client.FluentCaseInsensitiveStringsMap;
import com.ning.http.client.FluentStringsMap;
import com.ning.http.client.cookie.Cookie;
import org.apache.commons.io.IOUtils;

import java.io.BufferedOutputStream;
import java.io.FileOutputStream;
import java.io.OutputStream;
import java.util.HashMap;

public class DownloadFileActor extends ABaseUntypedActor {

    private AkkaWebClient webClient;
    private LoadFileQueryDto queryDto;
    private BizCallbackConfig bizCallbackConfig;
    private DownloadInfo downloadInfo;
    private OutputStream targetOutputStream;

    public DownloadFileActor(AkkaWebClient webClient, LoadFileQueryDto queryDto, BizCallbackConfig bizCallbackConfig, DownloadInfo downloadInfo) {
        this.webClient = webClient;
        this.queryDto = queryDto;
        this.bizCallbackConfig = bizCallbackConfig;
        this.downloadInfo = downloadInfo;
    }

    public void postStop() throws Exception {
        IOUtils.closeQuietly(this.targetOutputStream);
        this.logger.info(this.downloadInfo.getConversionJob().getUUID(), "STOP: download [worker: {}]", new Object[]{this.getPath()});
        super.postStop();
    }

    public void realPreStart() throws Exception {
        this.logger.info(this.downloadInfo.getConversionJob().getUUID(), "STARTUP: download [worker: {}]", new Object[]{this.getPath()});
        this.getSelf().tell(new DownloadStarted(), (ActorRef) null);
    }

    public void realOnReceive(Object message) throws Exception {
        if (message instanceof RetryFailedMessage) {
            RetryFailedMessage msg = (RetryFailedMessage) RetryFailedMessage.class.cast(message);
            DownloadFailed resultMsg = (DownloadFailed) this.downloadInfo.getFailClass().getConstructor(Throwable.class).newInstance(msg.getCause());
            this.downloadInfo.getSender().tell(resultMsg, this.getSelf());
            this.getContext().stop(this.getSelf());
        } else if (message instanceof DownloadStarted) {
            this.logger.info(this.downloadInfo.getConversionJob().getUUID(), "start downloading");
            this.internalDownload();
        } else if (this.downloadInfo.getResponseClass().isInstance(message)) {
            this.logger.info(this.downloadInfo.getConversionJob().getUUID(), "finished downloading");
            this.downloadInfo.getSender().tell(message, this.getSelf());
            this.getContext().stop(this.getSelf());
        } else if (message instanceof FailWebClientTask) {
            FailWebClientTask realMessage = (FailWebClientTask) FailWebClientTask.class.cast(message);
            Throwable cause = realMessage.getCause();
            this.downloadInfo.getSender().tell(new WebCallFailed(cause), this.getSelf());
            if (!realMessage.isFatal()) {
                WebClientException ex = new WebClientException(cause);
                ex.setFatal(realMessage.isFatal());
                throw ex;
            }

            DownloadFailed resultMsg = (DownloadFailed) this.downloadInfo.getFailClass().getConstructor(Throwable.class).newInstance(cause);
            this.downloadInfo.getSender().tell(resultMsg, this.getSelf());
        } else {
            this.unhandled(message);
        }
    }


    protected void internalDownload() throws Exception {
        // get downloadUrl
        // 根据job中additional_info回调发送
        FluentCaseInsensitiveStringsMap appendHeaders = this.queryDto.getNettyHeaders();
        HashMap<String, String> bodyMap = new HashMap<>();
        bodyMap.put("expected_kind", this.downloadInfo.getConvertKind());
        String callbackBody = GsonUtils.getGson().toJson(bodyMap);
        WebRequest webRequest = new WebRequest(new GuardUrl(bizCallbackConfig.getDownloadUrl(), bizCallbackConfig.getServiceId()),
                (FluentStringsMap) null, (FluentCaseInsensitiveStringsMap) appendHeaders, callbackBody, (Cookie[]) null);
        DownloadPresignResult resp = webClient.execute(webRequest, DownloadPresignResult.class);
        if (resp.getError() != null) {
            this.getSelf().tell(new FailWebClientTask(new Exception(resp.getError().toString()), false, uuid), (ActorRef) null);
            return;
        }

        // download file
        GuardUrl downloadGuardUrl = new GuardUrl(resp.getDownload_url(), bizCallbackConfig.getServiceId());
        FluentCaseInsensitiveStringsMap headers = queryDto.getNettyHeaders();
        FluentStringsMap queryParams = queryDto.getQueryParams();
        String body = queryDto.getBody();
        this.targetOutputStream = new BufferedOutputStream(new FileOutputStream(this.downloadInfo.getTargetPath()));
        WebRequest request = new WebRequest(downloadGuardUrl, queryParams, headers, body, (Cookie[]) null);
        request.setPost(false); // download file by get
        this.webClient.internalFileDownload(request, this.getSelf(), this.downloadInfo.getResponseClass(), this.logger, this.downloadInfo.getConversionJob().getUUID(), this.targetOutputStream);
    }

    public class DownloadPresignResult extends BaseWebResult {
        private String download_url;
        private String kind;

        public String getDownload_url() {
            return download_url;
        }

        public void setDownload_url(String download_url) {
            this.download_url = download_url;
        }

        public String getKind() {
            return kind;
        }

        public void setKind(String kind) {
            this.kind = kind;
        }
    }
}
