package com.egeio.services.transformer.fileio.model;

import akka.actor.ActorRef;
import com.egeio.core.jobsystem.actors.message.DownloadFailed;
import com.egeio.core.web.entity.BucketInfo;
import com.egeio.core.webclient.AkkaWebClient;
import com.egeio.core.webclient.message.DownloadCompleted;
import com.egeio.services.transformer.models.ConversionJob;

import java.io.OutputStream;

public class DownloadInfo {
    private BucketInfo bucketInfo;
    private ConversionJob conversionJob;
    private String uniqueName;
    private String targetPath;
    private OutputStream targetOutputStream;
    private ActorRef sender;
    private Class<? extends DownloadCompleted> responseClass;
    private Class<? extends DownloadFailed> failClass;
    private boolean isNeedDecrypt;
    private AkkaWebClient webClient;
    private String convertKind;

    public DownloadInfo(BucketInfo bucketInfo, ConversionJob job,String uniqueName, String targetPath, ActorRef sender, AkkaWebClient webClient) {
        this(bucketInfo, job, uniqueName, targetPath, sender, DownloadCompleted.class, DownloadFailed.class, webClient);
        this.isNeedDecrypt = true;
    }

    public DownloadInfo(BucketInfo bucketInfo, ConversionJob job,String uniqueName, String targetPath, ActorRef sender,Class<? extends DownloadCompleted> responseClass, Class<? extends DownloadFailed> failClass, AkkaWebClient webClient) {
        this.bucketInfo = bucketInfo;
        this.conversionJob = job;
        this.uniqueName = uniqueName;
        this.targetPath = targetPath;
        this.sender = sender;
        this.responseClass = responseClass;
        this.failClass = failClass;
        this.webClient = webClient;
        this.isNeedDecrypt = job.isNeedEncrypt();
    }


    public String getTargetPath() {
        return targetPath;
    }

    public void setTargetPath(String targetPath) {
        this.targetPath = targetPath;
    }


    public Class<? extends DownloadCompleted> getResponseClass() {
        return responseClass;
    }

    public void setResponseClass(Class<? extends DownloadCompleted> responseClass) {
        this.responseClass = responseClass;
    }

    public Class<? extends DownloadFailed> getFailClass() {
        return failClass;
    }

    public void setFailClass(Class<? extends DownloadFailed> failClass) {
        this.failClass = failClass;
    }

    public AkkaWebClient getWebClient() {
        return webClient;
    }

    public void setWebClient(AkkaWebClient webClient) {
        this.webClient = webClient;
    }

    public OutputStream getTargetOutputStream() {
        return targetOutputStream;
    }

    public void setTargetOutputStream(OutputStream targetOutputStream) {
        this.targetOutputStream = targetOutputStream;
    }

    public ActorRef getSender() {
        return sender;
    }

    public void setSender(ActorRef sender) {
        this.sender = sender;
    }

    public boolean isNeedDecrypt() {
        return isNeedDecrypt;
    }

    public void setNeedDecrypt(boolean needDecrypt) {
        isNeedDecrypt = needDecrypt;
    }

    public BucketInfo getBucketInfo() {
        return bucketInfo;
    }

    public void setBucketInfo(BucketInfo bucketInfo) {
        this.bucketInfo = bucketInfo;
    }

    public String getUniqueName() {
        return uniqueName;
    }

    public void setUniqueName(String uniqueName) {
        this.uniqueName = uniqueName;
    }

    public ConversionJob getConversionJob() {
        return conversionJob;
    }

    public void setConversionJob(ConversionJob conversionJob) {
        this.conversionJob = conversionJob;
    }

    public String getConvertKind() {
        return convertKind;
    }

    public void setConvertKind(String convertKind) {
        this.convertKind = convertKind;
    }
}
