package com.egeio.services.transformer.fileio.middle_preview;

import akka.actor.ActorRef;
import com.egeio.core.auth.domain.GuardUrl;
import com.egeio.core.auth.domain.WebRequest;
import com.egeio.core.config.Config;
import com.egeio.core.jobsystem.actors.ABaseUntypedActor;
import com.egeio.core.jobsystem.actors.message.*;
import com.egeio.core.web.entity.BaseWebResult;
import com.egeio.core.webclient.AkkaWebClient;
import com.egeio.core.webclient.message.SingleUploadCompleted;
import com.egeio.services.transformer.fileio.middle_preview.model.BizCallbackConfig;
import com.egeio.services.transformer.fileio.middle_preview.model.LoadFileQueryDto;
import com.egeio.services.transformer.fileio.model.UploadInfo;
import com.google.gson.annotations.SerializedName;
import com.ning.http.client.FluentCaseInsensitiveStringsMap;
import com.ning.http.client.FluentStringsMap;
import com.ning.http.client.cookie.Cookie;

import java.io.File;
import java.util.HashMap;
import java.util.List;

public class UploadFileActor extends ABaseUntypedActor {

    private AkkaWebClient webClient;
    private HashMap<File, LoadFileQueryDto> queryDtos;
    private BizCallbackConfig bizCallbackConfig;
    private UploadInfo uploadInfo;
    private List<File> localFiles;
    private ActorRef sender;
    private String kind;
    private boolean needEncrypt;
    private static String upload_sub_url = Config.getConfig().getElement("/configuration/web_client/upload_sub_url").getTextTrim();


    private Class<? extends UploadFailed> failMessage;
    private Class<? extends UploadCompleted> completeMessage;

    private int filesUploaded = 0;
    private final int filesTotal;

    public UploadFileActor(HashMap<File, LoadFileQueryDto> queryDtos, UploadInfo uploadInfo, BizCallbackConfig bizCallbackConfig) {
        this.queryDtos = queryDtos;
        this.webClient = uploadInfo.getWebClient();
        this.uploadInfo = uploadInfo;
        this.localFiles = uploadInfo.getLocalFiles();
        this.kind = uploadInfo.getKind();

        this.sender = uploadInfo.getSender();
        this.failMessage = uploadInfo.getFailMessage();
        this.completeMessage = uploadInfo.getCompleteMessage();
        this.bizCallbackConfig = bizCallbackConfig;
        this.uuid = uploadInfo.getConversionJob().getUUID();
        this.filesTotal = localFiles.size();
    }

    protected void realPreStart() throws Exception {
        this.logger.info(this.uuid, "STARTUP: upload [worker: {}]", new Object[]{this.getPath()});
        this.getSelf().tell(new UploadStarted(), (ActorRef) null);
    }

    protected void realOnReceive(Object message) throws Exception {
        UploadFailed resultMsg;
        if (message instanceof RetryFailedMessage) {
            RetryFailedMessage realMessage = (RetryFailedMessage) RetryFailedMessage.class.cast(message);
            resultMsg = (UploadFailed) this.failMessage.getConstructor(Throwable.class, String.class).newInstance(realMessage.getCause(), this.kind);
            this.sender.tell(resultMsg, this.getSelf());
        } else if (message instanceof UploadStarted) {
            this.upload();
        } else if (message instanceof SingleUploadCompleted) {
            if (++this.filesUploaded >= this.filesTotal) {
                this.logger.info(this.uuid, "finished uploading {}", new Object[]{this.kind});
                UploadCompleted msg = (UploadCompleted) this.completeMessage.getConstructor(String.class, Integer.class).newInstance(this.kind, this.filesTotal);
                this.sender.tell(msg, this.getSelf());
                this.getContext().stop(this.getSelf());
            } else {
                this.logger.info(this.uuid, "finished uploading {} files", new Object[]{this.filesUploaded});
            }
        } else if (message instanceof SingleUploadFailed) {
            SingleUploadFailed realMessage = (SingleUploadFailed) SingleUploadFailed.class.cast(message);
            resultMsg = (UploadFailed) this.failMessage.getConstructor(Throwable.class, String.class).newInstance(realMessage.getCause(), this.kind);
            this.sender.tell(resultMsg, this.getSelf());
            this.getContext().stop(this.getSelf());
            this.logger.error(this.uuid, "failed uploading file");
        } else {
            this.unhandled(message);
        }

    }

    protected void upload() throws Exception {
        for (File file : queryDtos.keySet()
        ) {
            LoadFileQueryDto queryDto = queryDtos.get(file);
            // header和body在getUploadQueryDto中配置
            WebRequest webRequest = new WebRequest(new GuardUrl(bizCallbackConfig.getUploadUrl(), bizCallbackConfig.getServiceId()),
                    (FluentStringsMap) null, (FluentCaseInsensitiveStringsMap) queryDto.getNettyHeaders(), queryDto.getPostParams(), (Cookie[]) null);

            UploadFileActor.UploadPresignResult uploadPresignResp = webClient.execute(webRequest, UploadFileActor.UploadPresignResult.class);
            if (uploadPresignResp.getError() != null) {
                this.getSelf().tell(new SingleUploadFailed(new Exception(uploadPresignResp.getError().toString())), (ActorRef) null);
                return;
            }
            logger.info(uuid, "start to upload file to " + uploadPresignResp.getUploadUrl());
            GuardUrl uploadGuardUrl = new GuardUrl(uploadPresignResp.getUploadUrl(), bizCallbackConfig.getServiceId());
            // fangcloud的upload和internal_upload都是单文件上传, 处理逻辑相似，
            // 差异只是在文件信息是否与文件内容一起传递
            // 上传预签名, 2次请求(1. 预签名把文件和bucket信息存在缓存中, 2. 上传时从cache中取数据,并将body的multipart文件流写入后方存储)
            // internal_upload, 1次请求(在header中写入bucket等文件信息, 将body的multipart文件流写入后方存储)
            //
            // 对于其他对接服务, 只要支持body multipart单文件上传(netty httpProvider模拟)就能复用. 问题: 1. 文件过大, body和对端上传服务器的数据包策略配置(nginx)
            this.webClient.internalFileUpload(uploadGuardUrl, queryDto.getNettyHeaders(), file, uploadPresignResp.getFileName(), this.getSelf(), SingleUploadCompleted.class, this.logger, this.uploadInfo.getConversionJob().getUUID());
        }

    }

    public class UploadPresignResult extends BaseWebResult {
        @SerializedName("upload_url")
        private String uploadUrl;
        private String kind;
        @SerializedName("upload_file_name")
        private String fileName;

        public String getKind() {
            return kind;
        }

        public void setKind(String kind) {
            this.kind = kind;
        }

        public String getUploadUrl() {
            return uploadUrl;
        }

        public void setUploadUrl(String uploadUrl) {
            this.uploadUrl = uploadUrl;
        }

        public String getFileName() {
            return fileName;
        }

        public void setFileName(String fileName) {
            this.fileName = fileName;
        }
    }
}
