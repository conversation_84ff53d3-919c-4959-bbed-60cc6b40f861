package com.egeio.services.transformer.fileio.middle_preview.model;

import com.google.gson.annotations.SerializedName;
import com.ning.http.client.FluentCaseInsensitiveStringsMap;
import com.ning.http.client.FluentStringsMap;
import com.ning.http.client.cookie.Cookie;
import com.ning.http.client.multipart.Part;

import java.io.File;
import java.io.InputStream;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;

public class LoadFileQueryDto {
    public static final String AdditionalInfo_Key_LOADFILEQUERYDTO = "preview_query_dto";

    @SerializedName("query_params")
    private FluentStringsMap queryParams;
    @SerializedName("post_params")
    private FluentStringsMap postParams;
    @SerializedName("headers")
    private HashMap<String, List<String>> headers;
    @SerializedName("body")
    private String body;
    @SerializedName("stream_body")
    private InputStream streamBody;
    @SerializedName("parts")
    private List<Part> parts;
    @SerializedName("cookies")
    private Cookie[] cookies;
    @SerializedName("is_post")
    private boolean isPost;
    @SerializedName("file_body")
    private File fileBody;

    public FluentStringsMap getQueryParams() {
        return queryParams;
    }

    public void setQueryParams(FluentStringsMap queryParams) {
        this.queryParams = queryParams;
    }

    public FluentStringsMap getPostParams() {
        return postParams;
    }

    public void setPostParams(FluentStringsMap postParams) {
        this.postParams = postParams;
    }

    public HashMap<String, List<String>> getHeaders() {
        return headers;
    }

    public void setHeaders(HashMap<String, List<String>> headers) {
        this.headers = headers;
    }

    public String getBody() {
        return body;
    }

    public void setBody(String body) {
        this.body = body;
    }

    public InputStream getStreamBody() {
        return streamBody;
    }

    public void setStreamBody(InputStream streamBody) {
        this.streamBody = streamBody;
    }

    public List<Part> getParts() {
        return parts;
    }

    public void setParts(List<Part> parts) {
        this.parts = parts;
    }

    public Cookie[] getCookies() {
        return cookies;
    }

    public void setCookies(Cookie[] cookies) {
        this.cookies = cookies;
    }

    public boolean isPost() {
        return isPost;
    }

    public void setPost(boolean post) {
        isPost = post;
    }

    public File getFileBody() {
        return fileBody;
    }

    public void setFileBody(File fileBody) {
        this.fileBody = fileBody;
    }

    public FluentCaseInsensitiveStringsMap getNettyHeaders() {
        FluentCaseInsensitiveStringsMap nettyHeader = new FluentCaseInsensitiveStringsMap();
        for (String key : headers.keySet()) {
            nettyHeader.add(key, headers.get(key));
        }
        return nettyHeader;
    }

    public synchronized void addHeader(String key, String value){
        if(!headers.containsKey(key)){
            headers.put(key,new ArrayList<>());

        }
        headers.get(key).add(value);
    }
}
