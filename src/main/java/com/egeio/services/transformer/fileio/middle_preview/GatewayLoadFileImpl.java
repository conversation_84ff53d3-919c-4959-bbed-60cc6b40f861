package com.egeio.services.transformer.fileio.middle_preview;

import akka.actor.Props;
import akka.actor.UntypedActorContext;
import com.egeio.core.web.WebClient;
import com.egeio.core.web.entity.UploadFileInfo;
import com.egeio.core.web.entity.UploadType;
import com.egeio.services.transformer.fileio.AbstractLoadFileImpl;
import com.egeio.services.transformer.fileio.middle_preview.model.BizCallbackConfig;
import com.egeio.services.transformer.fileio.middle_preview.model.LoadFileQueryDto;
import com.egeio.services.transformer.models.ConversionJob;
import com.google.gson.Gson;
import com.google.gson.JsonSyntaxException;
import com.ning.http.client.FluentStringsMap;

import java.io.File;
import java.util.HashMap;
import java.util.Map;

import static com.egeio.services.transformer.fileio.middle_preview.model.LoadFileQueryDto.AdditionalInfo_Key_LOADFILEQUERYDTO;


/**
 *  在mq转换任务ConversionJob中的additonal_info字段中做扩展, CONFIGFIELD
 *
 * */
public class GatewayLoadFileImpl extends AbstractLoadFileImpl {

    private static String UPLOAD_HEADER_KEY_UPLOAD_INFO = "X-Upload-Info";
    private static String UPLOAD_HEADER_KEY_FILEINFO_CONVERT_KIND_TASK_ID = "Append-Upload-Info-Convert-Kind-Task-Id";
    private static String UPLOAD_HEADER_KEY_FILEINFO_CONVERT_KIND = "Append-Upload-Info-Convert-Kind";
    private static String UPLOAD_HEADER_KEY_BUCKET_TYPE = "Append-Upload-Info-Bucket-Type";

    private static Gson GSON = new Gson();

    public GatewayLoadFileImpl(UntypedActorContext context, WebClient webClient, ConversionJob job) throws Exception {
        super(context, webClient, job);
    }

    @Override
    public void downloadFile() throws Exception {
        // transformer -> 对接模块: 获取下载请求信息, 对接模块根据file_id和kind(过程文件)去获取下载链接
        // transformer -> 下载服务器 根据匿名的url下载文件

        LoadFileQueryDto downloadQueryDto = getDownloadQueryDto(job);
        this.actorContext.actorOf(Props.create(DownloadFileActor.class, webClient, downloadQueryDto, getGatewayConfig(),
                downloadInfo));
    }

    @Override
    public void uploadFile() throws Exception {
        // transformer -> 对接模块 : 上传文件流到对接模块
        // 对接模块 -> 上传服务: 对接模块根据请求参数, 查找业务集成的上传模块，将文件流上传到上传服务器
        //  类似wps编辑的上传接口, 传参: task_uuid, bucket信息(可选项), 文件流(相较于直接向上传服务上传文件, 多1次网络IO和写缓存区的时间,
        // 内网环境带宽高, 可接受), 在preview预览能力服务中做文件信息整理和上传到上传服务
        // 问题: 1. 文件名规则在哪里制定? 目前在AbstractPreConvert.prepareTargetFile()中产生, 对应的外部系统需要维护这样的转换结果映射表

        HashMap<File, LoadFileQueryDto> uploadQueryDtos = getUploadQueryDto();
        BizCallbackConfig bizCallbackConfig = getGatewayConfig();
        this.actorContext.actorOf(Props.create(UploadFileActor.class, uploadQueryDtos,
                this.uploadInfo, bizCallbackConfig));
    }

    private LoadFileQueryDto prepareQueryDTO() throws Exception {
        Map<String, Object> additionalInfo = job.getAdditionalInfos();
        Object queryDto = additionalInfo.get(AdditionalInfo_Key_LOADFILEQUERYDTO);

        String queryDtoStr = GSON.toJson(queryDto);
        try {
            // 序列化反序列化, 创建新对象, 保证线程安全
            LoadFileQueryDto newQueryDto = GSON.fromJson(queryDtoStr, LoadFileQueryDto.class);
            return newQueryDto;
        } catch (JsonSyntaxException e) {
            logger.error(job.getUUID(), "Field " + AdditionalInfo_Key_LOADFILEQUERYDTO + " in job additionalInfo is not the instance of LoadFileQueryDto");
            throw e;
        }
    }

    protected BizCallbackConfig getGatewayConfig() {
        Map<String, Object> additionalInfo = job.getAdditionalInfos();
        Object gatewayConfig = additionalInfo.get(BizCallbackConfig.AdditionalInfo_Key_PREVIEW_BIZ_CALLBACK_CONFIG);

        String gatewayConfigStr = GSON.toJson(gatewayConfig);
        try {
            // 序列化反序列化, 创建新对象, 保证线程安全
            BizCallbackConfig newBizCallbackConfig = GSON.fromJson(gatewayConfigStr, BizCallbackConfig.class);
            return newBizCallbackConfig;
        } catch (JsonSyntaxException e) {
            logger.error(job.getUUID(), "Field " + AdditionalInfo_Key_LOADFILEQUERYDTO + " in job additionalInfo is not the instance of LoadFileQueryDto");
            throw e;
        }
    }

    protected LoadFileQueryDto getDownloadQueryDto(ConversionJob job) throws Exception {
        // 从转换job的additional_info中
        LoadFileQueryDto fileQueryDto = prepareQueryDTO();

        // 将convert_kind, 目标文件类型附加到原来的body map中
        // 指定下载的文件类型
        Map<String, String> bodyMap = new HashMap<>();
        if (fileQueryDto.getBody() != null) {
            Map<String, String> originMap = GSON.fromJson(fileQueryDto.getBody(), Map.class);
            for (String key : originMap.keySet()) {
                bodyMap.put(key, originMap.get(key));
            }
        }
        bodyMap.put("expected_kind", downloadInfo.getConvertKind());
        fileQueryDto.setBody(GSON.toJson(bodyMap));
        return fileQueryDto;
    }

    protected HashMap<File, LoadFileQueryDto> getUploadQueryDto() throws Exception {

        LoadFileQueryDto fileQueryDto = prepareQueryDTO();
        String queryStr = GSON.toJson(fileQueryDto);

        // 构建UploadFileInfo

        HashMap<File, LoadFileQueryDto> fileQueryDtos = new HashMap<>();
        for (File file : uploadInfo.getLocalFiles()) {

            UploadFileInfo uploadFileInfo = new UploadFileInfo();
            uploadFileInfo.setBucketInfo(null);
            uploadFileInfo.setCallbackUrl((String) null);
            uploadFileInfo.setCallbackServiceId(-1L);
            uploadFileInfo.setDate(this.uploadInfo.getConversionJob().getDate());
            uploadFileInfo.setHour(this.uploadInfo.getConversionJob().getHour());
            uploadFileInfo.setNeedEncrypt(this.uploadInfo.getConversionJob().isNeedEncrypt());
            uploadFileInfo.setUploadType(UploadType.INTERNAL);
            uploadFileInfo.setUniqueName(file.getName());
            String uploadInfoJson = GSON.toJson(uploadFileInfo);

            //创建新对象, 避免数据覆盖
            LoadFileQueryDto copyedFileQueryDto = GSON.fromJson(queryStr, LoadFileQueryDto.class);
            if (copyedFileQueryDto.getHeaders() == null) {
                copyedFileQueryDto.setHeaders(new HashMap<>());
            }
            copyedFileQueryDto.addHeader(UPLOAD_HEADER_KEY_UPLOAD_INFO, uploadInfoJson);
            copyedFileQueryDto.addHeader(UPLOAD_HEADER_KEY_FILEINFO_CONVERT_KIND_TASK_ID,
                    this.uploadInfo.getConversionJob().getConvertMetas().containsKey(this.uploadInfo.getKind()) ?
                            this.uploadInfo.getConversionJob().getConvertMetas().get(this.uploadInfo.getKind()).getTaskId() : "");
            copyedFileQueryDto.addHeader(UPLOAD_HEADER_KEY_FILEINFO_CONVERT_KIND, this.uploadInfo.getKind());
            copyedFileQueryDto.addHeader(UPLOAD_HEADER_KEY_BUCKET_TYPE, this.uploadInfo.getBucketType());

            fileQueryDtos.put(file, copyedFileQueryDto);
        }

        return fileQueryDtos;
    }

}
