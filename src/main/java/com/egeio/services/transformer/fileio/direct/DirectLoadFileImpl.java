package com.egeio.services.transformer.fileio.direct;

import akka.actor.Props;
import akka.actor.UntypedActorContext;
import com.egeio.core.jobsystem.actors.DownloadActor;
import com.egeio.core.jobsystem.actors.UploadActor;
import com.egeio.core.web.WebClient;
import com.egeio.services.transformer.fileio.AbstractLoadFileImpl;
import com.egeio.services.transformer.models.ConversionJob;

public class DirectLoadFileImpl extends AbstractLoadFileImpl {


    public DirectLoadFileImpl(UntypedActorContext context, WebClient webClient, ConversionJob job) {
        super(context, webClient, job);
    }

    @Override
    public void downloadFile() throws Exception {
        this.actorContext.actorOf(Props.create(DownloadActor.class,
                downloadInfo.getBucketInfo(), downloadInfo.getConversionJob().getDate(), downloadInfo.getConversionJob().getHour(),
                downloadInfo.getUniqueName(), downloadInfo.getConversionJob().getFileStorageId(),
                downloadInfo.getConversionJob().getUserId(), downloadInfo.getTargetPath(),
                downloadInfo.getSender(), downloadInfo.getConversionJob().getUUID(), downloadInfo.getResponseClass(),
                downloadInfo.getFailClass(), downloadInfo.isNeedDecrypt(), downloadInfo.getWebClient()));
    }


    @Override
    public void uploadFile() throws Exception {

        this.actorContext.actorOf(Props.create(UploadActor.class,
                uploadInfo.getBucketInfo(), uploadInfo.getConversionJob().getDate(), uploadInfo.getConversionJob().getHour(),
                uploadInfo.getLocalFiles(), uploadInfo.getSender(), uploadInfo.getKind(),
                uploadInfo.getWebClient(), uploadInfo.getConversionJob().isNeedEncrypt(),
                uploadInfo.getCompleteMessage(), uploadInfo.getFailMessage(),
                uploadInfo.getConversionJob().getUUID()));
    }
}
