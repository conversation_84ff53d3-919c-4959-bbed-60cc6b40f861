package com.egeio.services.transformer.fileio.middle_preview.model;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.google.gson.annotations.SerializedName;

import java.io.Serializable;
import java.util.Map;

public class BizCallbackConfig implements Serializable {

    public final static String AdditionalInfo_Key_PREVIEW_BIZ_CALLBACK_CONFIG = "preview_biz_callback_config";

    @SerializedName("service_id")
    private Long serviceId;

    @SerializedName("identify")
    private String identify;
    @SerializedName("host")
    private String host;

    @SerializedName("file")
    private String fileInfoUrl;
    @SerializedName("upload")
    private String uploadUrl;
    @SerializedName("download")
    private String downloadUrl;
    @SerializedName("complete_callback")
    private String completeCallbackUrl;
    @SerializedName("check_preview_url")
    private String checkPreviewUrl;
    @SerializedName("check_job_url")
    private String checkJobUrl;
    @SerializedName("update_status_url")
    private String updateStatusUrl;

    @SerializedName("additional_info")
    private Map<String, String> additionalInfo;

    public String getHost() {
        return host;
    }

    public void setHost(String host) {
        this.host = host;
    }

    public String getDownloadUrl() {
        return host + downloadUrl;
    }

    public void setDownloadUrl(String downloadUrl) {
        this.downloadUrl = downloadUrl;
    }

    public String getUploadUrl() {
        return host + uploadUrl;
    }

    public void setUploadUrl(String uploadUrl) {
        this.uploadUrl = uploadUrl;
    }

    public Long getServiceId() {
        return serviceId;
    }

    public void setServiceId(Long serviceId) {
        this.serviceId = serviceId;
    }

    public String getCompleteCallbackUrl() {
        return host + completeCallbackUrl;
    }

    public void setCompleteCallbackUrl(String completeCallbackUrl) {
        this.completeCallbackUrl = completeCallbackUrl;
    }

    public String getCheckPreviewUrl() {
        return host + checkPreviewUrl;
    }

    public void setCheckPreviewUrl(String checkPreviewUrl) {
        this.checkPreviewUrl = checkPreviewUrl;
    }

    public String getUpdateStatusUrl() {
        return host + updateStatusUrl;
    }

    public void setUpdateStatusUrl(String updateStatusUrl) {
        this.updateStatusUrl = updateStatusUrl;
    }

    public Map<String, String> getAdditionalInfo() {
        return additionalInfo;
    }

    public void setAdditionalInfo(Map<String, String> additionalInfo) {
        this.additionalInfo = additionalInfo;
    }

    public String getIdentify() {
        return identify;
    }

    public void setIdentify(String identify) {
        this.identify = identify;
    }

    public String getFileInfoUrl() {
        return host + fileInfoUrl;
    }

    public void setFileInfoUrl(String fileInfoUrl) {
        this.fileInfoUrl = fileInfoUrl;
    }
}
