package com.egeio.services.transformer.fileio.middle_preview.model;

import com.egeio.core.config.Config;
import com.google.gson.annotations.SerializedName;

public class PreviewCallbackConfig {
    public transient final static String AdditionalInfo_Key_PREVIEW_CALLBACK_CONFIG = "preview_callback_config";

    @SerializedName("preview_host")
    private String host;

    @SerializedName("preview_external_host")
    private String externalHost;

    @SerializedName("preview_update_status_url")
    private String updateStatusUrl;


    public String getUpdateStatusUrl() {
        boolean isInContainer = Config.getBoolean("/configuration/preview_url", "isInContainer", false);
        if (isInContainer)
            return host + updateStatusUrl;
        return externalHost + updateStatusUrl;
    }

}
