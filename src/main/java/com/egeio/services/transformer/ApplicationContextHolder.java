package com.egeio.services.transformer;

import org.apache.commons.lang3.Validate;
import org.springframework.beans.BeansException;
import org.springframework.beans.factory.DisposableBean;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;

/**
 * 为了方便从 applicationContext 获取 bean
 *
 * <AUTHOR>
 * @since
 */
public class ApplicationContextHolder implements ApplicationContextAware, DisposableBean {

    private static ApplicationContext applicationContext = null;

    public static <T> T getBean(Class<T> requiredType) {
        assertContextInjected();

        return applicationContext.getBean(requiredType);
    }

    public static <T> T getBean(String beanName) {
        assertContextInjected();

        return (T) applicationContext.getBean(beanName);
    }

    @Override
    public void destroy() throws Exception {
        this.applicationContext = null;
    }

    @Override
    public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {
        this.applicationContext = applicationContext;
    }

    private static void assertContextInjected() {
        Validate.validState(applicationContext != null, "applicationContext is not inject!!! please config");
    }

}
