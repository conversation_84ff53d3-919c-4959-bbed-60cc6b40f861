package com.egeio.services.transformer.service;

import com.egeio.core.cmd.CmdExecute;
import com.egeio.core.config.Config;
import com.egeio.core.log.Logger;
import com.egeio.core.log.LoggerFactory;
import com.egeio.core.log.MyUUID;
import com.egeio.core.utils.Utils;
import com.egeio.services.transformer.constant.Errors;
import com.egeio.services.transformer.exception.TransformerException;
import com.egeio.services.transformer.models.OfficeType;
import com.egeio.services.transformer.models.WatermarkInfo;
import com.egeio.services.transformer.models.watermark.*;
import com.egeio.services.transformer.preconvert.PreCode2Html;
import com.egeio.services.transformer.preconvert.PreHtml2Pdf;
import com.egeio.services.transformer.utils.ImageUtils;
import com.egeio.services.transformer.utils.PdfWatermarkUtil;
import org.apache.commons.exec.CommandLine;
import org.apache.commons.io.FileUtils;
import org.apache.commons.io.IOUtils;
import org.apache.commons.io.filefilter.NameFileFilter;
import org.im4java.core.IM4JavaException;
import org.springframework.stereotype.Service;

import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import java.io.*;
import java.util.UUID;

/**
 * <AUTHOR>
 * @date 2022/3/18 下午3:52
 */
@Service
public class FileConvertService {

    protected Logger logger = LoggerFactory.getLogger(this.getClass());

    private static String TMP_CONVERSION_DIR = Config.getConfig()
            .getElement("/configuration/conversion/tmp-conversion-dir")
            .getText().trim();
    private File workingDir = new File(new File(TMP_CONVERSION_DIR),
            UUID.randomUUID().toString());

    public static String EMPTY_PDF = Config
            .getString("/configuration/empty_pdf", "empty.pdf");

    private static long timeout = 600000l;

    private static String PDF_TYPE = "pdf";


    public void FileConvertStream(MyUUID myUUID,
                                  InputStream inputStream,
                                  HttpServletResponse response,
                                  WatermarkParam watermarkParam

    ) {

        String extension = watermarkParam.getExtension();

        //1 新建一个工作目录
        workingDir = new File(new File(TMP_CONVERSION_DIR), myUUID.getUuid().toString());
        Utils.createFolder(workingDir);

        //1 读取文件写到本地
        String fileNamePre = UUID.randomUUID().toString();
        File sourceFile = new File(workingDir, fileNamePre + "." + extension);
        logger.info(myUUID, "转换源文件sourceFile path:{}", sourceFile.getAbsolutePath());
        try {
            FileUtils.copyInputStreamToFile(inputStream, sourceFile);
        } catch (IOException e) {
            logger.error(myUUID, "文件读取异常", e);
            return;
        }

        //判断该文件是不是支持aspose预览
        if (OfficeType.isOfficeType(extension)) {
            //执行aspose预览：
            // 非水印 pdf（现在都是加密的pdf文件 如果转换成pdf 需要修改类型kind）
            // 非水印加密 pdf_enc 先执行LinuxOfficeWorker来转换 然后执行PdfEncryptWorker进行加密操作
            // 加水印 pdf_watermark_enc：先执行LinuxOfficeWorker来转换  在执行PdfWatermarkWorker来进行水印和加密的操作

            //去掉加密的操作
            try {
                //文件的原始类型就是extension
                doAsposeCmdJob(myUUID, response, fileNamePre, sourceFile, extension, watermarkParam);
                clean(myUUID);
            } catch (Exception e) {
                logger.error(myUUID, "aspose转换失败.", e);
                throw new TransformerException(Errors.FILE_CONVERT_ERROR);

            }


        } else if (ImageType.isImageType(extension)) {
            // 图片类型
            File rotateSourceFile = new File(workingDir, fileNamePre + "_rotate.png");
            File watermarkFile = new File(workingDir, fileNamePre + "_watermark.png");
            // 最终生成的完整水印图片
            File targetFile = new File(workingDir, fileNamePre + "_target." + "jpg");
            try {
                doImageJob(myUUID, response, sourceFile.getAbsolutePath(), rotateSourceFile.getAbsolutePath(), watermarkFile.getAbsolutePath(), targetFile.getAbsolutePath(), watermarkParam);
                clean(myUUID);
            } catch (IOException e) {
                logger.error(myUUID, "图片转换IO异常", e);
                throw new TransformerException(Errors.SYSTEM_ERROR);
            } catch (IM4JavaException e) {
                logger.error(myUUID, "图片转换IM4J异常", e);
                throw new TransformerException(Errors.SYSTEM_ERROR);
            } catch (InterruptedException e) {
                logger.error(myUUID, "图片转换InterruptedException", e);
                throw new TransformerException(Errors.SYSTEM_ERROR);
            }

        } else if (TextType.isTextType(extension)) {
            // 文本类型
            try {
                doTextJob(myUUID, response, fileNamePre, sourceFile, watermarkParam);
                clean(myUUID);
            } catch (Exception e) {
                logger.error(myUUID, "文本转换失败", e);
                throw new TransformerException(Errors.FILE_CONVERT_ERROR);
            }

        } else if (HtmlType.isHtmlType(extension)) {
            // html类型
            doHtmlJob(myUUID, response, fileNamePre, sourceFile, watermarkParam);
            clean(myUUID);
        } else if (MarkdownType.isMarkdownType(extension)) {
            // md类型
            doMarkdownJob(myUUID, response, fileNamePre, sourceFile, watermarkParam);

        } else if (extension.equals(PDF_TYPE)) {
            doPdfJob(myUUID, response, fileNamePre, sourceFile, watermarkParam);
            clean(myUUID);
        } else {
            logger.error(myUUID, "当前文件类型不支持extension:{}", extension);
            throw new TransformerException(Errors.FILE_TYPE_NOT_SUPPORT);
        }


    }

    /**
     * @param myUUID
     * @param response
     * @param finalFile:最终生成水印的文件
     */
    private void writeResponseOutputStream(MyUUID myUUID, HttpServletResponse response, File finalFile) {
        ServletOutputStream outputStream = null;
        try {
            outputStream = response.getOutputStream();
            FileUtils.copyFile(finalFile, outputStream);

        } catch (IOException e) {
            logger.error(myUUID, "文件写入异常", e);
            throw new TransformerException(Errors.SYSTEM_ERROR);
        } finally {
            try {
                outputStream.flush();
                outputStream.close();
            } catch (IOException e) {
                logger.error(myUUID, "关闭文件异常", e);
                throw new TransformerException(Errors.SYSTEM_ERROR);
            }
        }


    }


    /**
     * 执行script脚本
     * aspose：LinuxOfficeWorker， 加水印pdf_watermark_enc：PdfWatermarkWorker    pdf_enc：PdfEncryptWorker
     * 看一下 有没有转换类型是pdf的情况 一般都是pdf_enc
     * <p>
     * html：html2pdf：pdf：Html2PdfWorker    水印pdf_watermark_enc ：PdfWatermarkWorker    pdf_enc：PdfEncryptWorker     PreHtml2Pdf
     * markdown2html：MarkdownWorker   PreMarkdown2Html
     * pdf2image： PdfToImageWorker   PrePdf2Image
     * vi2image：  CMDWorker PreVimage2Image
     * <p>
     * 图片类型是单独的处理的：
     * ImageWorker ：com.egeio.services.transformer.utils.ImageUtils#doImageWork(java.lang.String, java.lang.String, int, int, int, com.egeio.services.transformer.models.PreviewType, boolean, boolean, boolean, com.egeio.services.transformer.models.ImageMeta, com.egeio.core.log.MyUUID)
     * ImageWatermarkWorker：com.egeio.services.transformer.utils.ImageUtils#addWatermark(java.lang.String, java.lang.String, java.lang.String, java.lang.String, java.lang.String, com.egeio.core.log.MyUUID)
     * bimface不支持加水印
     *
     * @param myUUID
     * @param response
     * @param fileNamePre:生成文件的地址前缀
     * @param sourceFile
     * @param extension
     * @param watermarkParam
     * @throws Exception
     */
    private void doAsposeCmdJob(MyUUID myUUID,
                                HttpServletResponse response,
                                String fileNamePre,
                                File sourceFile,
                                String extension,
                                WatermarkParam watermarkParam
    ) throws Exception {
        logger.info(myUUID, "开始执行doAsposeCmdJob");
        //做空文件转换
        if (sourceFile.length() == 0) {
            File emptyFile = doEmptyPdfConvert(myUUID, fileNamePre);
            writeResponseOutputStream(myUUID, response, emptyFile);
            return;
        }
        // aspose生成的文件
        File asposeFile = new File(workingDir, fileNamePre + "_aspose." + "pdf");
        String workingDirPath = workingDir.getAbsolutePath();

        //1 构造一个script脚本转换的参数
        //aspose参数构造
//        PreLinuxOffice2Pdf preConvert = new PreLinuxOffice2Pdf();
        logger.info(myUUID, "sourceFilePath:{} asposeFile:{}", sourceFile.getAbsoluteFile(), asposeFile.getAbsolutePath());

        AsposeWatermarkParam asposeWatermarkParam = new AsposeWatermarkParam();
        asposeWatermarkParam.setSourceFile(sourceFile);
        asposeWatermarkParam.setTargetFile(asposeFile);
        asposeWatermarkParam.setSourceType(extension);

        CommandLine cmd = asposeWatermarkParam.getCMD();
        OutputStream out = null;
        //2 开始执行aspose转换
        new CmdExecute().execute(cmd, workingDirPath, timeout, out);

        logger.info(myUUID, "aspose转换完成.");

        //3 转换完成检查是否转换成功
        if (!convertStatus(asposeFile)) {
            throw new Exception("convert failed!");
        }

        //4 转换成功之后需要去转换加水印
//        PdfWatermarkWorker
        /**
         * START: Pdf Watermark
         * STOP: Pdf Watermark
         */
        File watermarkFile = doPdf2PdfWatermark(myUUID, fileNamePre, asposeFile, watermarkParam);

        writeResponseOutputStream(myUUID, response, watermarkFile);

        logger.info(myUUID, "结束执行doAsposeCmdJob");
    }


    /**
     * 做图片转换
     * 第一类：png,jpg,jpeg,jpf,jp2,gif,bmp,aix,ico  image_1024_watermark   ImageWatermarkWorker    PreImageWatermark
     * 第二类：tif,tiff  单独的服务器转换 队列名：file_conversion_tif_queue  水印转换：ImageWatermarkWorker
     * 第三类：ps,eps
     *
     * @param myUUID
     * @param sourceFilePath
     * @param rotateSourceFilePath
     * @param watermarkFilePath
     * @param targetFilePath
     * @param watermarkParam
     */
    private void doImageJob(MyUUID myUUID,
                            HttpServletResponse response,
                            String sourceFilePath,
                            String rotateSourceFilePath,
                            String watermarkFilePath,
                            String targetFilePath,
                            WatermarkParam watermarkParam) throws InterruptedException, IOException, IM4JavaException {

        //ImageWatermarkWorker START: Image Watermark
        logger.info(myUUID, "开始执行图片转换doImageJob");
        logger.info(myUUID, "rotateSourceFilePath:{} watermarkFilePath:{} targetFilePath:{}", rotateSourceFilePath, watermarkFilePath, targetFilePath);
        WatermarkInfo watermarkInfo = new WatermarkInfo();
        watermarkInfo.setContentList(watermarkParam.getWatermarkContent());
        watermarkInfo.setFontSize(watermarkParam.getFontSize());
        watermarkInfo.setFontColor(watermarkParam.getFontColor());
        watermarkInfo.setOpacity(watermarkParam.getOpacity());
        watermarkInfo.setCustom(true);
        watermarkInfo.setWatermarkLineDistance(watermarkParam.getLineDistance());
        ImageUtils.addWatermark(
                sourceFilePath,
                rotateSourceFilePath,
                targetFilePath,
                watermarkInfo,
                myUUID
        );

        //ImageWatermarkWorker STOP: Image Watermark
        // 写入输出数据流
        File targetFile = new File(targetFilePath);

        writeResponseOutputStream(myUUID, response, targetFile);
        logger.info(myUUID, "执行图片转换结束doImageJob");

    }


    /**
     * @param myUUID
     * @param response
     * @param fileNamePre
     * @param sourceFile
     * @param watermarkParam
     * @throws Exception
     */
    private void doTextJob(MyUUID myUUID,
                           HttpServletResponse response,
                           String fileNamePre,
                           File sourceFile,
                           WatermarkParam watermarkParam) throws Exception {


        // 文本类型
        /**
         * 1 CMDWorker
         * start convert [kind: html]
         * finished convert [kind: html]
         *
         * 2 Html2PdfWorker  PreHtml2Pdf
         * start convert [kind: pdf]
         * finished convert [kind: pdf]
         *
         * 3 PdfWatermarkWorker
         * start convert [kind: pdf_watermark_enc]
         * START: Pdf Watermark
         * STOP: Pdf Watermark
         * finished convert [kind: pdf_watermark_enc]
         */
        logger.info(myUUID, "开始执行doTextJob");

        //做空文件转换
        if (sourceFile.length() == 0) {
            File emptyFile = doEmptyPdfConvert(myUUID, fileNamePre);
            writeResponseOutputStream(myUUID, response, emptyFile);
            return;
        }

        // 1 text转换成html
        File htmlFile = new File(workingDir, fileNamePre + ".html");
        PreCode2Html preCode2Html = new PreCode2Html();
        preCode2Html.setSourceFile(sourceFile);
        preCode2Html.setTargetFile(htmlFile);
        logger.info(myUUID, " text转换成html htmlFile path:{}", htmlFile.getAbsolutePath());
        new CmdExecute().execute(preCode2Html.getCMD(), workingDir.getAbsolutePath(), timeout, null);

        if (!convertStatus(htmlFile)) {
            logger.error(myUUID, "text转换成html异常 文件不存在:{}");
        }


        // 2 html转换成pdf
        File pdfFile = new File(workingDir, fileNamePre + ".pdf");
        PreHtml2Pdf preHtml2Pdf = new PreHtml2Pdf();
        preHtml2Pdf.setSourceFile(htmlFile);
        preHtml2Pdf.setTargetFile(pdfFile);
        logger.info(myUUID, " html转换成pdf pdfFile path:{}", pdfFile.getAbsolutePath());
        new CmdExecute().execute(preHtml2Pdf.getCMD(), workingDir.getAbsolutePath(), timeout, null);

        // 3 pdf转换水印pdf
        File watermarkFile = doPdf2PdfWatermark(myUUID, fileNamePre, pdfFile, watermarkParam);

        writeResponseOutputStream(myUUID, response, watermarkFile);
        logger.info(myUUID, "结束执行doTextJob");
    }


    private void doHtmlJob(MyUUID myUUID, HttpServletResponse response, String fileNamePre, File sourceFile, WatermarkParam watermarkParam) {

        // 1 html转换成pdf
        File pdfFile = new File(workingDir, fileNamePre + ".pdf");
        logger.info(myUUID, "开始执行doHtmlJob sourceFile path:{} pdfFile path:{}", sourceFile.getAbsolutePath(), pdfFile.getAbsolutePath());
        PreHtml2Pdf preHtml2Pdf = new PreHtml2Pdf();
        preHtml2Pdf.setSourceFile(sourceFile);
        preHtml2Pdf.setTargetFile(pdfFile);
        logger.info(myUUID, " html转换成pdf pdfFile path:{}", pdfFile.getAbsolutePath());

        try {
            new CmdExecute().execute(preHtml2Pdf.getCMD(), workingDir.getAbsolutePath(), timeout, null);
        } catch (Exception e) {
            if (pdfFile.exists() && pdfFile.isFile() && pdfFile.length() > 0) {
                logger.warn(myUUID, "html to pdf failed but file generated", e);
            } else {
                throw new TransformerException(Errors.FILE_CONVERT_ERROR);
            }
        }

        // 2 pdf转换水印pdf
        File watermarkFile = doPdf2PdfWatermark(myUUID, fileNamePre, pdfFile, watermarkParam);

        writeResponseOutputStream(myUUID, response, watermarkFile);
        logger.info(myUUID, "结束执行doHtmlJob");

    }


    private void doPdfJob(MyUUID myUUID, HttpServletResponse response, String fileNamePre, File sourceFile, WatermarkParam watermarkParam) {
        logger.info(myUUID, "开始执行doPdfJob");
        // pdf转换水印pdf
        File watermarkFile = doPdf2PdfWatermark(myUUID, fileNamePre, sourceFile, watermarkParam);

        writeResponseOutputStream(myUUID, response, watermarkFile);
        logger.info(myUUID, "结束执行doPdfJob");
    }


    /**
     * md->html->pdf->pdf_watermark
     *
     * @param myUUID
     * @param response
     * @param fileNamePre
     * @param sourceFile
     * @param watermarkParam
     */
    private void doMarkdownJob(MyUUID myUUID, HttpServletResponse response, String fileNamePre, File sourceFile, WatermarkParam watermarkParam) {
        // 1 md转换成html
        File tmpFile = new File(workingDir, fileNamePre + "_tmp." + watermarkParam.getExtension());
        logger.info(myUUID, "开始执行doHtmlJob sourceFile path:{} tmpFile path:{}", sourceFile.getAbsolutePath(), tmpFile.getAbsolutePath());
        Markdown2HtmlParam markdown2HtmlParam = new Markdown2HtmlParam(tmpFile);
        File htmlFile = new File(workingDir, fileNamePre + ".html");

        markdown2HtmlParam.setSourceFile(sourceFile);
        markdown2HtmlParam.setTargetFile(htmlFile);
        markdown2HtmlParam.setTmpFile(tmpFile);
        markdown2HtmlParam.setWorkingDir(workingDir);
        //转换文件编码为utf8
        CommandLine utfCmd = null;
        try {
            utfCmd = markdown2HtmlParam.get2UtfCMD();
            new CmdExecute().execute(utfCmd, markdown2HtmlParam.getWorkingDirPath(), timeout / 2);
        } catch (IOException e) {
            logger.error(myUUID, "Markdown转换获取utfCmd异常", e);
            throw new TransformerException(Errors.SYSTEM_ERROR);
        } catch (Exception e) {
            logger.error(myUUID, "Markdown转换异常", e);
            throw new TransformerException(Errors.SYSTEM_ERROR);
        }

        if (!tmpFile.exists()) {
            throw new TransformerException(Errors.FILE_CONVERT_ERROR);
        }

        //转换任务
        CommandLine cmd = null;
        try {
            cmd = markdown2HtmlParam.getCMD();
            new CmdExecute().execute(cmd, markdown2HtmlParam.getWorkingDirPath(), timeout / 2);
        } catch (IOException e) {
            logger.error(myUUID, "Markdown转换获取Cmd异常", e);
            throw new TransformerException(Errors.SYSTEM_ERROR);
        } catch (Exception e) {
            logger.error(myUUID, "Markdown转换异常", e);
            throw new TransformerException(Errors.SYSTEM_ERROR);
        }

        if (new File(markdown2HtmlParam.getWorkingDirPath()).list(markdown2HtmlParam.getFileFilter()).length == 0) {
            throw new TransformerException(Errors.FILE_CONVERT_ERROR);
        }

        tmpFile.delete();

        // 2 html转换成pdf
        File pdfFile = new File(workingDir, fileNamePre + ".pdf");
        logger.info(myUUID, "htmlFile path:{} pdfFile path:{}", htmlFile.getAbsolutePath(), pdfFile.getAbsolutePath());

        PreHtml2Pdf preHtml2Pdf = new PreHtml2Pdf();
        preHtml2Pdf.setSourceFile(htmlFile);
        preHtml2Pdf.setTargetFile(pdfFile);
        logger.info(myUUID, " html转换成pdf pdfFile path:{}", pdfFile.getAbsolutePath());

        try {
            new CmdExecute().execute(preHtml2Pdf.getCMD(), workingDir.getAbsolutePath(), timeout, null);
        } catch (Exception e) {
            if (pdfFile.exists() && pdfFile.isFile() && pdfFile.length() > 0) {
                logger.warn(myUUID, "html to pdf failed but file generated", e);
            } else {
                throw new TransformerException(Errors.FILE_CONVERT_ERROR);
            }
        }

        // 3 pdf转换水印pdf
        File watermarkFile = doPdf2PdfWatermark(myUUID, fileNamePre, pdfFile, watermarkParam);

        writeResponseOutputStream(myUUID, response, watermarkFile);
        logger.info(myUUID, "结束执行doHtmlJob");
    }

    /**
     * 判断转换是否成功
     *
     * @param targetFile
     * @return
     */
    private boolean convertStatus(File targetFile) {
        NameFileFilter nameFileFilter = new NameFileFilter(targetFile.getName());
        if (new File(workingDir.getAbsolutePath())
                .list(nameFileFilter).length == 0) {
            return false;
        }
        return true;
    }


    private File doEmptyPdfConvert(MyUUID myUUID, String fileNamePre) {
        File targetFile = new File(workingDir, fileNamePre + "_empty.pdf");
        try {
            try (InputStream input = getClass().getClassLoader()
                    .getResourceAsStream(EMPTY_PDF);
                 OutputStream output = new FileOutputStream(
                         targetFile)) {
                IOUtils.copy(input, output);
            }
        } catch (IOException e) {
            logger.error(myUUID, "pdf空文件转换失败", e);
        }
        return targetFile;
    }


    private File doPdf2PdfWatermark(MyUUID myUUID, String fileNamePre, File sourceFile, WatermarkParam watermarkParam) {
        File watermarkFile = new File(workingDir, fileNamePre + "_watermark." + "pdf");
        logger.info(myUUID, "pdf转换水印pdf watermarkFile path:{}", watermarkFile.getAbsolutePath());
        PdfWatermarkUtil pdfWatermarkUtil = new PdfWatermarkUtil();
        pdfWatermarkUtil.addWatermark(myUUID, sourceFile.getAbsolutePath(), watermarkFile.getAbsolutePath(), watermarkParam);
        return watermarkFile;

    }


    private void clean(MyUUID myUUID) {
        if (this.workingDir != null) {
            try {
                FileUtils.deleteDirectory(this.workingDir);
                logger.info(myUUID, "下载水印路径删除成功 [dest: {}] directory", this.workingDir.getAbsolutePath());
            }
            catch (IOException e) {
                logger.error(myUUID, e, "下载水印路径删除失败[{}]", workingDir.getAbsolutePath());
            }
        }
    }

}
