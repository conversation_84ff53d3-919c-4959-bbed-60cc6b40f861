package com.egeio.services.transformer.utils;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.FileUtils;

import javax.imageio.IIOImage;
import javax.imageio.ImageIO;
import javax.imageio.ImageWriteParam;
import javax.imageio.ImageWriter;
import javax.imageio.stream.ImageOutputStream;
import java.awt.image.BufferedImage;
import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.nio.file.Files;
import java.util.Iterator;
import java.util.Objects;

/**
 * 图片压缩工具类
 *
 * <AUTHOR>
 * @since
 */
@Slf4j
public class ImageCompressUtils {

    /**
     * 压缩文件，将压缩后的内容回写到原始文件
     *
     * @param imageFile 被压缩的文件
     * @param maxSize   压缩至不超过该大小
     */
    public static void compressJpeg(File imageFile, long maxSize) {
        String name = imageFile.getName().toLowerCase();
        if (!imageFile.exists() || (!name.endsWith(".jpg") && !name.endsWith(".jpeg"))) {
            return;
        }

        // 每次以0.75的质量压缩, 将文件压缩变小，最多压缩3次
        long fileSize = imageFile.length();
        boolean isSuccess = true;
        for (int i = 0; i < 3 && isSuccess && fileSize > maxSize; i++, fileSize = imageFile.length()) {
            log.info("compressing file: " + imageFile.getAbsolutePath() + " the " + i + "th time, fileSize:" + fileSize);

            isSuccess = doCompressJpeg(imageFile);
        }
    }

    private static boolean doCompressJpeg(File imageFile) {
        String absolutePath = imageFile.getAbsolutePath();
        File compressedImageFile = new File(imageFile.getAbsolutePath() + ".jpg");
        ImageWriter writer = null;
        try (InputStream is = Files.newInputStream(imageFile.toPath());
             OutputStream os = Files.newOutputStream(compressedImageFile.toPath());
             ImageOutputStream ios = ImageIO.createImageOutputStream(os)) {
            float quality = 0.75f;
            BufferedImage image = ImageIO.read(is);

            // get all image writers for JPG format
            Iterator<ImageWriter> writers = ImageIO.getImageWritersByFormatName("jpg");
            if (!writers.hasNext()) {
                log.warn("No writers found to compress file: " + absolutePath);
                return false;
            }

            writer = writers.next();
            writer.setOutput(ios);
            ImageWriteParam param = writer.getDefaultWriteParam();

            // compress to a given quality
            param.setCompressionMode(ImageWriteParam.MODE_EXPLICIT);
            param.setCompressionQuality(quality);

            // appends a complete image stream containing a single image and
            //associated stream and image metadata and thumbnails to the output
            writer.write(null, new IIOImage(image, null, null), param);

            // copy compressed file content to source file
            FileUtils.copyFile(compressedImageFile, imageFile);
        } catch (IOException e) {
            log.warn("failed compress file: " + absolutePath, e);
            return false;
        } finally {
            if (Objects.nonNull(writer)) {
                writer.dispose();
            }

            // 清理临时文件
            if (compressedImageFile.exists()) {
                FileUtils.deleteQuietly(compressedImageFile);
            }
        }

        return true;
    }

}
