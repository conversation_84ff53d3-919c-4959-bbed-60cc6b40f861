package com.egeio.services.transformer.utils;

import com.alibaba.fastjson.JSON;
import com.egeio.services.transformer.preconvert.PrePdfWatermark;
import org.springframework.util.StringUtils;

/**
 *
 * <AUTHOR>
 * @date 2022/3/30 下午3:40
 */
public class ColorUtil {


    /**
     * @param rgbArr
     * @return
     */
    public static String RGB2Hex(int [] rgbArr){
        String hex = String.format("#%02X%02X%02X", rgbArr[0], rgbArr[1], rgbArr[2]);
        return hex;
    }

    /**
     * 16进制颜色字符串转换成rgb
     * @param hexStr
     * @return rgb
     */
    public static int[] hex2RGB(String hexStr) {

        if (!StringUtils.hasLength(hexStr) || hexStr.length() != 7 || !hexStr.startsWith("#")) {
            hexStr = PrePdfWatermark.COLOR;
        }

        int[] rgb = new int[3];
        rgb[0] = Integer.valueOf(hexStr.substring(1, 3), 16);
        rgb[1] = Integer.valueOf(hexStr.substring(3, 5), 16);
        rgb[2] = Integer.valueOf(hexStr.substring(5, 7), 16);
        return rgb;
    }

    public static void main(String[] args) {

//        int[] rgbArr = {187, 255, 255};
        int[] rgbArr = {255 ,250 ,250};
        String rgb2Hex2 = RGB2Hex(rgbArr);
        System.out.println(rgb2Hex2);


        int[] ints = hex2RGB("#FFFAFA");
        System.out.println(JSON.toJSONString(ints));

    }
}
