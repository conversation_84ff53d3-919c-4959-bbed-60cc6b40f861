package com.egeio.services.transformer.utils;

import com.bimface.sdk.BimfaceClient;
import com.egeio.core.config.Config;

public class BimfaceClientFactory {
    private static BimfaceClient client;
    private static String appKey;
    private static String appSecret;

    private BimfaceClientFactory() {
    }

    public static BimfaceClient getClient() {
        if (client == null) {
            appKey = Config.getConfig()
                    .getElement("/configuration/bimface/app_key").getTextTrim();
            appSecret = Config.getConfig()
                    .getElement("/configuration/bimface/app_secret")
                    .getTextTrim();
            client = new BimfaceClient(appKey, appSecret);
        }
        return client;
    }

}
