package com.egeio.services.transformer.utils;

import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;

import org.apache.poi.EncryptedDocumentException;
import org.apache.poi.hslf.usermodel.HSLFSlideShow;
import org.apache.poi.hwpf.HWPFDocument;
import org.apache.poi.poifs.filesystem.NPOIFSFileSystem;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.ss.usermodel.WorkbookFactory;
import org.dom4j.Element;

import com.egeio.core.config.Config;
import com.egeio.core.log.Logger;
import com.egeio.core.log.LoggerFactory;
import com.egeio.core.log.MyUUID;
import com.egeio.core.tika.utils.TikaUtils;

public class FileTypeDetector {
    private static HashMap<String, List<String>> extToContentTypeMapping;
    private static HashMap<String, List<String>> extToContentTypeProtectedMapping;
    private static Logger logger = LoggerFactory
            .getLogger(FileTypeDetector.class);

    static {
        loadFileContentTypeMapping();
    }

    private static void loadFileContentTypeMapping() {
        extToContentTypeMapping = loadFileContentTypeMappings("/configuration/tika_file_type_detector/mapping");
        extToContentTypeProtectedMapping = loadFileContentTypeMappings("/configuration/tika_protect_file_type_detector/mapping");
    }


    private static HashMap<String, List<String>> loadFileContentTypeMappings(String config) {
        HashMap<String, List<String>> typeMappings = new HashMap<String, List<String>>();

        List<Element> mappings = Config.getConfig()
                .getElements(config);
        for (Element mapping : mappings) {
            String extensions = mapping.attributeValue("extensions").trim();
            String type = mapping.attributeValue("type").trim();

            for (String item : extensions.split(",")) {
                item = item.trim();
                if (typeMappings.get(item) == null) {
                    List<String> list = new ArrayList<String>();
                    typeMappings.put(item, list);
                }

                typeMappings.get(item).add(type);
            }
        }
        return typeMappings;
    }

    public static String detect(String filePath, MyUUID uuid)
            throws IOException {
        String type = TikaUtils.detect(new File(filePath));
        logger.info(uuid, "file [content type: {}]", type);

        return type;
    }

    /**
     * Only detect the files whose extension are in the white list. Return empty
     * string if not in the white list.
     * 
     * @param filePath
     * @param extension
     * @param uuid
     * @return the file type. empty string means no need to check. null means
     *         unknown.
     * @throws IOException
     */
    public static String checkTypeByExtension(String filePath, String extension,
            MyUUID uuid) throws IOException {
        File file = new File(filePath);
        // Only check non-empty files
        if (file.length() > 0 && extToContentTypeMapping
                .containsKey(extension.toLowerCase())) {
            return detect(filePath, uuid);
        }

        return "";
    }

    /**
     * @param fileType
     *            empty string means no need to check. null means unknown
     * @param extension
     * @return whether the file extension matches
     */
    public static boolean checkIfProtectExtension(String filePath, String fileType, String extension) {

        if (fileType != null) {
            List<String> typeList = extToContentTypeProtectedMapping.get(extension);
            if (typeList != null && typeList.contains(fileType)) {
                return true;
            }
        }

        switch (extension) {
            case "xls":
                return checkProtectXls(filePath);
            case "doc":
                return checkProtectDoc(filePath);
            case "ppt":
                return checkProtectPpt(filePath);
        }
        return false;
    }

    private static boolean checkProtectXls(String filePath) {
        try (Workbook wb = WorkbookFactory.create(new File(filePath))) {
        } catch (EncryptedDocumentException e) {
            // Password protected, try to decrypt and load
            return true;
        } catch (Exception e) {

        }
        return false;
    }

    private static boolean checkProtectDoc(String filePath) {
        try (NPOIFSFileSystem fs = new NPOIFSFileSystem(new FileInputStream(filePath));
             HWPFDocument doc = new HWPFDocument(fs.getRoot())) {
        } catch (EncryptedDocumentException e) {
            // Password protected, try to decrypt and load
            return true;
        } catch (Exception e) {

        }
        return false;
    }
    private static boolean checkProtectPpt(String filePath) {
        try (NPOIFSFileSystem fs = new NPOIFSFileSystem(new FileInputStream(filePath));
             HSLFSlideShow ppt = new HSLFSlideShow(fs)) {
        } catch (EncryptedDocumentException e) {
            // Password protected, try to decrypt and load
            return true;
        } catch (Exception e) {

        }
        return false;
    }

    /**
     * @param fileType
     *            empty string means no need to check. null means unknown
     * @param extension
     * @return whether the file extension matches
     */
    public static boolean checkIfCorrectExtension(String fileType,
            String extension) {
        if ("".equals(fileType)) {
            return true;
        }
        else {
            if (fileType != null) {
                List<String> typeList = extToContentTypeMapping.get(extension);
                if (typeList.contains(fileType)) {
                    return true;
                }
            }
            return false;
        }
    }

    public static void main(String[] args) throws IOException {
        String folder = "/Users/<USER>/Desktop/Office_Password/files";
        File file = new File(folder);
        for (File f : file.listFiles()) {
            String type = FileTypeDetector.detect(f.getAbsolutePath(),
                    new MyUUID());
            System.out.println(f.getName() + ": " + type);
            String ext = f.getName().substring(f.getName().lastIndexOf('.') + 1);
            System.out.println("is protect: " + FileTypeDetector.checkIfProtectExtension(f.getAbsolutePath(), type, ext));
        }
    }
}
