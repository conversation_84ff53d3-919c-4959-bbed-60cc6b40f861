package com.egeio.services.transformer.utils;

import java.io.File;
import java.io.IOException;
import java.nio.file.Paths;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.UUID;

import com.alibaba.fastjson.JSON;
import com.egeio.services.transformer.constant.Errors;
import com.egeio.services.transformer.exception.TransformerException;
import com.egeio.services.transformer.models.WatermarkInfo;
import com.egeio.services.transformer.models.watermark.WatermarkParam;
import com.egeio.services.transformer.preconvert.PreImageWatermark;
import org.apache.tika.Tika;
import org.dom4j.Element;
import org.im4java.core.*;
import org.im4java.process.ArrayListOutputConsumer;

import com.drew.imaging.ImageMetadataReader;
import com.drew.metadata.Metadata;
import com.drew.metadata.exif.ExifIFD0Directory;
import com.egeio.core.config.Config;
import com.egeio.core.log.Logger;
import com.egeio.core.log.LoggerFactory;
import com.egeio.core.log.MyUUID;
import com.egeio.core.utils.ImageInfo;
import com.egeio.services.transformer.actors.worker.ImageWorker;
import com.egeio.services.transformer.models.ConversionStatus;
import com.egeio.services.transformer.models.ImageMeta;
import com.egeio.services.transformer.models.PreviewType;
import com.egeio.services.transformer.preconvert.AbstractImagePreConvert;
import org.springframework.util.StringUtils;

public class ImageUtils {
    private static Logger logger = LoggerFactory.getLogger(ImageWorker.class);

    private static String cmykIcc;
    private static String rgbIcc;

    public static int MAX_SIZE = 3000;
    public static int MAX_ACCEPT_SIZE = 15000;
    public static int MAX_DISPLAY_SIZE = 8000;

    private static String background = "white";
    private static List<String> multiImageList = new ArrayList<String>();
    private static List<String> orientationList = new ArrayList<String>();
    private static List<String> cmykImageList = new ArrayList<String>();
    private static List<String> forceConvertImageList = new ArrayList<String>();

    public static int WATERMARK_SIZE_WIDTH;
    public static int WATERMARK_SIZE_HEIGHT;
    private static int WATERMARK_POINTSIZE;
    private static String WATERMARK_FILL;
    private static String WATERMARK_FONT;
    private static int WATERMARK_ROTATE;
    private static int WATERMARK_BORDER;
    private static int WATERMARK_DISSOLVE;

    private static List<String> getList(String elementPath) {
        Element ele = Config.getConfig().getElement(elementPath);
        List<String> result = new ArrayList<>();
        if (ele != null) {
            String listStr = ele.getTextTrim();
            if (listStr != null) {
                String[] listArray = listStr.split(",");
                for (int i = 0; i < listArray.length; i++) {
                    listArray[i] = listArray[i].trim().toLowerCase();
                    if (!listArray[i].isEmpty()) {
                        result.add(listArray[i]);
                    }
                }
            }
        }

        return result;
    }

    static {
        MAX_SIZE = Config.getNumber("/configuration/image/max_pixel_size",
                MAX_SIZE);

        background = Config.getString("/configuration/image/background",
                background);

        multiImageList = getList("/configuration/image/multi_list");
        orientationList = getList("/configuration/image/orientation_list");
        forceConvertImageList = getList(
                "/configuration/image/force_convert_list");
        cmykImageList = getList("/configuration/image/cmyk_list");

        MAX_ACCEPT_SIZE = Config.getNumber(
                "/configuration/image/max_accept_size", MAX_ACCEPT_SIZE);
        MAX_DISPLAY_SIZE = Config.getNumber(
                "/configuration/image/max_display_size", MAX_DISPLAY_SIZE);

        Element ele = Config.getConfig()
                .getElement("/configuration/image/cmyk_icc");
        if (ele != null) {
            cmykIcc = ele.getTextTrim();
        }

        ele = Config.getConfig().getElement("/configuration/image/rgb_icc");
        if (ele != null) {
            rgbIcc = ele.getTextTrim();
        }

        // watermark settings
        WATERMARK_SIZE_WIDTH = Config
                .getNumber("/configuration/watermark/image/size", "width", 800);
        WATERMARK_SIZE_HEIGHT = Config.getNumber(
                "/configuration/watermark/image/size", "height", 120);
        WATERMARK_POINTSIZE = Config
                .getNumber("/configuration/watermark/image/pointsize", 30);
        WATERMARK_ROTATE = Config
                .getNumber("/configuration/watermark/image/rotate", 350);
        WATERMARK_BORDER = Config
                .getNumber("/configuration/watermark/image/border", 10);
        WATERMARK_DISSOLVE = Config
                .getNumber("/configuration/watermark/image/dissolve", 50);
        WATERMARK_FILL = Config.getString("/configuration/watermark/image/fill",
                "gray50");
        WATERMARK_FONT = Paths.get(
                Config.getString("/configuration/watermark/font_dir",
                        "/usr/share/fonts/my_fonts"),
                Config.getString("/configuration/watermark/image/font",
                        "msyh.ttf"))
                .toString();
    }

    public static String detectColorSpace(String fileName, MyUUID uuid) {
        try {
            checkImage(fileName);
            IdentifyCmd cmd = new IdentifyCmd();
            ArrayListOutputConsumer consumer = new ArrayListOutputConsumer();
            cmd.setOutputConsumer(consumer);

            IMOperation imOperation = new IMOperation();
            imOperation.addRawArgs("-format", "%[colorspace]");
            imOperation.addImage(fileName);

            cmd.run(imOperation);

            List<String> outputStrs = consumer.getOutput();
            if (outputStrs.size() > 0) {
                return outputStrs.get(0);
            }
        }
        catch (Exception e) {
            logger.warn(uuid, "failed to detect color space", e);
        }

        return "unknown";
    }

    /**
     * 图片旋转方向问题
     * Orientation属性为：
     * 旋转角度	   参数
     * 0°	        1
     * 顺时针90°	    6
     * 逆时针90°	    8
     * 180°	        3
     * @param fileName
     * @param uuid
     * @return
     */
    public static int detectOrientation(String fileName, MyUUID uuid) {
        int orientation = 1;

        try {
            //获取文件的媒体数据
            Metadata metadata = ImageMetadataReader.readMetadata(new File(fileName));

            //查看当前的文件是否包含exif信息
            if (metadata.containsDirectoryOfType(ExifIFD0Directory.class)) {
                //查看是否包含方向信息，如果包含就赋值给orientation
                ExifIFD0Directory exifIFD0Directory = metadata.getFirstDirectoryOfType(ExifIFD0Directory.class);
                Integer orientationInteger = exifIFD0Directory.getInteger(ExifIFD0Directory.TAG_ORIENTATION);
                if(Objects.nonNull(orientationInteger)){
                    orientation = orientationInteger;
                }
            }
        }
        catch (Exception e) {
            logger.warn(uuid, "failed to detect orientation", e);
        }

        return orientation;
    }

    /**
     * Calculate the enlarge multiple for long pictures
     * 
     * @param height
     * @param width
     * @return the ratio to multiple. Min to 1.
     */
    private static double calculateMultipleForLongPic(int height, int width) {
        // make sure height is bigger than width after swap
        if (height < width) {
            int temp = height;
            height = width;
            width = temp;
        }

        // muliple for half of height width ratio
        double multi = (height / 2.0) / width;

        // multiple will be at lease 1
        if (multi < 1) {
            multi = 1;
        }

        return multi;
    }

    /**
     * height and width should be smaller than max size This function calculate
     * the lessen multiple to restrict into the max size.
     * 
     * @param height
     *            current height
     * @param width
     *            current width
     * @param maxSize
     *            max size
     * @return the lessen multiple to apply. Max to 1
     */
    private static double getMultiViaMaxSize(double height, double width,
            int maxSize) {
        double multi = 1;

        if (maxSize != -1) {
            double tmp = height / maxSize;
            if (tmp > multi) {
                multi = tmp;
            }

            tmp = width / maxSize;
            if (tmp > multi) {
                multi = tmp;
            }
        }

        return multi;
    }

    /**
     * Crop the image to the target size
     * 
     * @param preConvert
     * @param uuid
     * @param targetWidth
     * @param targetHeight
     * @return the job status
     * @throws IOException
     * @throws InterruptedException
     * @throws IM4JavaException
     */
    public static ConversionStatus cropImage(AbstractImagePreConvert preConvert,
            MyUUID uuid, int targetWidth, int targetHeight, ImageMeta meta)
            throws IOException, InterruptedException, IM4JavaException {
        logger.info(uuid, "start crop image");

        ConversionStatus result = doImageWork(preConvert.getSourceFilePath(),
                preConvert.getTargetFilePath(), targetWidth, targetHeight,
                MAX_SIZE, preConvert.getPreviewType(), true, meta, uuid);

        return result;
    }

    public static ConversionStatus waterFlowImage(
            AbstractImagePreConvert preConvert, MyUUID uuid, int targetWidth,
            ImageMeta meta)
            throws IOException, InterruptedException, IM4JavaException {
        logger.info(uuid, "start do warter flow image");

        ConversionStatus result = doImageWork(preConvert.getSourceFilePath(),
                preConvert.getTargetFilePath(), targetWidth, targetWidth,
                MAX_SIZE, preConvert.getPreviewType(), true, meta, uuid);

        return result;
    }

    /**
     * Compress the imsage to the max size
     * 
     * @param preConvert
     * @param uuid
     * @return the job status
     * @throws IOException
     * @throws InterruptedException
     * @throws IM4JavaException
     */
    public static ConversionStatus compressImage(
            AbstractImagePreConvert preConvert, MyUUID uuid, int maxSize,
            ImageMeta meta)
            throws IOException, InterruptedException, IM4JavaException {
        logger.info(uuid, "try compress image to max size {}", maxSize);

        ConversionStatus result = doImageWork(preConvert.getSourceFilePath(),
                preConvert.getTargetFilePath(), maxSize, maxSize, maxSize,
                preConvert.getPreviewType(), false, meta, uuid);

        if (!result.isFail()) {
            if (result == ConversionStatus.IGNORE) {
                logger.info(uuid, "no need to compress");
            }
        }

        return result;
    }

    public static ConversionStatus compressImage(File f, File workingDir,
            String type, MyUUID uuid, int maxSize, ImageMeta meta)
            throws IOException, InterruptedException, IM4JavaException {
        File tmpOutput = new File(workingDir,
                UUID.randomUUID().toString() + '.' + type);
        ConversionStatus result = doImageWork(f.getAbsolutePath(),
                tmpOutput.getAbsolutePath(), maxSize, maxSize, maxSize,
                PreviewType.PREVIEW, false, meta, uuid);

        if (!result.isFail()) {
            if (result == ConversionStatus.IGNORE) {
                logger.info(uuid, "no need to compress");
            }
            else {
                com.egeio.core.utils.Utils.rename(tmpOutput, f);
            }
        }

        return result;
    }

    /**
     * Do real image work to convert image
     * 
     * @param sourcePath
     * @param targetPath
     * @param targetWidth
     * @param targetHeight
     * @param isCrop
     * @param forceConvert
     * @param uuid
     * @return the job status
     * @throws IOException
     * @throws InterruptedException
     * @throws IM4JavaException
     */
    public static ConversionStatus doImageWork(String sourcePath,
            String targetPath, int targetWidth, int targetHeight, int maxSize,
            PreviewType type, boolean forceConvert, ImageMeta meta, MyUUID uuid)
            throws IOException, InterruptedException, IM4JavaException {
        return doImageWork(sourcePath, targetPath, targetWidth, targetHeight,
                maxSize, type, forceConvert, false, false, meta, uuid);
    }

    /**
     * Do the real image job to convert the image
     * 
     * @param sourcePath
     * @param targetPath
     * @param targetWidth
     * @param targetHeight
     * @param isCrop
     * @param forceConvert
     * @param meta
     *            the output to put the image meta, such as height and width
     * @param uuid
     * @return the job status
     * @throws IOException
     * @throws InterruptedException
     * @throws IM4JavaException
     */
    public static ConversionStatus doImageWork(String sourcePath,
            String targetPath, int targetWidth, int targetHeight, int maxSize,
            PreviewType type, boolean forceConvert, boolean firstLayer,
            boolean checkLarge, ImageMeta meta, MyUUID uuid)
            throws IOException, InterruptedException, IM4JavaException {
        checkImage(sourcePath);
        // create command
        ConvertCmd cmd = new ConvertCmd();
        // create the operation, add images and operators/options
        IMOperation op = new IMOperation();

        String pathWithLayer = sourcePath;

        if (firstLayer) {
            pathWithLayer += "[0]";
        }

        op.addImage(pathWithLayer); // source file

        for (String ext : cmykImageList) {
            if (sourcePath.toLowerCase().endsWith(ext)) {
                String colorSpace = detectColorSpace(pathWithLayer, uuid);
                if (colorSpace.equalsIgnoreCase("cmyk")) {
                    op.profile(cmykIcc);
                    op.profile(rgbIcc);
                    op.colorspace("srgb");
                    forceConvert = true;
                }
                break;
            }
        }

        boolean getSceneCount = false;
        for (String ext : multiImageList) {
            if (sourcePath.toLowerCase().endsWith(ext)) {
                getSceneCount = true;
                break;
            }
        }

        ImageInfo info = ImageInfo.getImageInfo(sourcePath, getSceneCount,
                firstLayer);

        // IE doesn't support some types such as JP2. Force converting it to jpg
        for (String imgType : forceConvertImageList) {
            if (info.getFormatName().equalsIgnoreCase(imgType)) {
                forceConvert = true;
                break;
            }
        }
        if (getSceneCount && info.getHeight() == 1 && info.getWidth() == 1){
            logger.warn(uuid,"获取图片长度宽度异常,进行降级Height=" + info.getHeight() + ",Width=" + info.getWidth());
            info = ImageInfo.getImageInfo(sourcePath, false,
                    firstLayer);
        }

        int orientation = 0;

        if (meta != null) {
            meta.setHeight(info.getHeight());
            meta.setWidth(info.getWidth());
            meta.setForceConvert(forceConvert);

            for (String ext : orientationList) {
                if (sourcePath.toLowerCase().endsWith(ext)) {
                    orientation = detectOrientation(sourcePath, uuid);
                    meta.setRotation(orientation);
                    break;
                }
            }
        }

        if (checkLarge && Math.max(info.getHeight(),
                info.getWidth()) > MAX_ACCEPT_SIZE) {
            return ConversionStatus.TOO_LARGE;
        }

        int number = info.getNumberOfImages();
        // check if more than one scene
        if (number > 1) {
            if (number == 2) {
                op.delete(1);
            }
            else {
                op.delete("1-" + (number - 1));
            }
        }

        op.background(background);
        op.flatten();

        switch (type) {
        case THUMBNAIL:
            dealThumbnail(op, targetWidth, targetHeight);
            break;
        case PREVIEW:
            if (!dealPreview(op, info, targetWidth, targetHeight, maxSize,
                    forceConvert, meta)) {
                return ConversionStatus.IGNORE;
            }
            break;
        case WATER_FLOW:
            dealWaterFlow(op, info, targetWidth, orientation);
            break;
        default:
            break;
        }

        op.addImage(targetPath);

        try {
            cmd.run(op);
        }
        catch (CommandException e) {
            File target = new File(targetPath);
            if (target.exists() && target.isFile() && target.length() > 0) {
                logger.warn(uuid,
                        "ImageMagic failed but file generated, maybe warning",
                        e);
            }
            else {
                throw e;
            }
        }

        return ConversionStatus.CONVERTED;
    }

    private static void dealThumbnail(IMOperation op, int targetWidth,
            int targetHeight) {
        op.autoOrient();
        op.strip();
        op.resize(targetWidth, targetHeight, "^");
        op.gravity("center");
        op.crop(targetWidth, targetHeight, 0, 0);
    }

    private static boolean dealPreview(IMOperation op, ImageInfo info,
            int targetWidth, int targetHeight, int maxSize,
            boolean forceConvert, ImageMeta meta) {
        int height = info.getHeight();
        int width = info.getWidth();

        double multiple = calculateMultipleForLongPic(height, width);

        double finalWidth = targetWidth * multiple;
        double finalHeight = targetHeight * multiple;

        double finalMulti = getMultiViaMaxSize(finalHeight, finalWidth,
                maxSize);

        finalWidth /= finalMulti;
        finalHeight /= finalMulti;

        if (finalMulti > 1) {
            if (meta != null) {
                meta.setReachMax(true);
            }
        }

        if (Math.max(finalHeight, finalWidth) >= Math.max(width, height)) {
            if (meta != null) {
                meta.setReachMax(true);
            }
            if (!forceConvert) {
                return false;
            }
            else {
                finalWidth = width;
                finalHeight = height;
            }
        }
        op.p_profile("!exif,!icc,*");
        op.resize((int) finalWidth, (int) finalHeight, ">");

        return true;
    }

    private static void dealWaterFlow(IMOperation op, ImageInfo info,
            int targetWidth, int orientation) {
        int height = info.getHeight();
        int width = info.getWidth();

        if (orientation >= 5 && orientation <= 8) {
            int tmp = height;
            height = width;
            width = tmp;
        }

        double multiple = width * 1.0 / targetWidth;
        int targetHeight = (int) Math.min(height / multiple, targetWidth * 2);

        op.autoOrient();
        op.strip();
        op.resize(targetWidth, targetHeight, "^");
        op.gravity("center");
        op.crop(targetWidth, targetHeight, 0, 0);
    }

    /**
     * @param sourcePath
     * @param targetPath
     * @param targetWidth
     * @param targetHeight
     * @param isCrop
     * @param uuid
     * @return the svg job status
     * @throws IOException
     * @throws InterruptedException
     * @throws IM4JavaException
     */
    public static ConversionStatus doSvgWork(String sourcePath,
            String targetPath, int targetWidth, int targetHeight,
            PreviewType type, ImageMeta meta, MyUUID uuid)
            throws IOException, InterruptedException, IM4JavaException {
        checkImage(sourcePath);
        if (type == null) {
            type = PreviewType.PREVIEW;
        }
        switch (type) {
        case THUMBNAIL:
        case WATER_FLOW:
            return doImageWork(sourcePath, targetPath, targetWidth,
                    targetHeight, MAX_SIZE, type, true, meta, uuid);
        case PREVIEW:
            // create command
            ConvertCmd cmd = new ConvertCmd();
            // create the operation, add images and operators/options
            IMOperation op = new IMOperation();

            op.addImage(sourcePath); // source file

            ImageInfo info = ImageInfo.getImageInfo(sourcePath, false, false);

            op.flatten();
            op.p_profile("!exif,!icc,*");

            op.resize(info.getWidth(), info.getHeight());

            op.addImage(targetPath);

            if (meta != null) {
                meta.setHeight(info.getHeight());
                meta.setWidth(info.getWidth());
            }

            try {
                cmd.run(op);
            }
            catch (CommandException e) {
                File target = new File(targetPath);
                if (target.exists() && target.isFile() && target.length() > 0) {
                    logger.warn(uuid,
                            "ImageMagic failed but file generated, maybe warning",
                            e);
                }
                else {
                    throw e;
                }
            }

            return ConversionStatus.CONVERTED;

        default:
            return ConversionStatus.UNKNOWN;
        }
    }

    public static void trimImage(String sourcePath, String targetPath,
            String background, int border, MyUUID uuid)
            throws IOException, InterruptedException, IM4JavaException {
        checkImage(sourcePath);
        ConvertCmd cmd = new ConvertCmd();
        // create the operation, add images and operators/options
        IMOperation op = new IMOperation();

        op.addImage(sourcePath); // source file

        op.p_profile("!exif,!icc,*");
        op.trim();
        op.bordercolor(background);
        op.border(border);

        op.addImage(targetPath);

        try {
            cmd.run(op);
        }
        catch (CommandException e) {
            File target = new File(targetPath);
            if (target.exists() && target.isFile() && target.length() > 0) {
                logger.warn(uuid,
                        "ImageMagic failed but file generated, maybe warning",
                        e);
            }
            else {
                throw e;
            }
        }
    }

    /**
     * add watermark on image
     *
     * @param preImageWatermark
     * @param uuid
     * @throws IOException
     * @throws IM4JavaException
     * @throws InterruptedException
     */
    public static void addWatermark(PreImageWatermark preImageWatermark, MyUUID uuid)
            throws IOException, IM4JavaException, InterruptedException {

        String sourcePath = preImageWatermark.getSourceFilePath();
        String rotateSourcePath = preImageWatermark.getRotateSourceFilePath();
        String watermarkPath = preImageWatermark.getWatermarkFilePath();
        String targetPath = preImageWatermark.getTargetFilePath();

        //水印信息
        String paragraph = preImageWatermark.getWatermarkParagraph();
        WatermarkInfo watermarkInfo = preImageWatermark.getWatermarkInfo();

        String color = WATERMARK_FILL;
        int dissolve = WATERMARK_DISSOLVE;
        int fontSize = WATERMARK_POINTSIZE;
        if(watermarkInfo.isCustom()){
            //颜色
            if(!StringUtils.isEmpty(watermarkInfo.getFontColor())){
                color = watermarkInfo.getFontColor();
            }
            //透明度
            if(Objects.nonNull(watermarkInfo.getOpacity())){
                dissolve =  (int) (watermarkInfo.getOpacity() * 100);
            }

            if(Objects.nonNull(watermarkInfo.getFontSize())){
                fontSize =watermarkInfo.getFontSize();
            }

       }


        checkImage(sourcePath);
        logger.info(uuid,"图片添加水印 sourcePath:{} rotateSourcePath:{} watermarkPath:{} targetPath:{}",sourcePath,rotateSourcePath,watermarkPath,targetPath);
        logger.info(uuid,"图片添加水印 color:{} dissolve:{} fontSize:{}",color,dissolve,fontSize);

        try {
            // prepare watermark image
            ConvertCmd prepareCmd = new ConvertCmd();
            IMOperation op = new IMOperation();
            op.size(WATERMARK_SIZE_WIDTH, WATERMARK_SIZE_HEIGHT);
            op.addRawArgs("xc:transparent");
            op.font(WATERMARK_FONT);
            op.pointsize(fontSize);
            op.fill(color);
            op.gravity("center");
            op.annotate(WATERMARK_ROTATE, WATERMARK_ROTATE, 0, 0, paragraph);
            op.p_profile("!exif,!icc,*");
            op.trim();
            op.bordercolor("transparent");
            op.border(WATERMARK_BORDER);
            op.addImage(watermarkPath);
            prepareCmd.run(op);

            String finalSourcePath = sourcePath;
            // auto rotate if need
            int orientation = detectOrientation(sourcePath, uuid);
            if (orientation != 1) {
                ConvertCmd rotateCmd = new ConvertCmd();
                op = new IMOperation();
                op.autoOrient();
                op.addImage(sourcePath);
                op.addImage(rotateSourcePath);
                rotateCmd.run(op);
                finalSourcePath = rotateSourcePath;
            }

            // add watermark to source
            CompositeCmd cmd = new CompositeCmd();
            op = new IMOperation();
            op.dissolve(dissolve);
            op.tile();
            op.addImage(watermarkPath);
            op.addImage(finalSourcePath);
            op.addImage(targetPath);
            cmd.run(op);
        }
        catch (CommandException e) {
            File target = new File(targetPath);
            if (target.exists() && target.isFile() && target.length() > 0) {
                logger.warn(uuid,
                        "ImageMagic failed but file generated, maybe warning",
                        e);
            }
            else {
                throw e;
            }
        }

    }

    private static void checkImage(String sourcePath) throws IOException {
        Tika tika = new Tika();
        String type = "";
        try {
            type = tika.detect(new File(sourcePath));
        }
        catch (IOException e) {

        }

        if ("text/plain".equals(type)) {
            throw new IOException("not a image");
        }
    }


    /**
     * 自定义水印
     * @param sourcePath
     * @param rotateSourcePath
     * @param watermarkPath
     * @param targetPath
     * @param watermarkParam
     * @param uuid
     * @throws IOException
     * @throws IM4JavaException
     * @throws InterruptedException
     */
    public static void addCustomWatermark(String sourcePath, String rotateSourcePath,
                                          String watermarkPath, String targetPath, WatermarkParam watermarkParam,
                                          MyUUID uuid)
            throws IOException, IM4JavaException, InterruptedException {
        checkImage(sourcePath);

        String watermarkContent = watermarkParam.getImageWatermarkContent();
        if(!StringUtils.hasLength(watermarkContent)){
            watermarkContent = "    ";
            logger.info(uuid, "[下载水印信息为空串]", watermarkContent);
        }
        logger.info(uuid, "[下载水印信息]  [水印内容]watermarkContent():{}", watermarkContent);
        try {
            ConvertCmd prepareCmd = new ConvertCmd();
            IMOperation op = new IMOperation();
            //水印大小
            op.size(watermarkParam.getImageWatermarkSizeWidth(), watermarkParam.getImageWatermarkSizeHeight());
            op.addRawArgs("xc:transparent");
            //字体
            op.font(WATERMARK_FONT);
            op.pointsize(watermarkParam.getFontSize());
            //颜色
            op.fill(watermarkParam.getFontColor());
            op.gravity("center");
            op.annotate(WATERMARK_ROTATE, WATERMARK_ROTATE, 0, 0, watermarkContent);
            op.p_profile("!exif,!icc,*");
            op.trim();
            op.bordercolor("transparent");
            op.border(WATERMARK_BORDER);
            op.addImage(watermarkPath);
            logger.info(uuid,"ConvertCmd IMOperation:{}", JSON.toJSONString(op));
            prepareCmd.run(op);

            String finalSourcePath = sourcePath;
            // auto rotate if need
            int orientation = detectOrientation(sourcePath, uuid);
            if (orientation != 1) {
                ConvertCmd rotateCmd = new ConvertCmd();
                op = new IMOperation();
                op.autoOrient();
                op.addImage(sourcePath);
                op.addImage(rotateSourcePath);
                rotateCmd.run(op);
                finalSourcePath = rotateSourcePath;
            }

            // add watermark to source
            CompositeCmd cmd = new CompositeCmd();
            op = new IMOperation();

            //水印透明度 0 ~ 100 越大颜色越深
            int dissolve = (int) (watermarkParam.getOpacity()*100);
            op.dissolve(dissolve);
            op.tile();
            op.addImage(watermarkPath);
            op.addImage(finalSourcePath);
            op.addImage(targetPath);

            logger.info(uuid,"CompositeCmd IMOperation:{}", JSON.toJSONString(op));

            cmd.run(op);
        }
        catch (CommandException e) {
            File target = new File(targetPath);
            if (target.exists() && target.isFile() && target.length() > 0) {
                logger.warn(uuid, "ImageMagic failed but file generated, maybe warning", e);
            }
            else {

                logger.error(uuid, "ImageMagic convert failed", e);
                throw new TransformerException(Errors.FILE_CONVERT_ERROR);
            }
        }

    }


    public static void addWatermark(String sourcePath, String rotateSourcePath,String targetPath, WatermarkInfo watermarkInfo,
                                    MyUUID uuid) throws InterruptedException, IOException, IM4JavaException {
        logger.info(uuid,"走新的图片水印流程");
        checkImage(sourcePath);
        String finalSourcePath = sourcePath;
        // auto rotate if need
        int orientation = detectOrientation(sourcePath, uuid);
        if (orientation != 1) {
            logger.info(uuid,"需要旋转图片");
            ConvertCmd rotateCmd = new ConvertCmd();
            IMOperation op = new IMOperation();
            op.autoOrient();
            op.addImage(sourcePath);
            op.addImage(rotateSourcePath);
            rotateCmd.run(op);
            finalSourcePath = rotateSourcePath;
        }

        // 获取图片尺寸
        logger.info(uuid,"获取初始文件=" + finalSourcePath);
        Info imageInfo = new Info(finalSourcePath, true);
        int imageWidth = imageInfo.getImageWidth();
        int imageHeight = imageInfo.getImageHeight();
        logger.info(uuid,"图片大小=" + imageWidth + "," + imageHeight);

        // 计算水印间隔
        int length = 0;
        String watermarkContent;
        if (watermarkInfo.isCustom()){
            for (String content : watermarkInfo.getContentList()){
                if (content.length() > length){
                    length = content.length();
                }
            }
            //水印文字内容居中对齐
            StringBuilder builder = new StringBuilder();
            for (String content : watermarkInfo.getContentList()){
                if (content.length() < length){
                    int index = (length - content.length());
                    String blank = "";
                    for (int i = 0;i < index ;i++ ){
                        blank += " ";
                    }
                    content = blank + content;
                }
                builder.append(content).append("\\n");
            }
            watermarkContent = builder.toString();
        }else {
            watermarkContent = watermarkInfo.getWatermark();
            length = watermarkContent.length();
        }

        String color = "#000000";
        int dissolve = WATERMARK_DISSOLVE;
        int fontSize = WATERMARK_POINTSIZE;
        int lineDistance = 5;
        if(watermarkInfo.isCustom()){
            //颜色
            if(!StringUtils.isEmpty(watermarkInfo.getFontColor())){
                color = watermarkInfo.getFontColor();
            }
            //透明度
            if(Objects.nonNull(watermarkInfo.getOpacity())){
                dissolve =  (int) (watermarkInfo.getOpacity() * 100);
            }

            if(Objects.nonNull(watermarkInfo.getFontSize())){
                fontSize = watermarkInfo.getFontSize();
            }
            if (Objects.nonNull(watermarkInfo.getWatermarkLineDistance())){
                lineDistance = watermarkInfo.getWatermarkLineDistance();
            }

        }


        int watermarkIntervalWidth = 300 + length * 2;
        int watermarkIntervalHeight = 100 + lineDistance * 20;
        int rotateAngle = -30;
        logger.info(uuid,"水印间隔=" + watermarkIntervalWidth + "," + watermarkIntervalHeight);


        // 创建一个新的操作对象
        IMOperation op = new IMOperation();

        // 设置操作对象的输入文件
        op.addImage(finalSourcePath);
        op.background("white");
        op.flatten();
        // 设置水印文字样式
        op.font(WATERMARK_FONT);
        op.pointsize(fontSize);
        op.gravity("NorthWest");
        //颜色
        //水印透明度 0 ~ 100 越大颜色越深
        String hexOpacity = Integer.toHexString((int) (dissolve * 255 / 100));
        if (hexOpacity.length() == 1) {
            hexOpacity = "0" + hexOpacity;
        }
        op.fill(color + hexOpacity);
        op.strokewidth(1);
        op.stroke("none");



        // 计算水平和垂直方向的偏移量
        double radian = Math.toRadians(rotateAngle);
        int offsetX = (int) (watermarkIntervalHeight * Math.sin(radian));
        int offsetY = (int) (watermarkIntervalHeight * Math.cos(radian));

        // 平铺水印
        for (int x = -offsetX; x < imageWidth; x += watermarkIntervalWidth) {
            for (int y = -offsetY; y < imageHeight; y += watermarkIntervalHeight) {
                op.annotate(rotateAngle, rotateAngle, x, y, watermarkContent);
            }
        }

        // 设置输出文件
        op.addImage(targetPath);

        // 执行操作
        ConvertCmd cmd = new ConvertCmd();
        logger.info(uuid,"CompositeCmd IMOperation:{}", JSON.toJSONString(op));
        cmd.run(op);
    }
}
