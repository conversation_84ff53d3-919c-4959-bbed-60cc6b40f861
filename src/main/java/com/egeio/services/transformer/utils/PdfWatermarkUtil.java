package com.egeio.services.transformer.utils;

import com.alibaba.fastjson.JSON;
import com.egeio.core.log.Logger;
import com.egeio.core.log.LoggerFactory;
import com.egeio.core.log.MyUUID;
import com.egeio.services.transformer.models.WatermarkInfo;
import com.egeio.services.transformer.models.watermark.WatermarkContentParam;
import com.egeio.services.transformer.models.watermark.WatermarkParam;
import com.egeio.services.transformer.preconvert.PrePdfWatermark;
import com.google.common.base.Strings;
import com.itextpdf.io.font.PdfEncodings;
import com.itextpdf.kernel.colors.Color;
import com.itextpdf.kernel.colors.DeviceRgb;
import com.itextpdf.kernel.font.PdfFont;
import com.itextpdf.kernel.font.PdfFontFactory;
import com.itextpdf.kernel.geom.Rectangle;
import com.itextpdf.kernel.pdf.*;
import com.itextpdf.kernel.pdf.canvas.PdfCanvas;
import com.itextpdf.kernel.pdf.extgstate.PdfExtGState;
import com.itextpdf.layout.Document;
import com.itextpdf.layout.element.Paragraph;
import com.itextpdf.layout.property.TextAlignment;
import com.itextpdf.layout.property.VerticalAlignment;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.RandomStringUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.util.CollectionUtils;

import java.io.File;
import java.io.FileNotFoundException;
import java.io.IOException;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2022/3/23 上午10:28
 */
@Slf4j
public class PdfWatermarkUtil {

    private Logger logger = LoggerFactory.getLogger(this.getClass());

    /**
     *
     * @param myUUID
     * @param sourceFilePath
     * @param targetFilePath
     * @param watermarkParam
     */
    public void addWatermark(MyUUID myUUID, String sourceFilePath, String targetFilePath, WatermarkParam watermarkParam) {
        logger.info(myUUID, " 开始添加pdf水印");
        WriterProperties prop = new WriterProperties();
        // add edit password 加密操作
        if (Objects.nonNull(watermarkParam)
                && Objects.nonNull(watermarkParam.getDisableEdit())
                && watermarkParam.getDisableEdit()) {
            String password = watermarkParam.getDisableEditPassword();
            if (StringUtils.isBlank(password)) {
                password = RandomStringUtils.randomAscii(6);
            }
            prop.setStandardEncryption(null, password.getBytes(), 0,
                    EncryptionConstants.ENCRYPTION_AES_256);
        }

        try (PdfReader reader = new PdfReader(sourceFilePath);
             PdfDocument pdfDoc = new PdfDocument(reader.setUnethicalReading(true), new PdfWriter(targetFilePath, prop));
             Document document = new Document(pdfDoc)) {

            int n = pdfDoc.getNumberOfPages();
            for (int i = 1; i <= n; i++) {
                PdfPage page = pdfDoc.getPage(i);
                Rectangle pageSize = page.getPageSize();
                PdfCanvas canvas = new PdfCanvas(page);
                // draw watermark 添加水印
                Paragraph p = constructWatermarkParagraph(myUUID, pageSize, watermarkParam);
                canvas.saveState();
                //透明度
                PdfExtGState gs1 = new PdfExtGState().setFillOpacity(watermarkParam.getOpacity());
                canvas.setExtGState(gs1);

                document.showTextAligned(p,
                        pageSize.getWidth() / 7,
                        pageSize.getHeight() / 7,
                        pdfDoc.getPageNumber(page),
                        TextAlignment.CENTER, VerticalAlignment.MIDDLE,
                        (float) Math.PI * (PrePdfWatermark.ROTATE / 180.0f));
                canvas.restoreState();
            }
        } catch (FileNotFoundException e) {
            logger.error(myUUID, "添加水印 源文件找不到e:{} SourceFilePath:{}", e, sourceFilePath);
        } catch (IOException e) {
            logger.error(myUUID, "添加水印 IO异常", e);
        }

        logger.info(myUUID, "添加水印pdf结束");

    }

    private Paragraph constructWatermarkParagraph(MyUUID myUUID, Rectangle pageSize, WatermarkParam watermarkParam)
            throws IOException {
        PdfFont font = PdfFontFactory.createFont(PrePdfWatermark.FONT, PdfEncodings.IDENTITY_H, true);

        //准备水印，获取文件所有水印的样式
//        String watermarkContent = watermarkParam.getWatermarkContent();
//        int repeatY = PrePdfWatermark.REPEAT_Y;
//        int repeatX = PrePdfWatermark.REPEAT_X;

        List<String> watermarkContentList = watermarkParam.getWatermarkContent();
        String watermarkContent = getWatermarkContent(myUUID, watermarkParam.getFontSize(), pageSize, font, watermarkContentList, 10, watermarkParam.getLineDistance(),10);

//        WatermarkContentParam watermarkContentParam = getWatermarkContentAll(repeatX, repeatY, watermarkContentList);
//        String watermarkLine = watermarkContentParam.getWatermarkLine();
//        String watermarkAll = watermarkContentParam.getWatermarkAll();
//        logger.info(myUUID, "准备水印，watermarkContentParam:{}", watermarkContentParam.toString());

        String fontColor = watermarkParam.getFontColor();
        int[] colorInts = ColorUtil.hex2RGB(fontColor);
        Color color = new DeviceRgb(colorInts[0], colorInts[1], colorInts[2]);

        int fontSize = watermarkParam.getFontSize();

        logger.info(myUUID, "准备水印，fontColor:{} colorInts:{} fontSize:{} ",fontColor, JSON.toJSONString(colorInts),fontSize);

        //设置水印字体
        Paragraph p = new Paragraph(watermarkContent)
                .setMultipliedLeading(PrePdfWatermark.MULTIPLIED_LEADING)
                .setFont(font)
                .setFontSize(watermarkParam.getFontSize())
                .setFontColor(color);

        return p;
    }


    /**
     * 构造pdf水印参数类PrePdfWatermark
     *
     * @param myUUID
     * @param sourceFile
     * @param pdfWatermarkFile
     * @param watermarkParam
     * @return
     */
    public PrePdfWatermark genPrePdfWatermark(MyUUID myUUID, File sourceFile, File pdfWatermarkFile, WatermarkParam watermarkParam) {

        PrePdfWatermark prePdfWatermark = new PrePdfWatermark();
        prePdfWatermark.setSourceFile(sourceFile);
        prePdfWatermark.setTargetFile(pdfWatermarkFile);

        WatermarkInfo watermarkInfo = new WatermarkInfo();
//                WatermarkInfo watermarkInfo = prePdfWatermark.getWatermarkInfo();
        //设置自定义水印内容
        //PrePdfWatermark.getWatermarkParagraph(int, int)中会通过watermarkInfo.getWatermark()获取水印的样式
        watermarkInfo.setCustom(true);
//        watermarkInfo.setContent(watermarkParam.getWatermarkContent());
        prePdfWatermark.setWatermarkInfo(watermarkInfo);
        logger.info(myUUID, " prePdfWatermark:{}", prePdfWatermark.toString());
        return prePdfWatermark;
    }


    public static WatermarkContentParam getWatermarkContentAll(int repeatX, int repeatY, List<String> contentList) {
        StringBuilder builder = new StringBuilder();

        //添加水印内容
        int max_len = 0;
        for (String content:contentList) {
            if(content.length() > max_len){
                max_len = content.length();
            }
        }

        String watermarkLine = "";
        String watermarkAll = "";
        if (!CollectionUtils.isEmpty(contentList)) {
            for (int i = 0; i < repeatY; i++) {
                for (String content : contentList) {
                    //长度不够的补齐空格
                    if (content.length() < max_len) {
                        int addLen = max_len - content.length();
                        String repeatStr = Strings.repeat(" ", addLen);
                        content = content+repeatStr;
                    }

                    String watermarkString = new StringBuilder().append(content).append(PrePdfWatermark.DELIMITER_X).toString();
                    watermarkLine = Strings.repeat(watermarkString, repeatX);

                    builder.append(Strings.repeat("\u00a0", (repeatY - i - 1) * 10)).append(watermarkLine).append("\n");
                }
                builder.append(Strings.repeat("\u00a0", (repeatY - i - 1) * 10)).append("\n");
            }
            watermarkAll = builder.toString();
        }

        return WatermarkContentParam.builder()
                .watermarkLine(watermarkLine)
                .watermarkAll(watermarkAll)
                .build();
    }


    public String getWatermarkContent(MyUUID myUUID, int fontSize, Rectangle pageSize, PdfFont font, List<String> contentList, int watermarkLineOffset, int watermarkLineDistance, int watermarkLineRepeat) {
        StringBuilder builder = new StringBuilder();

        String maxContent = "";
        int max = 0;
        for (String content : contentList) {

            if (content.length() > max) {
                maxContent = content;
                max = content.length();
            }
        }

        float pageWidth = pageSize.getWidth();
        float pageHeight = pageSize.getHeight();

        //文字宽度
        float width = font.getWidth(maxContent, fontSize);
        //文字高度
        float height = fontSize;

        //勾股定理对角线长度
//        int diagonal = (int) Math.sqrt(pageWidth * pageWidth + pageHeight * pageHeight);

        int repeatX = (int) Math.ceil(pageWidth / width);
        int repeatY = (int) Math.ceil(pageHeight / height);

        if (!CollectionUtils.isEmpty(contentList)) {
            for (int i = 0; i < repeatY; i++) {
                for (String content : contentList) {
                    //长度不足的补齐空格
                    int addSpace = maxContent.length()-content.length();
                    String watermarkString = new StringBuilder().append(content).append(Strings.repeat(PrePdfWatermark.DELIMITER_X, 10)).toString();
                    String watermarkLine = Strings.repeat(watermarkString+Strings.repeat(" ",addSpace), repeatX);

                    builder.append(Strings.repeat("\u00a0", (repeatY - i - 1) * watermarkLineOffset)).append(watermarkLine).append("\n");
                }
                builder.append(Strings.repeat("\n", watermarkLineDistance));
            }
//            logger.info(myUUID, "水印完整信息:[{}]", builder.toString());
            return builder.toString();
        }
        return "";
    }



}
