package com.egeio.services.transformer.constant;

public enum Errors {

    // 业务
    OK("OK", "OK"),
    SYSTEM_ERROR("system_error", "系统异常，请稍后再试！"),
//    SERVLET_PART_ERROR("servlet_part_error", "servlet part异常"),
//    FILE_STREAM_CONVERT_ERROR("file_stream_convert_error", "文件流转换异常"),
//    FILE_WRITE_ERROR("file_write_error", "文件写入异常"),
//    FILE_CLOSE_ERROR("file_close_error", "文件关闭异常"),
    FILE_TYPE_NOT_SUPPORT("file_type_not_support", "文件类型不支持"),
    FILE_CONVERT_ERROR("file_type_not_support", "文件转换失败"),
//    TEXT_CONVERT_HTML_ERROR("text_convert_html_error", "text转换成html失败"),
//    HTML_CONVERT_PDF_ERROR("html_convert_pdf_error", "html转换成pdf失败"),

    ;

    private final String code;
    private final String message;

    Errors(String code, String message) {
        this.code = code;
        this.message = message;
    }

    public String getCode() {
        return code;
    }

    public String getMessage() {
        return message;
    }

}

