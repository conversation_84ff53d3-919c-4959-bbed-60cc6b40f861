package com.egeio.services.transformer;

import akka.actor.ActorSystem;
import com.egeio.core.ActorSystemFactory;
import com.egeio.core.config.Config;
import com.egeio.core.jetty.JettyServer;
import com.egeio.core.jetty.monitor.Monitor;
import com.egeio.core.mq.RabbitMqPool;
import com.egeio.core.utils.MemcacheUtils;
import com.egeio.daemon.DaemonLifecycleAdapter;
import com.egeio.services.bifrost.servlets.NewJobServlet;
import com.egeio.services.transformer.servlet.DownloadWatermarkServlet;
import com.egeio.services.transformer.utils.TransformerConstants;
import org.eclipse.jetty.server.Handler;
import org.eclipse.jetty.servlet.ServletContextHandler;
import org.springframework.context.support.AbstractApplicationContext;
import org.springframework.context.support.ClassPathXmlApplicationContext;

import javax.servlet.http.HttpServlet;
import java.io.IOException;
import java.util.HashMap;
import java.util.Map;

public class TransformerLifecycleAdapter extends DaemonLifecycleAdapter {
    private ActorSystem sys;
    private JettyServer server;
    public AbstractApplicationContext context;

    private void configServlets(JettyServer server) {
        ServletContextHandler context = server.createContext("/", ".");
        Map<String, Class<? extends HttpServlet>> servlets = new HashMap<String, Class<? extends HttpServlet>>();
        servlets.put("/" + TransformerConstants.NEW_JOB_PREFIX + "/*",
                NewJobServlet.class);


        //设置下载水印接口url
        servlets.put("/" + TransformerConstants.DOWNLOAD_WATERMARK_CONVERT + "/*",
                DownloadWatermarkServlet.class);

        server.addServlets(context, servlets, true);

        server.setHandlers(new Handler[] { context });
    }

    @Override
    public void doStart() throws Exception {
        startSpringContext();

        server = new JettyServer();
        configServlets(server);
        server.start();

        sys = ActorSystemFactory.createJobWorkerSystem(
                Config.getConfig().getServiceName("TransformerService"));
    }

    @Override
    public void doStop() throws Exception {
        if (sys != null) {
            sys.shutdown();
        }

        if (server != null) {
            server.stop();
        }

        RabbitMqPool.shutdown();

        stopSpringContext();
    }

    private void startSpringContext() {
        System.setProperty("dubbo.application.logger", "slf4j");
        context = new ClassPathXmlApplicationContext(new String[]{ "spring-config.xml" });
        context.start();
    }

    private void stopSpringContext() {
        context.stop();
    }
}
