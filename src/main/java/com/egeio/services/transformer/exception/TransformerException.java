package com.egeio.services.transformer.exception;

import com.egeio.services.transformer.constant.Errors;

import java.text.MessageFormat;

/**
 * <AUTHOR>
 * @date 2022/3/28 下午4:10
 */
public class TransformerException extends BaseException {


    public TransformerException() {
        super();
    }

    public TransformerException(Errors error) {
        super(error);
    }

    public TransformerException(Errors error, Throwable cause) {
        super(error, cause);
    }

    /**
     * 基本方式
     */
    public TransformerException(String code, String msg) {
        super(code, msg);
    }

    /**
     * 占位符方式
     */
    public TransformerException(String code, String msgFormat, Object... replacement) {
        super(code, MessageFormat.format(msgFormat, replacement));
    }
}
