package com.egeio.services.transformer.exception;

import com.egeio.services.transformer.constant.Errors;

import java.io.Serializable;
import java.text.MessageFormat;

public abstract class BaseException extends RuntimeException implements Serializable {

    private String code;
    private String message;
    private Errors error;

    public BaseException() {
        super();
    }

    /**
     * 设置异常信息
     */
    public BaseException(Errors error) {
        super(error.toString());
        this.error = error;
        this.code = error.getCode();
        this.message = error.getMessage();
    }

    /**
     * 基本方式
     */
    public BaseException(String code, String msg) {
        super(msg);
        this.code = code;
        this.message = msg;
    }

    /**
     * 占位符方式
     */
    public BaseException(String code, String msgFormat, Object... replacement) {
        this(code, MessageFormat.format(msgFormat, replacement));
    }


    /**
     * 设置异常信息和原始堆栈
     */
    public BaseException(Errors error, Throwable cause) {
        super(error.toString(), cause);
        this.error = error;
        this.code = error.getCode();
        this.message = error.getMessage();
    }


    public String getReturnErrorStackTrace() {
        Throwable cause = getCause();
        if (cause != null) {
            StringBuilder builder = new StringBuilder();
            if (message != null) {
                builder.append(message).append("; ");
            }
            builder.append("Nested Exception is ").append(cause);
            return builder.toString();
        } else {
            return message;
        }

    }

    public String getCode() {
        return code;
    }

    public String getMessage() {
        return message;
    }

    public Errors getError() {
        return error;
    }
}
