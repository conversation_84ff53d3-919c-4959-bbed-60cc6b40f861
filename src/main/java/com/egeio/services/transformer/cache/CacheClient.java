package com.egeio.services.transformer.cache;

import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @since 2.0.0
 */
public interface CacheClient {

    /**
     * 从缓存中查询数据
     *
     * @param key 缓存的key
     * @return 返回值都是string
     */
    Object get(String key);

    /**
     * 设置缓存的值
     *
     * @param key
     * @param value
     * @param timeout
     * @param unit
     */
    void set(String key, Object value, long timeout, TimeUnit unit);

    /**
     * 删除缓存的值
     *
     * @param key
     */
    void delete(String key);

}
