package com.egeio.services.transformer.cache;

import com.egeio.core.log.Logger;
import com.egeio.core.log.LoggerFactory;
import com.egeio.core.log.MyUUID;
import com.egeio.core.utils.MemcacheUtils;
import com.whalin.MemCached.MemCachedClient;
import org.springframework.data.redis.core.StringRedisTemplate;

import java.util.Date;
import java.util.concurrent.TimeUnit;

/**
 * 为了不停机替换memcached，开启双写，redis 作为主节点，用剔除 memcached 做铺垫
 * 之所以要继续写memcached的数据，是为了回滚发布考虑
 * <p>
 * 此后替换该bean为 RedisCacheClient
 *
 * <AUTHOR>
 * @since 2.0.0
 */
public class DoubleWriteRedisAsMasterCacheClient implements CacheClient {

    private static Logger logger = LoggerFactory.getLogger(DoubleWriteRedisAsMasterCacheClient.class);

    public DoubleWriteRedisAsMasterCacheClient(StringRedisTemplate stringRedisTemplate) {
        this.stringRedisTemplate = stringRedisTemplate;

        // 还是原先的初始化方法
        this.memCachedClient = MemcacheUtils.createMemcacheClient();
    }

    /**
     * 新的cache
     */
    private StringRedisTemplate stringRedisTemplate;

    /**
     * 老的cache
     */
    private MemCachedClient memCachedClient;

    @Override
    public Object get(String key) {
        return memCachedClient.get(key);
    }

    @Override
    public void set(String key, Object value, long timeout, TimeUnit unit) {
        stringRedisTemplate.opsForValue().set(key, value.toString(), timeout, unit);

        try {
            // 旧缓存设置
            memCachedClient.set(key, value, new Date(System.currentTimeMillis() + unit.toMillis(timeout)));
        } catch (Exception exception) {
            // 捕获异常
            logger.warn(new MyUUID(), "memcached write value exception, key:" + key, exception);
        }

    }

    @Override
    public void delete(String key) {
        stringRedisTemplate.delete(key);

        try {
            memCachedClient.delete(key);
        } catch (Exception exception) {
            // 捕获异常
            logger.warn(new MyUUID(), "memcached delete key exception, key:" + key, exception);
        }
    }

}
