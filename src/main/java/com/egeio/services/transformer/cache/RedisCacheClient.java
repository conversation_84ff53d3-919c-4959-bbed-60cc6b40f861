package com.egeio.services.transformer.cache;

import org.springframework.data.redis.core.StringRedisTemplate;

import java.util.concurrent.TimeUnit;

/**
 * 使用新缓存
 *
 * <AUTHOR>
 * @since 2.0.0
 */
public class RedisCacheClient implements CacheClient {

    public RedisCacheClient(StringRedisTemplate stringRedisTemplate) {
        this.stringRedisTemplate = stringRedisTemplate;
    }

    /**
     * 新的cache
     */
    private StringRedisTemplate stringRedisTemplate;

    @Override
    public Object get(String key) {
        return stringRedisTemplate.opsForValue().get(key);
    }

    @Override
    public void set(String key, Object value, long timeout, TimeUnit unit) {
        stringRedisTemplate.opsForValue().set(key, value.toString(), timeout, unit);
    }

    @Override
    public void delete(String key) {
        stringRedisTemplate.delete(key);
    }

}
