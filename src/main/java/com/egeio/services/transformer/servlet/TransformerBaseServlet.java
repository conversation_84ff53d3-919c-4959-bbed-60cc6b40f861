package com.egeio.services.transformer.servlet;

import com.egeio.core.jetty.servlet.BaseServlet;
import com.egeio.core.log.Logger;
import com.egeio.core.log.LoggerFactory;
import com.egeio.core.log.MyUUID;
import com.egeio.core.utils.Utils;
import org.eclipse.jetty.server.Request;

import javax.servlet.MultipartConfigElement;
import javax.servlet.ServletException;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.servlet.http.Part;
import java.io.IOException;

/**
 * <AUTHOR>
 * @date 2022/3/25 下午3:02
 */
public abstract class TransformerBaseServlet extends BaseServlet {

    private static Logger logger = LoggerFactory.getLogger(TransformerBaseServlet.class);

    private Part filePart;

    private static final String MULTI_PART_LOCATION = "/var/tmp/part_file";

    private static final MultipartConfigElement MULTI_PART_CONFIG = new MultipartConfigElement(MULTI_PART_LOCATION);

    /**
     * 业务逻辑前的准备工作
     * @param req
     * @param resp
     */
    protected void begin(HttpServletRequest req, HttpServletResponse resp){
        MyUUID uuid = new MyUUID(Utils.getOriginIp(req));
        String contentType = req.getContentType();

        if(contentType != null && contentType.startsWith("multipart/")){
            req.setAttribute(Request.__MULTIPART_CONFIG_ELEMENT, MULTI_PART_CONFIG);
            Part file = null;
            try {
                file = req.getPart("file");
            } catch (IOException e) {
                logger.error(uuid, "IOException ",e);
            } catch (ServletException e) {
                logger.error(uuid, "ServletException ",e);
            }
            this.filePart = file;
        }


        doRealWork(uuid, req, resp);

    }

    protected abstract void doRealWork(MyUUID uuid, HttpServletRequest req,
                                       HttpServletResponse resp) ;


    public Part getFilePart() {
        return filePart;
    }
}
