package com.egeio.services.transformer.servlet;

import com.alibaba.fastjson.JSON;
import com.egeio.core.log.Logger;
import com.egeio.core.log.LoggerFactory;
import com.egeio.core.log.MyUUID;
import com.egeio.services.transformer.constant.Errors;
import com.egeio.services.transformer.exception.TransformerException;
import com.egeio.services.transformer.models.watermark.WatermarkParam;
import com.egeio.services.transformer.service.FileConvertService;

import javax.servlet.annotation.MultipartConfig;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.servlet.http.Part;
import java.io.IOException;
import java.io.InputStream;
import java.io.UnsupportedEncodingException;
import java.net.URLDecoder;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2022/3/18 下午3:31
 */
@MultipartConfig
public class DownloadWatermarkServlet extends TransformerBaseServlet {

    private static Logger logger = LoggerFactory.getLogger(DownloadWatermarkServlet.class);

    /**
     * 处理post请求，做业务逻辑
     *
     * @param request
     * @param response
     */
    @Override
    protected void doPost(HttpServletRequest request, HttpServletResponse response) {


        begin(request, response);

    }

    @Override
    protected void doRealWork(MyUUID myUUID, HttpServletRequest request, HttpServletResponse response) {

        Part filePart = getFilePart();
        if (Objects.isNull(filePart)) {
            logger.error(myUUID, "获取filePart异常");
            throw new TransformerException(Errors.SYSTEM_ERROR);
        }

        InputStream inputStream = null;
        try {
            inputStream = filePart.getInputStream();
        } catch (IOException e) {
            logger.error(myUUID, "获取part inputStream异常", e);
            throw new TransformerException(Errors.SYSTEM_ERROR);
        }

        String paramsJson = request.getHeader("paramsJson");
        try {
            paramsJson= URLDecoder.decode(paramsJson, "UTF-8");
        } catch (UnsupportedEncodingException e) {
            logger.error(myUUID, "paramsJson解析异常", e);
            throw new TransformerException(Errors.SYSTEM_ERROR);
        }
        WatermarkParam watermarkParam = JSON.parseObject(paramsJson, WatermarkParam.class);
        logger.info(myUUID, "开始下载水印转换 watermarkParam:{} paramsJson:{}", watermarkParam.toString(),paramsJson);
        watermarkParam.genWatermarkParam();


        FileConvertService fileConvertServiceNew = new FileConvertService();
        fileConvertServiceNew.FileConvertStream(myUUID, inputStream, response, watermarkParam);


    }

}
