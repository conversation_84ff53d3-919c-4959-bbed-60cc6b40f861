package com.egeio.services.transformer.models;

import com.google.gson.Gson;
import com.google.gson.GsonBuilder;
import org.junit.Test;
import org.springframework.http.converter.json.GsonBuilderUtils;

import java.util.ArrayList;
import java.util.List;

import static org.junit.Assert.*;

public class WatermarkInfoTest {

    private WatermarkInfo getWatermarkInfo(String json) {

        Gson gson = new Gson();
        WatermarkInfo watermarkInfo = gson.fromJson(json, WatermarkInfo.class);
        return watermarkInfo;
    }

    @Test
    public void getWatermark() {
        List<String> jsons = new ArrayList<String>();
        jsons.add( "{\"user_id\":1073796,\"user_name\":\"yfy9732 lo\",\"login\":null}");

        WatermarkInfo newWater = new WatermarkInfo();
        newWater.setLogin(null);
        newWater.setUserId(0);
        newWater.setUserName(null);
        newWater.setContent("hello");
        newWater.setUniqueId("1073796Hello");
        newWater.setCustom(true);
        Gson gson = new Gson();
        jsons.add(gson.toJson(newWater));

        for(String json : jsons){
            WatermarkInfo watermarkInfo = getWatermarkInfo(json);
            System.out.println(watermarkInfo.getTargetFileUniqueId()+": "+watermarkInfo.getWatermark());


        }
    }
}