package com.egeio.services.transformer.fileio.middle_preview;

import com.egeio.core.utils.Assert;
import com.egeio.services.transformer.fileio.middle_preview.model.BizCallbackConfig;
import com.egeio.services.transformer.fileio.middle_preview.model.LoadFileQueryDto;
import com.egeio.services.transformer.models.ConversionJob;
import com.google.gson.Gson;
import com.google.gson.JsonSyntaxException;
import com.ning.http.client.FluentCaseInsensitiveStringsMap;
import com.ning.http.client.FluentStringsMap;
import org.junit.Test;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class GatewayLoadFileImplTest {

    private LoadFileQueryDto prepareQueryDTO(ConversionJob job) throws Exception {
        Map<String, Object> additionalInfo = job.getAdditionalInfos();
        Object queryDto = additionalInfo.get(LoadFileQueryDto.AdditionalInfo_Key_LOADFILEQUERYDTO);
        Gson gson = new Gson();
        String queryDtoStr = gson.toJson(queryDto);
        try {
            // 序列化反序列化, 创建新对象, 保证线程安全
            LoadFileQueryDto newQueryDto = gson.fromJson(queryDtoStr, LoadFileQueryDto.class);
            return newQueryDto;
        } catch (JsonSyntaxException e) {
            throw new Exception("Filed " + LoadFileQueryDto.AdditionalInfo_Key_LOADFILEQUERYDTO + " in job additionalInfo is not the instance of LoadFileQueryDto", e);
        }
    }

    private LoadFileQueryDto prepareLoadFileQueryDto() {
        LoadFileQueryDto loadFileQueryDto = new LoadFileQueryDto();
        BizCallbackConfig config = new BizCallbackConfig();
        config.setDownloadUrl("/download");
        config.setUploadUrl("/upload");
        config.setHost("http://preview-svc");
        config.setServiceId(123L);

        loadFileQueryDto.setPost(true);
        HashMap<String, List<String>> headerList = new HashMap<String, List<String>>();
        List<String> list = new ArrayList<>();
        list.add("test123");
        list.add("test345");
        headerList.put("X-Upload-Info-test", list);
        loadFileQueryDto.setHeaders(headerList);
        loadFileQueryDto.setBody("{json,download body}");
        FluentStringsMap postParams = new FluentStringsMap();
        postParams.add("key1", "value1");
        loadFileQueryDto.setPostParams(postParams);

        return loadFileQueryDto;
    }

    @Test
    public void prepareQueryDTO() throws Exception {
        String conversionJobStr = "{\"file_storage_id\":\"203000141478\",\"user_id\":\"777936\",\"extension\":\"pptx\",\"unique_name\":\"b1550cd81af3a69390e8578ec6a6c22e\",\"need_encrypt\":true,\"convert_kinds\":{\"pdf_enc\":{\"content_type\":\"application\\/pdf\",\"type\":\"preview\",\"file_name_includes_kind\":true,\"format\":\"pdf\",\"password\":\"uxwzD)qL'E\\\"lfzD-\",\"owner_password\":\"1J,OoK<@-E*l~$%A\",\"task_id\":\"57c6ffe44efc699d00b5362905773aeb\"}},\"force_convert\":false,\"target_platform\":1,\"version\":2,\"statistic_info\":{\"client_name\":\"web\",\"extension\":\"pptx\",\"size\":33111,\"enterprise_id\":\"11012\",\"storage_id\":2,\"is_private_preview\":false,\"is_watermark\":false,\"platform\":1,\"target_format\":\"pdf\"}}";
        String conversionJobStr2 = "{\"jobType\":\"file_conversion_job\",\"file_storage_id\":\"74000001640\",\"user_id\":\"1073796\",\"extension\":\"txt\",\"unique_name\":\"d58f362f72682f45b4cd1ba35d65f174\",\"need_encrypt\":true,\"convert_kinds\":{\"pdf_watermark_enc\":{\"content_type\":\"application\\/pdf\",\"type\":\"watermark_preview\",\"file_name_includes_kind\":true,\"format\":\"pdf\",\"watermark\":{\"user_id\":1073796,\"user_name\":\"yfy9732 lo\",\"login\":null},\"password\":\"RJ}~\\\"w40@63#{!8}\",\"owner_password\":\"GemL{SyP5W}@zSt9\",\"task_id\":\"1689c0c79a0cd2c18eebac84ca29c3de\"}},\"global_meta_info\":{\"watermark\":{\"user_id\":1073796,\"user_name\":\"yfy9732 lo\",\"login\":null}},\"force_convert\":false,\"target_platform\":2,\"version\":2,\"statistic_info\":{\"client_name\":\"web\",\"extension\":\"txt\",\"size\":317,\"enterprise_id\":\"15365\",\"storage_id\":2,\"is_private_preview\":false,\"is_watermark\":true,\"platform\":2,\"target_format\":\"pdf\"},\"__body_meta\":{\"new_job_url\":\"http:\\/\\/bifrost-svc:10080\\/transformer\\/new_job\",\"service_id\":261,\"http_meta\":{\"X-Convert-Type\":\"conversion\",\"X-Convert-Access\":\"public\"}},\"timestamp\":1607826770}";
        // excel linux
        String conversionJobStr3 = "{\"file_storage_id\":\"74000001626\",\"user_id\":\"1073796\",\"extension\":\"xls\",\"unique_name\":\"5c4494fcabc0359f8696271fb366473d\",\"need_encrypt\":true,\"convert_kinds\":{\"pdf_enc\":{\"content_type\":\"application\\/pdf\",\"type\":\"preview\",\"file_name_includes_kind\":true,\"format\":\"pdf\",\"password\":\"mc,LF=NQ!2hWMD(x\",\"owner_password\":\"7!a-(C}?UCJ9[9dw\",\"task_id\":\"c8a007c962f557fed39e99f6a7802312\"}},\"force_convert\":false,\"target_platform\":2,\"version\":2,\"statistic_info\":{\"client_name\":\"web\",\"extension\":\"xls\",\"size\":18944,\"enterprise_id\":\"15365\",\"storage_id\":2,\"is_private_preview\":false,\"is_watermark\":false,\"platform\":2,\"target_format\":\"pdf\"}}";
        // excel win
        String conversionJobStr4 = "{\"file_storage_id\":\"74000001626\",\"user_id\":\"1073796\",\"extension\":\"xls\",\"unique_name\":\"5c4494fcabc0359f8696271fb366473d\",\"need_encrypt\":true,\"convert_kinds\":{\"pdf_enc\":{\"content_type\":\"application\\/pdf\",\"type\":\"preview\",\"file_name_includes_kind\":true,\"format\":\"pdf\",\"password\":\"mc,LF=NQ!2hWMD(x\",\"owner_password\":\"7!a-(C}?UCJ9[9dw\",\"task_id\":\"6728afd4faf5048a607b2aa1aa870689\"}},\"force_convert\":true,\"target_platform\":1,\"version\":2,\"statistic_info\":{\"client_name\":\"web\",\"extension\":\"xls\",\"size\":18944,\"enterprise_id\":\"15365\",\"storage_id\":2,\"is_private_preview\":false,\"is_watermark\":false,\"platform\":1,\"target_format\":\"pdf\"}}";
        prepareQueryDTO(conversionJobStr);
        prepareQueryDTO(conversionJobStr2);
        prepareQueryDTO(conversionJobStr3);
        prepareQueryDTO(conversionJobStr4);

    }

    private void prepareQueryDTO(String conversionJobStr) throws Exception {
        Gson gson = new Gson();
        ConversionJob conversionJob = gson.fromJson(conversionJobStr, ConversionJob.class);
        LoadFileQueryDto downloadFileQueryDto = prepareLoadFileQueryDto();
        Map<String, Object> additionalInfos = new HashMap<>();
        additionalInfos.put(LoadFileQueryDto.AdditionalInfo_Key_LOADFILEQUERYDTO, downloadFileQueryDto);
        conversionJob.setAdditionalInfos(additionalInfos);

        String downloadGatewayConversionJobStr = gson.toJson(conversionJob);
        System.out.println("job example: " + downloadGatewayConversionJobStr);
        ConversionJob downloadGatewayConversionJob = gson.fromJson(downloadGatewayConversionJobStr, ConversionJob.class);
        LoadFileQueryDto dto = prepareQueryDTO(downloadGatewayConversionJob);
        Assert.notNull(dto);
        Assert.isTrue(downloadFileQueryDto != dto);
        Assert.isTrue(downloadFileQueryDto.getBody().equals(dto.getBody()));
    }

    @Test
    public void gsonTest() {
        Gson gson = new Gson();


        FluentStringsMap postParams = new FluentStringsMap();
        postParams.add("key1", "value1");

        String str2 = gson.toJson(postParams);
        FluentStringsMap postP = gson.fromJson(str2, FluentStringsMap.class);
        Assert.notNull(postP);

        Map<String, List<String>> headerList = new HashMap<String, List<String>>();
        List<String> list = new ArrayList<>();
        list.add("test123");
        list.add("test345");
        //headerList.put("X-Upload-Info-test", list);
        // FluentCaseInsensitiveStringsMap反序列化失败, duplicate key
        FluentCaseInsensitiveStringsMap headers = new FluentCaseInsensitiveStringsMap();
        //headers.add("X-Upload-Info-test",list);
        headers.add("key", "value");
        headers.add("key", "v2");
        String str = gson.toJson(headers);
        System.out.println("Str: " + str);
        HashMap<String, List<String>> header = gson.fromJson(str, HashMap.class);
        FluentCaseInsensitiveStringsMap nettyHeader = new FluentCaseInsensitiveStringsMap();
        for (String key : header.keySet()
        ) {

            nettyHeader.add(key, header.get(key));
        }
        System.out.println(gson.toJson(nettyHeader));

        Assert.notNull(nettyHeader);

    }
}